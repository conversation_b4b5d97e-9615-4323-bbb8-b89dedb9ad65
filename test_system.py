#!/usr/bin/env python3
"""
Test System Script
This script tests the vehicle diagnostic system functionality.
"""

import os
import sys
import logging
from controller import VehicleDiagnosticController

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("system_test.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("test_system")

def test_vehicle_database(controller):
    """Test vehicle database functionality"""
    logger.info("Testing vehicle database...")
    
    try:
        # Test getting vehicle makes
        makes = controller.get_vehicle_makes()
        logger.info(f"Found {len(makes)} vehicle makes: {makes[:5]}...")
        
        if makes:
            # Test getting models for first make
            first_make = makes[0]
            models = controller.get_vehicle_models(first_make)
            logger.info(f"Found {len(models)} models for {first_make}: {models[:3]}...")
            
            if models:
                # Test getting years for first model
                first_model = models[0]
                years = controller.get_vehicle_years(first_make, first_model)
                logger.info(f"Found {len(years)} years for {first_make} {first_model}: {years[:3]}...")
                
                if years:
                    # Test getting variants
                    first_year = years[0]
                    variants = controller.get_vehicle_variants(first_make, first_model, first_year)
                    logger.info(f"Found {len(variants)} variants for {first_make} {first_model} {first_year}: {variants}")
                    
                    # Test setting current vehicle
                    variant = variants[0] if variants else None
                    success = controller.set_current_vehicle(first_make, first_model, first_year, variant)
                    if success:
                        logger.info(f"Successfully set current vehicle to {first_make} {first_model} {first_year} {variant}")
                        logger.info(f"Current vehicle info: {controller.current_vehicle}")
                    else:
                        logger.warning("Failed to set current vehicle")
        
        return True
    except Exception as e:
        logger.error(f"Error testing vehicle database: {e}")
        return False

def test_dtc_database(controller):
    """Test DTC database functionality"""
    logger.info("Testing DTC database...")
    
    try:
        # Test getting DTC categories
        categories = controller.get_dtc_categories()
        logger.info(f"Found {len(categories)} DTC categories")
        
        if categories:
            # Test getting codes by category
            first_category = categories[0]
            category_prefix = first_category.get("code_prefix", "P0")
            codes = controller.get_dtc_codes_by_category(category_prefix)
            logger.info(f"Found {len(codes)} codes for category {category_prefix}")
            
            if codes:
                # Test getting DTC info
                first_code = codes[0]["code"]
                dtc_info = controller.get_dtc_info(first_code)
                if dtc_info:
                    logger.info(f"DTC info for {first_code}: {dtc_info['description']}")
                else:
                    logger.warning(f"No info found for DTC {first_code}")
        
        # Test searching DTC codes
        search_results = controller.search_dtc_codes("P0100")
        logger.info(f"Search for 'P0100' returned {len(search_results)} results")
        
        # Test getting DTC statistics
        stats = controller.get_dtc_statistics()
        logger.info(f"DTC database statistics: {stats}")
        
        return True
    except Exception as e:
        logger.error(f"Error testing DTC database: {e}")
        return False

def test_obd_interface(controller):
    """Test OBD interface functionality"""
    logger.info("Testing OBD interface...")
    
    try:
        # Test getting available ports
        ports = controller.get_available_ports()
        logger.info(f"Found {len(ports)} available ports")
        
        # Test protocol detection
        protocols = controller.protocol_handler.get_all_standard_protocols()
        logger.info(f"Available protocols: {protocols}")
        
        # Note: We can't test actual connection without hardware
        logger.info("OBD interface test completed (hardware connection not tested)")
        
        return True
    except Exception as e:
        logger.error(f"Error testing OBD interface: {e}")
        return False

def test_data_import_export(controller):
    """Test data import/export functionality"""
    logger.info("Testing data import/export...")
    
    try:
        # Test importing vehicle data
        if os.path.exists("database/vehicle_data/bmw_data.json"):
            count = controller.import_vehicle_data("database/vehicle_data/bmw_data.json")
            logger.info(f"Imported {count} vehicles from BMW data file")
        
        # Test importing DTC codes
        if os.path.exists("database/dtc_codes/sample_codes.json"):
            count = controller.import_dtc_codes("database/dtc_codes/sample_codes.json")
            logger.info(f"Imported {count} DTC codes from sample file")
        
        # Test exporting DTC codes
        export_path = "test_export_dtc.json"
        count = controller.export_dtc_codes(export_path)
        logger.info(f"Exported {count} DTC codes to {export_path}")
        
        # Clean up
        if os.path.exists(export_path):
            os.remove(export_path)
        
        return True
    except Exception as e:
        logger.error(f"Error testing data import/export: {e}")
        return False

def test_live_data_simulation(controller):
    """Test live data simulation"""
    logger.info("Testing live data simulation...")
    
    try:
        # Test getting live data for common PIDs
        pids = ["RPM", "SPEED", "COOLANT_TEMP", "ENGINE_LOAD"]
        
        for pid in pids:
            data = controller.get_live_data(pid)
            if data:
                logger.info(f"{pid}: {data['value']} {data['unit']}")
            else:
                logger.warning(f"No data for PID {pid}")
        
        # Test getting freeze frame data
        freeze_frame = controller.get_freeze_frame_data()
        logger.info(f"Freeze frame data contains {len(freeze_frame)} parameters")
        
        return True
    except Exception as e:
        logger.error(f"Error testing live data simulation: {e}")
        return False

def main():
    """Main test function"""
    logger.info("Starting system test...")
    
    try:
        # Initialize controller
        controller = VehicleDiagnosticController()
        
        # Run tests
        tests = [
            ("Vehicle Database", test_vehicle_database),
            ("DTC Database", test_dtc_database),
            ("OBD Interface", test_obd_interface),
            ("Data Import/Export", test_data_import_export),
            ("Live Data Simulation", test_live_data_simulation)
        ]
        
        results = {}
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"Running {test_name} test...")
            logger.info(f"{'='*50}")
            
            try:
                result = test_func(controller)
                results[test_name] = result
                status = "PASSED" if result else "FAILED"
                logger.info(f"{test_name} test: {status}")
            except Exception as e:
                logger.error(f"{test_name} test failed with exception: {e}")
                results[test_name] = False
        
        # Print summary
        logger.info(f"\n{'='*50}")
        logger.info("TEST SUMMARY")
        logger.info(f"{'='*50}")
        
        passed = 0
        total = len(tests)
        
        for test_name, result in results.items():
            status = "PASSED" if result else "FAILED"
            logger.info(f"{test_name}: {status}")
            if result:
                passed += 1
        
        logger.info(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            logger.info("All tests passed! System is working correctly.")
            return 0
        else:
            logger.warning(f"{total - passed} tests failed. Please check the logs.")
            return 1
            
    except Exception as e:
        logger.error(f"System test failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
