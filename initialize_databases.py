#!/usr/bin/env python3
"""
Initialize Databases Script
This script initializes the vehicle and DTC databases with sample data.
"""

import os
import sys
import logging
from database.vehicle_db import VehicleDatabase
from database.dtc_db import DTCDatabase
from database.vehicle_data_importer import VehicleDataImporter

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("database_init.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("initialize_databases")

def initialize_vehicle_database():
    """Initialize the vehicle database with sample data"""
    try:
        logger.info("Initializing vehicle database...")
        
        # Create database
        vehicle_db = VehicleDatabase("database/vehicles.db")
        
        # Create importer
        importer = VehicleDataImporter(vehicle_db, "database/vehicle_data")
        
        # Import sample data files
        data_files = [
            "database/vehicle_data/bmw_data.json",
            "database/vehicle_data/toyota_data.json",
            "database/vehicle_data/mercedes_data.json"
        ]
        
        total_count = 0
        for file_path in data_files:
            if os.path.exists(file_path):
                count = importer.import_from_json(file_path)
                total_count += count
                logger.info(f"Imported {count} vehicles from {file_path}")
            else:
                logger.warning(f"File not found: {file_path}")
        
        logger.info(f"Vehicle database initialized with {total_count} vehicles")
        return total_count
    except Exception as e:
        logger.error(f"Error initializing vehicle database: {e}")
        return 0

def initialize_dtc_database():
    """Initialize the DTC database with sample data"""
    try:
        logger.info("Initializing DTC database...")
        
        # Create database
        dtc_db = DTCDatabase("database/dtc.db")
        
        # Add sample DTC codes
        sample_codes = [
            {
                "code": "P0100",
                "description": "Mass or Volume Air Flow Circuit Malfunction",
                "category": "Powertrain",
                "severity": 3,
                "possible_causes": "Faulty MAF sensor, dirty air filter, vacuum leak",
                "solutions": "Clean or replace MAF sensor, replace air filter, check for vacuum leaks"
            },
            {
                "code": "P0101",
                "description": "Mass or Volume Air Flow Circuit Range/Performance Problem",
                "category": "Powertrain",
                "severity": 3,
                "possible_causes": "Dirty MAF sensor, faulty MAF sensor, air leak",
                "solutions": "Clean MAF sensor, replace MAF sensor if necessary, check for air leaks"
            },
            {
                "code": "P0171",
                "description": "System Too Lean (Bank 1)",
                "category": "Powertrain",
                "severity": 4,
                "possible_causes": "Vacuum leak, faulty fuel pump, dirty fuel injectors, faulty oxygen sensor",
                "solutions": "Check for vacuum leaks, test fuel pressure, clean fuel injectors, replace oxygen sensor"
            },
            {
                "code": "P0172",
                "description": "System Too Rich (Bank 1)",
                "category": "Powertrain",
                "severity": 4,
                "possible_causes": "Faulty fuel injectors, faulty fuel pressure regulator, faulty oxygen sensor",
                "solutions": "Test fuel injectors, replace fuel pressure regulator, replace oxygen sensor"
            },
            {
                "code": "P0300",
                "description": "Random/Multiple Cylinder Misfire Detected",
                "category": "Powertrain",
                "severity": 5,
                "possible_causes": "Faulty spark plugs, faulty ignition coils, fuel system problems, engine mechanical problems",
                "solutions": "Replace spark plugs, test ignition coils, check fuel system, perform compression test"
            },
            {
                "code": "P0420",
                "description": "Catalyst System Efficiency Below Threshold (Bank 1)",
                "category": "Powertrain",
                "severity": 3,
                "possible_causes": "Faulty catalytic converter, faulty oxygen sensors, engine misfires",
                "solutions": "Replace catalytic converter, replace oxygen sensors, fix engine misfires"
            },
            {
                "code": "P0442",
                "description": "Evaporative Emission Control System Leak Detected (small leak)",
                "category": "Powertrain",
                "severity": 2,
                "possible_causes": "Loose gas cap, faulty EVAP canister, damaged EVAP lines",
                "solutions": "Tighten gas cap, replace EVAP canister, repair EVAP lines"
            },
            {
                "code": "P0505",
                "description": "Idle Control System Malfunction",
                "category": "Powertrain",
                "severity": 3,
                "possible_causes": "Faulty idle air control valve, carbon buildup, vacuum leak",
                "solutions": "Clean or replace idle air control valve, clean throttle body, check for vacuum leaks"
            },
            {
                "code": "B0001",
                "description": "Driver Airbag Circuit Open",
                "category": "Body",
                "severity": 4,
                "possible_causes": "Faulty airbag, damaged wiring, faulty airbag control module",
                "solutions": "Check airbag wiring, replace airbag if necessary, replace airbag control module"
            },
            {
                "code": "C0035",
                "description": "Left Front Wheel Speed Sensor Circuit Malfunction",
                "category": "Chassis",
                "severity": 4,
                "possible_causes": "Faulty wheel speed sensor, damaged sensor wiring, dirty sensor",
                "solutions": "Clean wheel speed sensor, check sensor wiring, replace sensor if necessary"
            },
            {
                "code": "U0100",
                "description": "Lost Communication With ECM/PCM",
                "category": "Network",
                "severity": 5,
                "possible_causes": "Faulty ECM/PCM, damaged CAN bus wiring, faulty CAN bus termination",
                "solutions": "Check CAN bus wiring, test CAN bus termination, replace ECM/PCM if necessary"
            }
        ]
        
        # Add codes to database
        count = 0
        for code_data in sample_codes:
            # Find or create category
            category_id = dtc_db.get_or_create_category(
                code_data["category"], 
                code_data["code"][:2], 
                code_data["category"] + " related codes"
            )
            
            if category_id:
                # Add the code
                dtc_id = dtc_db.add_dtc_code(
                    code=code_data["code"],
                    description=code_data["description"],
                    category_id=category_id,
                    severity=code_data["severity"],
                    possible_causes=code_data["possible_causes"],
                    solutions=code_data["solutions"]
                )
                
                if dtc_id:
                    count += 1
        
        logger.info(f"DTC database initialized with {count} codes")
        return count
    except Exception as e:
        logger.error(f"Error initializing DTC database: {e}")
        return 0

def main():
    """Main function"""
    logger.info("Starting database initialization...")
    
    # Create database directories if they don't exist
    os.makedirs("database", exist_ok=True)
    os.makedirs("database/vehicle_data", exist_ok=True)
    os.makedirs("database/dtc_codes", exist_ok=True)
    
    # Initialize vehicle database
    vehicle_count = initialize_vehicle_database()
    
    # Initialize DTC database
    dtc_count = initialize_dtc_database()
    
    logger.info(f"Database initialization complete:")
    logger.info(f"  - Vehicle database: {vehicle_count} vehicles")
    logger.info(f"  - DTC database: {dtc_count} codes")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
