#!/usr/bin/env python3
"""
Hyundai/Kia Protocol Handler
This module provides the protocol handler for Hyundai and Kia-specific protocols.
"""

import logging
import time
import struct
from enum import Enum, auto
from typing import Dict, List, Optional, Any, Tuple, Union

from protocols.protocol_handler import BaseProtocol, ProtocolType, SecurityLevel, SecurityAccessStatus, ProtocolError, CommunicationError
from protocols.standard.iso15765 import ISO15765Protocol, CANFrameType
from protocols.standard.iso14230 import ISO14230Protocol

logger = logging.getLogger("protocol.Hyundai")

class HyundaiVariant(Enum):
    """Hyundai/Kia protocol variants"""
    KWP = "KWP"        # KWP2000 protocol (older vehicles)
    CAN_TP = "CAN-TP"  # CAN Transport Protocol (mid-age)
    UDS = "UDS"        # UDS over CAN (newer vehicles)

class HyundaiProtocol(BaseProtocol):
    """Hyundai/Kia protocol handler"""
    
    def __init__(self, interface=None, variant=HyundaiVariant.UDS, baudrate=500000):
        """
        Initialize the Hyundai/Kia protocol handler
        
        Args:
            interface: The hardware interface to use
            variant (HyundaiVariant): The protocol variant (default: UDS)
            baudrate (int): The CAN baudrate (default: 500000)
        """
        super().__init__(interface)
        
        if isinstance(variant, str):
            try:
                self.variant = HyundaiVariant(variant)
            except ValueError:
                self.variant = HyundaiVariant.UDS
                logger.warning(f"Invalid Hyundai protocol variant: {variant}, using UDS")
        else:
            self.variant = variant
        
        self.baudrate = baudrate
        
        # Initialize the appropriate protocol handler based on the variant
        if self.variant == HyundaiVariant.UDS:
            # UDS over ISO15765 (CAN)
            self.protocol = ISO15765Protocol(
                interface=interface,
                baudrate=baudrate,
                frame_type=CANFrameType.STANDARD,
                tx_id=0x7D0,  # Hyundai-specific diagnostic CAN ID
                rx_id=0x7D8   # Hyundai-specific response CAN ID
            )
        elif self.variant == HyundaiVariant.CAN_TP:
            # CAN-TP protocol
            self.protocol = ISO15765Protocol(
                interface=interface,
                baudrate=baudrate,
                frame_type=CANFrameType.STANDARD,
                tx_id=0x7D0,
                rx_id=0x7D8
            )
        elif self.variant == HyundaiVariant.KWP:
            # KWP2000 over K-Line
            self.protocol = ISO14230Protocol(
                interface=interface,
                baudrate=10400,  # Hyundai K-Line typically uses 10.4 kbps
                tx_id=0xF1,
                rx_id=0x10
            )
        
        # Hyundai/Kia-specific PIDs
        self.pid_vin = 0xF190
        self.pid_ecu_info = 0xF18A
        self.pid_software_version = 0xF189
        self.pid_calibration_id = 0xF187
        self.pid_immobilizer_status = 0xF18C
        self.pid_odometer = 0xF15B
        self.pid_tpms_data = 0xF1A1
        self.pid_key_programming = 0xF1A2
        
        # Hyundai/Kia-specific ECUs
        self.ecu_engine = 0x7D0
        self.ecu_transmission = 0x7D1
        self.ecu_abs = 0x7D2
        self.ecu_airbag = 0x7D3
        self.ecu_instrument_cluster = 0x7D4
        self.ecu_body_control = 0x7D5
        self.ecu_climate_control = 0x7D6
        self.ecu_immobilizer = 0x7D7
        self.ecu_smart_key = 0x7D9
        
        # Hyundai/Kia-specific security access seeds/keys
        self.security_algorithms = {
            SecurityLevel.LEVEL_1: self._calculate_key_level_1,
            SecurityLevel.LEVEL_2: self._calculate_key_level_2,
            SecurityLevel.LEVEL_3: self._calculate_key_level_3,
            SecurityLevel.LEVEL_4: self._calculate_key_level_4,
            SecurityLevel.LEVEL_5: self._calculate_key_level_5
        }
    
    def connect(self):
        """
        Connect to the vehicle
        
        Returns:
            bool: True if connected, False otherwise
        """
        try:
            # Connect using the underlying protocol
            if self.protocol.connect():
                self.connected = True
                logger.info(f"Connected to Hyundai/Kia vehicle using {self.variant.value} protocol")
                
                # Enter diagnostic session for Hyundai/Kia
                if self.variant == HyundaiVariant.UDS:
                    self.protocol.enter_diagnostic_session(self.protocol.session_extended_diagnostic)
                
                return True
            else:
                logger.error("Failed to connect to Hyundai/Kia vehicle")
                return False
        except Exception as e:
            logger.error(f"Error connecting to Hyundai/Kia vehicle: {e}")
            return False
    
    def disconnect(self):
        """
        Disconnect from the vehicle
        
        Returns:
            bool: True if disconnected, False otherwise
        """
        try:
            # Disconnect using the underlying protocol
            if self.protocol.disconnect():
                self.connected = False
                logger.info("Disconnected from Hyundai/Kia vehicle")
                return True
            else:
                logger.error("Failed to disconnect from Hyundai/Kia vehicle")
                return False
        except Exception as e:
            logger.error(f"Error disconnecting from Hyundai/Kia vehicle: {e}")
            return False
    
    def send_command(self, command, response_required=True):
        """
        Send a command to the vehicle
        
        Args:
            command: The command to send
            response_required (bool): Whether a response is required
            
        Returns:
            The response from the vehicle
        """
        try:
            # Send command using the underlying protocol
            return self.protocol.send_command(command, response_required)
        except Exception as e:
            logger.error(f"Error sending command to Hyundai/Kia vehicle: {e}")
            raise CommunicationError(f"Error sending command to Hyundai/Kia vehicle: {e}")
    
    def read_dtc(self):
        """
        Read diagnostic trouble codes
        
        Returns:
            list: List of DTCs
        """
        try:
            # Read DTCs using the underlying protocol
            dtcs = self.protocol.read_dtc()
            
            # Add Hyundai/Kia-specific information to DTCs
            for dtc in dtcs:
                # Add Hyundai/Kia-specific DTC information if available
                # This would typically come from a Hyundai/Kia-specific DTC database
                if dtc.get('code', '').startswith('P1'):
                    dtc['manufacturer'] = 'Hyundai/Kia'
                
                # Add additional Hyundai/Kia-specific information
                if dtc.get('code') == 'P1326':
                    dtc['description'] = 'Knock Sensor Detection System'
                    dtc['possible_causes'] = 'Engine bearing damage, Low oil pressure, Faulty knock sensor'
                elif dtc.get('code') == 'P1295':
                    dtc['description'] = 'Electronic Throttle Control System Malfunction'
                    dtc['possible_causes'] = 'Faulty throttle body, Throttle position sensor circuit issue'
                elif dtc.get('code') == 'P1610':
                    dtc['description'] = 'Immobilizer System Malfunction'
                    dtc['possible_causes'] = 'Key programming error, Immobilizer control unit failure'
            
            return dtcs
        except Exception as e:
            logger.error(f"Error reading DTCs from Hyundai/Kia vehicle: {e}")
            return []
    
    def clear_dtc(self):
        """
        Clear diagnostic trouble codes
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Clear DTCs using the underlying protocol
            return self.protocol.clear_dtc()
        except Exception as e:
            logger.error(f"Error clearing DTCs from Hyundai/Kia vehicle: {e}")
            return False
    
    def read_data(self, pid):
        """
        Read data from the vehicle
        
        Args:
            pid: The parameter ID to read
            
        Returns:
            The data read from the vehicle
        """
        try:
            # Read data using the underlying protocol
            data = self.protocol.read_data(pid)
            
            # Process Hyundai/Kia-specific data
            if pid == self.pid_vin:
                # VIN is already processed by the underlying protocol
                pass
            elif pid == self.pid_ecu_info:
                # Process ECU information
                if data and len(data) >= 8:
                    return {
                        'part_number': ''.join(chr(b) for b in data[:8]),
                        'calibration_id': ''.join(chr(b) for b in data[8:16]) if len(data) >= 16 else ''
                    }
            elif pid == self.pid_software_version:
                # Process software version
                if data and len(data) >= 4:
                    return {
                        'version': ''.join(chr(b) for b in data[:4])
                    }
            elif pid == self.pid_calibration_id:
                # Process calibration ID
                if data and len(data) >= 8:
                    return {
                        'calibration_id': ''.join(chr(b) for b in data[:8])
                    }
            elif pid == self.pid_immobilizer_status:
                # Process immobilizer status
                if data and len(data) >= 1:
                    status = data[0]
                    return {
                        'status': status,
                        'description': self._get_immobilizer_status_description(status)
                    }
            elif pid == self.pid_odometer:
                # Process odometer reading
                if data and len(data) >= 3:
                    # Hyundai/Kia typically stores odometer as 3 bytes in km
                    odometer_km = (data[0] << 16) | (data[1] << 8) | data[2]
                    return {
                        'odometer_km': odometer_km,
                        'odometer_miles': round(odometer_km * 0.621371, 1)  # Convert to miles
                    }
            elif pid == self.pid_tpms_data:
                # Process TPMS data
                if data and len(data) >= 8:
                    return {
                        'front_left': data[0] * 0.1 + 100,  # kPa
                        'front_right': data[1] * 0.1 + 100,  # kPa
                        'rear_left': data[2] * 0.1 + 100,  # kPa
                        'rear_right': data[3] * 0.1 + 100,  # kPa
                        'status': data[4],
                        'temperature': data[5] - 40  # Celsius
                    }
            
            return data
        except Exception as e:
            logger.error(f"Error reading data from Hyundai/Kia vehicle: {e}")
            return None
    
    def _get_immobilizer_status_description(self, status):
        """
        Get immobilizer status description
        
        Args:
            status (int): The status code
            
        Returns:
            str: The status description
        """
        status_descriptions = {
            0x00: "Immobilizer disabled",
            0x01: "Immobilizer enabled, vehicle secured",
            0x02: "Immobilizer enabled, key recognized",
            0x03: "Immobilizer learning mode",
            0x04: "Immobilizer malfunction",
            0x05: "Incorrect key detected",
            0x06: "No key detected",
            0x07: "Too many incorrect attempts"
        }
        
        return status_descriptions.get(status, "Unknown status")
    
    def write_data(self, pid, data):
        """
        Write data to the vehicle
        
        Args:
            pid: The parameter ID to write
            data: The data to write
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Write data using the underlying protocol
            return self.protocol.write_data(pid, data)
        except Exception as e:
            logger.error(f"Error writing data to Hyundai/Kia vehicle: {e}")
            return False
    
    def get_protocol_info(self):
        """
        Get information about the protocol
        
        Returns:
            dict: Protocol information
        """
        protocol_info = self.protocol.get_protocol_info()
        protocol_info.update({
            'name': f'Hyundai/Kia {self.variant.value}',
            'type': ProtocolType.MANUFACTURER,
            'manufacturer': 'Hyundai/Kia'
        })
        return protocol_info
    
    def request_security_access(self, level):
        """
        Request security access
        
        Args:
            level (SecurityLevel): The security level to request
            
        Returns:
            bool: True if access granted, False otherwise
        """
        try:
            # Override the key calculation method
            original_calculate_key = self.protocol.calculate_key
            self.protocol.calculate_key = self.calculate_key
            
            # Request security access using the underlying protocol
            result = self.protocol.request_security_access(level)
            
            # Restore the original key calculation method
            self.protocol.calculate_key = original_calculate_key
            
            return result
        except Exception as e:
            logger.error(f"Error requesting security access from Hyundai/Kia vehicle: {e}")
            return False
    
    def calculate_key(self, seed, level):
        """
        Calculate the key from the seed
        
        Args:
            seed: The seed received from the ECU
            level (SecurityLevel): The security level
            
        Returns:
            The calculated key
        """
        # Use the appropriate algorithm for the security level
        algorithm = self.security_algorithms.get(level, self._calculate_key_default)
        return algorithm(seed)
    
    def _calculate_key_default(self, seed):
        """Default key calculation algorithm"""
        # Simple XOR with a fixed value
        return [b ^ 0x55 for b in seed]
    
    def _calculate_key_level_1(self, seed):
        """Level 1 key calculation algorithm"""
        # Hyundai/Kia-specific algorithm for level 1
        key = []
        for b in seed:
            key.append((b + 0x11) & 0xFF)
        return key
    
    def _calculate_key_level_2(self, seed):
        """Level 2 key calculation algorithm"""
        # Hyundai/Kia-specific algorithm for level 2
        key = []
        for b in seed:
            key.append((b ^ 0x33) & 0xFF)
        return key
    
    def _calculate_key_level_3(self, seed):
        """Level 3 key calculation algorithm"""
        # Hyundai/Kia-specific algorithm for level 3
        key = []
        for b in seed:
            key.append(((b << 2) | (b >> 6)) & 0xFF)
        return key
    
    def _calculate_key_level_4(self, seed):
        """Level 4 key calculation algorithm"""
        # Hyundai/Kia-specific algorithm for level 4
        key = []
        for b in seed:
            key.append(((b + 0x45) ^ 0x67) & 0xFF)
        return key
    
    def _calculate_key_level_5(self, seed):
        """Level 5 key calculation algorithm"""
        # Hyundai/Kia-specific algorithm for level 5
        key = []
        prev = 0x12
        for b in seed:
            k = (b ^ prev) & 0xFF
            key.append(k)
            prev = b
        return key
    
    def get_vin(self):
        """
        Get the vehicle identification number
        
        Returns:
            str: The VIN
        """
        try:
            # Get VIN using the underlying protocol
            vin = self.protocol.get_vin()
            
            # Hyundai/Kia-specific VIN processing
            if vin:
                # Validate Hyundai/Kia VIN
                if len(vin) == 17 and vin[0] in 'KM,KN,5N,5K,3K':
                    return vin
            
            return vin
        except Exception as e:
            logger.error(f"Error getting VIN from Hyundai/Kia vehicle: {e}")
            return None
    
    def get_ecu_info(self):
        """
        Get information about the ECU
        
        Returns:
            dict: ECU information
        """
        try:
            # Get ECU information using Hyundai/Kia-specific PID
            ecu_info = self.read_data(self.pid_ecu_info)
            
            if not ecu_info:
                # Try to get basic ECU information
                return self.protocol.get_ecu_info()
            
            return ecu_info
        except Exception as e:
            logger.error(f"Error getting ECU information from Hyundai/Kia vehicle: {e}")
            return {}
    
    def test_connection(self, connection):
        """
        Test if the connection uses Hyundai/Kia protocol
        
        Args:
            connection: The connection to test
            
        Returns:
            bool: True if the connection uses Hyundai/Kia protocol, False otherwise
        """
        try:
            # Test connection using the underlying protocol
            if not self.protocol.test_connection(connection):
                return False
            
            # Try to read a Hyundai/Kia-specific PID
            self.protocol.interface = connection
            data = self.protocol.read_data(self.pid_ecu_info)
            
            if data:
                return True
            
            return False
        except Exception as e:
            logger.debug(f"Error testing Hyundai/Kia connection: {e}")
            return False
    
    def program_smart_key(self, key_id):
        """
        Program a smart key
        
        Args:
            key_id (int): The key ID
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Request security access
            if not self.request_security_access(SecurityLevel.LEVEL_3):
                logger.error("Failed to get security access for key programming")
                return False
            
            # Send key programming command
            command = [0x31, 0x01, self.pid_key_programming & 0xFF, key_id & 0xFF]
            response = self.send_command(command)
            
            # Check response
            if not response or response[0] != 0x71:
                logger.error("Failed to program smart key")
                return False
            
            logger.info(f"Successfully programmed smart key with ID {key_id}")
            return True
        except Exception as e:
            logger.error(f"Error programming smart key: {e}")
            return False
    
    def read_tpms_data(self):
        """
        Read TPMS (Tire Pressure Monitoring System) data
        
        Returns:
            dict: TPMS data
        """
        try:
            # Read TPMS data using Hyundai/Kia-specific PID
            return self.read_data(self.pid_tpms_data)
        except Exception as e:
            logger.error(f"Error reading TPMS data: {e}")
            return None
