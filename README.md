# Vehicle Diagnostics Tool

A comprehensive vehicle diagnostics application for reading, analyzing, and clearing diagnostic trouble codes (DTCs) from vehicles, as well as monitoring live data and programming ECUs.

## Features

- **Vehicle Database**: Includes specifications for various makes and models
- **OBD Protocol Support**: Supports multiple OBD standard protocols (CAN, ISO, etc.)
- **Manufacturer-specific Diagnostic Protocols**: Supports manufacturer-specific protocols for deeper diagnostics
- **Hardware Interface**: Compatible with ELM327 and similar OBD-II adapters
- **User Interface**: Modern GUI for viewing and analyzing diagnostic data
- **DTC Database**: Comprehensive database of diagnostic trouble codes with descriptions, causes, and solutions
- **ECU Programming**: Tools for reading, writing, and verifying ECU data

## Requirements

- Python 3.6+
- PyQt5
- python-obd
- pyserial
- SQLAlchemy
- pandas
- matplotlib

## Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/vehicle-diagnostics-tool.git
   cd vehicle-diagnostics-tool
   ```

2. Install the required packages:
   ```
   pip install -r requirements.txt
   ```

## Usage

Run the application:
```
python main.py
```

### Connecting to a Vehicle

1. Select the port where your OBD-II adapter is connected
2. Choose the protocol or leave it on "Auto-detect"
3. Click "Connect"

### Vehicle Selection

1. Select the make, model, and year of your vehicle
2. Click "Set Vehicle" to load vehicle-specific information

### Reading DTCs

1. Connect to the vehicle
2. Go to the "Diagnostic Codes" tab
3. Click "Read DTCs" to retrieve any stored trouble codes
4. View the codes and their descriptions

### Clearing DTCs

1. Connect to the vehicle
2. Go to the "Diagnostic Codes" tab
3. Click "Clear DTCs" to clear any stored trouble codes
4. Confirm the action when prompted

### Monitoring Live Data

1. Connect to the vehicle
2. Go to the "Live Data" tab
3. Select the parameters you want to monitor
4. Click "Start" to begin monitoring

### ECU Programming (Advanced)

**Warning**: ECU programming can damage your vehicle if done incorrectly. Use at your own risk.

1. Connect to the vehicle
2. Go to the "ECU Programming" tab
3. Select the ECU you want to program
4. Choose the operation (Read, Write, or Verify)
5. Follow the on-screen instructions

### DTC Database

1. Go to the "DTC Database" tab
2. Browse codes by category or search for specific codes
3. View detailed information about each code, including possible causes and solutions

## Limitations

- This tool requires compatible OBD-II hardware (ELM327 or similar)
- Some manufacturer-specific functions may require additional hardware or software
- ECU programming features are limited and may not work with all vehicles
- Professional diagnostic tools like Autel MaxiSys have exclusive access to manufacturer databases and specialized hardware

## Disclaimer

This software is provided for educational and informational purposes only. The authors are not responsible for any damage caused by the use of this software. Always consult a professional mechanic for serious vehicle issues.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- The python-obd library for OBD communication
- The PyQt5 team for the GUI framework
- The automotive community for sharing diagnostic information
