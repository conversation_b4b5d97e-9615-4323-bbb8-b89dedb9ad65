# Vehicle Diagnostic Tool

A comprehensive vehicle diagnostic system that supports OBD-II protocols, manufacturer-specific diagnostics, and ECU programming capabilities with an extensive database of vehicle specifications and diagnostic trouble codes.

## Features

### Core Functionality
- **OBD-II Protocol Support**: Compatible with all standard OBD-II protocols (ISO 9141-2, ISO 14230, ISO 15765, SAE J1850)
- **Manufacturer-Specific Diagnostics**: Support for BMW UDS, Mercedes Star Diagnosis, VW/Audi VAG-COM, Toyota Techstream
- **ECU Programming**: Read, write, and verify ECU data with security access
- **Real-time Data Monitoring**: Live sensor data with visualization and graphing
- **DTC Code Management**: Comprehensive diagnostic trouble code database with solutions

### Database Features
- **Vehicle Database**: Detailed vehicle specifications, ECU information, and diagnostic procedures
- **DTC Database**: Extensive diagnostic trouble code database with descriptions, causes, and solutions
- **Data Import/Export**: Support for JSON and CSV data formats
- **Web Scraping**: Automatic updates from online DTC code sources
- **Database Management**: Easy import/export and update tools with progress tracking

### User Interface
- **Modern GUI**: Built with PyQt5 for a professional interface
- **Data Visualization**: Real-time graphs, dashboard gauges, and charts
- **Multi-tab Interface**: Organized workflow with dedicated tabs for different functions
- **Vehicle Variant Selection**: Support for specific vehicle variants and trim levels
- **Database Statistics**: Real-time statistics and monitoring of database content

## Requirements

### Prerequisites
- Python 3.7 or higher
- pip package manager

### Required Dependencies
```bash
pip install PyQt5 pyserial requests beautifulsoup4 sqlalchemy matplotlib
```

### Optional Dependencies
```bash
pip install lxml html5lib  # For better web scraping performance
```

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/vehicle-diagnostic-tool.git
   cd vehicle-diagnostic-tool
   ```

2. Install the required packages:
   ```bash
   pip install PyQt5 pyserial requests beautifulsoup4 sqlalchemy matplotlib
   ```

3. Initialize the databases:
   ```bash
   python run_diagnostic_tool.py --init-db
   ```

4. Run system tests (optional):
   ```bash
   python run_diagnostic_tool.py --test
   ```

## Quick Start

### 1. Initialize the System
```bash
python run_diagnostic_tool.py --init-db
```

### 2. Start the Application
```bash
python run_diagnostic_tool.py
```

### 3. Alternative Launch Methods
```bash
# Run with debug logging
python run_diagnostic_tool.py --log-level DEBUG

# Run without splash screen
python run_diagnostic_tool.py --no-splash

# Run system tests
python run_diagnostic_tool.py --test
```

### Connecting to a Vehicle

1. Select the port where your OBD-II adapter is connected
2. Choose the protocol or leave it on "Auto-detect"
3. Click "Connect"

### Vehicle Selection

1. Select the make, model, and year of your vehicle
2. Click "Set Vehicle" to load vehicle-specific information

### Reading DTCs

1. Connect to the vehicle
2. Go to the "Diagnostic Codes" tab
3. Click "Read DTCs" to retrieve any stored trouble codes
4. View the codes and their descriptions

### Clearing DTCs

1. Connect to the vehicle
2. Go to the "Diagnostic Codes" tab
3. Click "Clear DTCs" to clear any stored trouble codes
4. Confirm the action when prompted

### Monitoring Live Data

1. Connect to the vehicle
2. Go to the "Live Data" tab
3. Select the parameters you want to monitor
4. Click "Start" to begin monitoring

### ECU Programming (Advanced)

**Warning**: ECU programming can damage your vehicle if done incorrectly. Use at your own risk.

1. Connect to the vehicle
2. Go to the "ECU Programming" tab
3. Select the ECU you want to program
4. Choose the operation (Read, Write, or Verify)
5. Follow the on-screen instructions

### DTC Database

1. Go to the "DTC Database" tab
2. Browse codes by category or search for specific codes
3. View detailed information about each code, including possible causes and solutions

### Database Management

The application includes a comprehensive database management system:

#### Vehicle Database Management
1. Go to the "Database Management" tab
2. **Import Vehicle Data**: Load vehicle specifications from JSON/CSV files
3. **Scrape Vehicle Data**: Automatically gather vehicle data from online sources
4. **Export Vehicle Data**: Save vehicle database to files for backup

#### DTC Database Management
1. **Update DTC Database**: Automatically scrape DTC codes from online sources
2. **Import DTC Codes**: Load DTC codes from JSON files
3. **Export DTC Codes**: Save DTC database to files for backup
4. **View Statistics**: Monitor database growth and content

#### Database Operations
- Real-time progress tracking for all operations
- Detailed logging of all database activities
- Automatic database initialization on first run
- Statistics and monitoring of database content

### Advanced Features

#### Vehicle Variant Selection
- Support for specific vehicle variants and trim levels
- Detailed ECU information for each variant
- Manufacturer-specific diagnostic procedures

#### Data Visualization
1. Go to the "Data Visualization" tab
2. Choose between real-time graphs or dashboard view
3. Monitor multiple parameters simultaneously
4. Customizable data sources and display options

#### Web Scraping
- Automatic updates from multiple online DTC code sources
- Support for manufacturer-specific code databases
- Configurable scraping parameters and sources

## Limitations

- This tool requires compatible OBD-II hardware (ELM327 or similar)
- Some manufacturer-specific functions may require additional hardware or software
- ECU programming features are limited and may not work with all vehicles
- Professional diagnostic tools like Autel MaxiSys have exclusive access to manufacturer databases and specialized hardware

## Disclaimer

This software is provided for educational and informational purposes only. The authors are not responsible for any damage caused by the use of this software. Always consult a professional mechanic for serious vehicle issues.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- The python-obd library for OBD communication
- The PyQt5 team for the GUI framework
- The automotive community for sharing diagnostic information
