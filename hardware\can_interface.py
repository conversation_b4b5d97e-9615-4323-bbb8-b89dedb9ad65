#!/usr/bin/env python3
"""
CAN Interface
This module provides an interface for CAN devices.
"""

import logging
import time
import struct
from enum import Enum, auto
from typing import Dict, List, Optional, Any, Tuple

logger = logging.getLogger("hardware.can_interface")

class CANFrameType(Enum):
    """CAN frame types"""
    STANDARD = auto()
    EXTENDED = auto()

class CANInterface:
    """CAN interface for CAN devices"""
    
    def __init__(self, port=None, baudrate=500000, frame_type=CANFrameType.STANDARD):
        """
        Initialize the CAN interface
        
        Args:
            port (str): The port to use (optional)
            baudrate (int): The baudrate to use (default: 500000)
            frame_type (CANFrameType): The frame type to use (default: STANDARD)
        """
        self.port = port
        self.baudrate = baudrate
        self.frame_type = frame_type
        self.connected = False
        self.version = "Unknown"
        self.can_socket = None
        
        # Try to import python-can
        try:
            import can
            self.can = can
            self.can_available = True
        except ImportError:
            self.can = None
            self.can_available = False
            logger.warning("python-can not available, CAN functionality will be limited")
    
    def connect(self):
        """
        Connect to the CAN device
        
        Returns:
            bool: True if connected, False otherwise
        """
        if self.connected:
            logger.info("Already connected to CAN device")
            return True
        
        if not self.can_available:
            logger.error("python-can not available, cannot connect to CAN device")
            return False
        
        try:
            # Create CAN bus
            self.can_socket = self.can.interface.Bus(
                channel=self.port,
                bustype='socketcan',
                bitrate=self.baudrate
            )
            
            # Get version information
            self.version = f"CAN Interface ({self.port}, {self.baudrate} bps)"
            
            self.connected = True
            logger.info(f"Connected to CAN device: {self.port}")
            return True
        except Exception as e:
            logger.error(f"Error connecting to CAN device: {e}")
            return False
    
    def disconnect(self):
        """
        Disconnect from the CAN device
        
        Returns:
            bool: True if disconnected, False otherwise
        """
        if not self.connected:
            logger.info("Not connected to CAN device")
            return True
        
        try:
            # Close CAN socket
            if self.can_socket:
                self.can_socket.shutdown()
                self.can_socket = None
            
            self.connected = False
            logger.info("Disconnected from CAN device")
            return True
        except Exception as e:
            logger.error(f"Error disconnecting from CAN device: {e}")
            return False
    
    def send_message(self, arbitration_id, data, extended_id=None):
        """
        Send a CAN message
        
        Args:
            arbitration_id (int): The arbitration ID
            data (bytes): The data to send
            extended_id (bool): Whether to use extended ID (optional)
            
        Returns:
            bool: True if sent, False otherwise
        """
        if not self.connected or not self.can_socket:
            logger.error("Not connected to CAN device")
            return False
        
        try:
            # Determine if extended ID should be used
            if extended_id is None:
                extended_id = (self.frame_type == CANFrameType.EXTENDED)
            
            # Create message
            message = self.can.Message(
                arbitration_id=arbitration_id,
                data=data,
                is_extended_id=extended_id
            )
            
            # Send message
            self.can_socket.send(message)
            return True
        except Exception as e:
            logger.error(f"Error sending CAN message: {e}")
            return False
    
    def receive_message(self, timeout=1.0):
        """
        Receive a CAN message
        
        Args:
            timeout (float): The timeout in seconds
            
        Returns:
            tuple: (arbitration_id, data, extended_id) or None if no message
        """
        if not self.connected or not self.can_socket:
            logger.error("Not connected to CAN device")
            return None
        
        try:
            # Receive message
            message = self.can_socket.recv(timeout=timeout)
            
            if message is None:
                return None
            
            # Return message data
            return (message.arbitration_id, message.data, message.is_extended_id)
        except Exception as e:
            logger.error(f"Error receiving CAN message: {e}")
            return None
    
    def send_isotp_message(self, arbitration_id, data, extended_id=None, rx_id=None):
        """
        Send an ISO-TP message
        
        Args:
            arbitration_id (int): The arbitration ID
            data (bytes): The data to send
            extended_id (bool): Whether to use extended ID (optional)
            rx_id (int): The receive ID (optional)
            
        Returns:
            bool: True if sent, False otherwise
        """
        if not self.connected or not self.can_socket:
            logger.error("Not connected to CAN device")
            return False
        
        try:
            # Check if isotp is available
            try:
                import isotp
            except ImportError:
                logger.error("isotp not available, cannot send ISO-TP message")
                return False
            
            # Determine if extended ID should be used
            if extended_id is None:
                extended_id = (self.frame_type == CANFrameType.EXTENDED)
            
            # Determine receive ID if not specified
            if rx_id is None:
                rx_id = arbitration_id + 8  # Common convention
            
            # Create ISO-TP socket
            isotp_socket = isotp.socket()
            
            # Set up addressing information
            if extended_id:
                isotp_socket.set_opts(txpad=0, rxpad=0, ext_address=0, tx_stmin=0, tx_bs=0)
            else:
                isotp_socket.set_opts(txpad=0, rxpad=0, ext_address=None, tx_stmin=0, tx_bs=0)
            
            # Bind socket
            isotp_socket.bind(self.port, isotp.Address(
                rxid=rx_id,
                txid=arbitration_id,
                addressing_mode=(isotp.AddressingMode.Extended_29bits if extended_id else isotp.AddressingMode.Normal_11bits)
            ))
            
            # Send message
            isotp_socket.send(data)
            
            # Close socket
            isotp_socket.close()
            
            return True
        except Exception as e:
            logger.error(f"Error sending ISO-TP message: {e}")
            return False
    
    def receive_isotp_message(self, arbitration_id, extended_id=None, tx_id=None, timeout=1.0):
        """
        Receive an ISO-TP message
        
        Args:
            arbitration_id (int): The arbitration ID
            extended_id (bool): Whether to use extended ID (optional)
            tx_id (int): The transmit ID (optional)
            timeout (float): The timeout in seconds
            
        Returns:
            bytes: The received data or None if no data
        """
        if not self.connected or not self.can_socket:
            logger.error("Not connected to CAN device")
            return None
        
        try:
            # Check if isotp is available
            try:
                import isotp
            except ImportError:
                logger.error("isotp not available, cannot receive ISO-TP message")
                return None
            
            # Determine if extended ID should be used
            if extended_id is None:
                extended_id = (self.frame_type == CANFrameType.EXTENDED)
            
            # Determine transmit ID if not specified
            if tx_id is None:
                tx_id = arbitration_id + 8  # Common convention
            
            # Create ISO-TP socket
            isotp_socket = isotp.socket()
            
            # Set up addressing information
            if extended_id:
                isotp_socket.set_opts(txpad=0, rxpad=0, ext_address=0, tx_stmin=0, tx_bs=0)
            else:
                isotp_socket.set_opts(txpad=0, rxpad=0, ext_address=None, tx_stmin=0, tx_bs=0)
            
            # Bind socket
            isotp_socket.bind(self.port, isotp.Address(
                rxid=arbitration_id,
                txid=tx_id,
                addressing_mode=(isotp.AddressingMode.Extended_29bits if extended_id else isotp.AddressingMode.Normal_11bits)
            ))
            
            # Set timeout
            isotp_socket.settimeout(timeout)
            
            # Receive message
            try:
                data = isotp_socket.recv()
            except socket.timeout:
                data = None
            
            # Close socket
            isotp_socket.close()
            
            return data
        except Exception as e:
            logger.error(f"Error receiving ISO-TP message: {e}")
            return None
    
    def get_supported_protocols(self):
        """
        Get supported protocols
        
        Returns:
            list: List of supported protocols
        """
        return [
            "CAN",
            "ISO15765"
        ]
    
    def update_firmware(self, firmware_file):
        """
        Update firmware
        
        Args:
            firmware_file (str): Path to the firmware file
            
        Returns:
            bool: True if updated, False otherwise
        """
        logger.error("Firmware update not supported for CAN devices")
        return False
