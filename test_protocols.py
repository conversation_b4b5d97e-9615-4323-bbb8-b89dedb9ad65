#!/usr/bin/env python3
"""
Protocol Test Script
This script tests the protocol handlers.
"""

import sys
import time
import logging
import argparse
import json

from hardware.elm327 import ELM327Interface
from protocols.protocol_manager import ProtocolManager, ConnectionMethod
from protocols.protocol_handler import SecurityLevel
from diagnostic.session_manager import DiagnosticSession, SessionState

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("protocol_test.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("test_protocols")

def test_connection(port, baudrate, protocol=None, make=None, model=None, year=None):
    """
    Test connection to the vehicle
    
    Args:
        port (str): The serial port
        baudrate (int): The baudrate
        protocol (str): The protocol name
        make (str): The vehicle make
        model (str): The vehicle model
        year (int): The vehicle year
    """
    try:
        logger.info("Testing connection to vehicle")
        
        # Create interface
        interface = ELM327Interface(port=port, baudrate=baudrate)
        
        # Connect to the interface
        if not interface.connect():
            logger.error("Failed to connect to ELM327 interface")
            return
        
        logger.info(f"Connected to ELM327 interface (version {interface.version})")
        
        # Create diagnostic session
        session = DiagnosticSession(interface)
        
        # Connect to the vehicle
        if protocol:
            # Manual connection with protocol
            logger.info(f"Connecting to vehicle using protocol: {protocol}")
            if not session.connect(method=ConnectionMethod.MANUAL, protocol_name=protocol):
                logger.error("Failed to connect to vehicle")
                interface.disconnect()
                return
        elif make and year:
            # Manual connection with vehicle info
            logger.info(f"Connecting to vehicle: {make} {model} {year}")
            if not session.connect(method=ConnectionMethod.MANUAL, make=make, model=model, year=year):
                logger.error("Failed to connect to vehicle")
                interface.disconnect()
                return
        else:
            # Automatic connection
            logger.info("Connecting to vehicle (automatic detection)")
            if not session.connect(method=ConnectionMethod.AUTO):
                logger.error("Failed to connect to vehicle")
                interface.disconnect()
                return
        
        # Get session info
        session_info = session.get_session_info()
        logger.info(f"Session info: {json.dumps(session_info, indent=2)}")
        
        # Read DTCs
        logger.info("Reading DTCs")
        dtcs = session.read_dtc()
        
        if dtcs:
            logger.info(f"Found {len(dtcs)} DTCs:")
            for dtc in dtcs:
                logger.info(f"  {dtc.get('code')} - {dtc.get('description', 'Unknown')}")
        else:
            logger.info("No DTCs found")
        
        # Read VIN
        logger.info("Reading VIN")
        vin = session.protocol_manager.current_protocol.get_vin()
        
        if vin:
            logger.info(f"VIN: {vin}")
        else:
            logger.info("Failed to read VIN")
        
        # Read some data
        logger.info("Reading engine RPM")
        rpm_data = session.read_data(0x0C)
        
        if rpm_data:
            # Parse RPM data (2 bytes)
            if len(rpm_data) >= 2:
                rpm = ((rpm_data[0] << 8) | rpm_data[1]) // 4
                logger.info(f"Engine RPM: {rpm}")
            else:
                logger.info(f"Raw RPM data: {rpm_data}")
        else:
            logger.info("Failed to read engine RPM")
        
        # Save session
        logger.info("Saving session")
        session.save_session("session.json")
        
        # Disconnect
        logger.info("Disconnecting from vehicle")
        session.disconnect()
        interface.disconnect()
        
        logger.info("Test completed successfully")
    except Exception as e:
        logger.error(f"Error testing connection: {e}")

def list_protocols():
    """List supported protocols"""
    try:
        logger.info("Listing supported protocols")
        
        # Create protocol manager
        protocol_manager = ProtocolManager()
        
        # Get supported protocols
        protocols = protocol_manager.get_supported_protocols()
        
        # Print standard protocols
        logger.info("Standard protocols:")
        for protocol in protocols.get('standard', []):
            logger.info(f"  {protocol}")
        
        # Print manufacturer protocols
        logger.info("Manufacturer protocols:")
        for protocol in protocols.get('manufacturer', []):
            logger.info(f"  {protocol}")
        
        # Print manufacturers
        logger.info("Supported manufacturers:")
        for manufacturer in protocols.get('manufacturers', []):
            # Get protocols for this manufacturer
            manufacturer_protocols = protocol_manager.get_manufacturer_protocols(manufacturer)
            logger.info(f"  {manufacturer}: {', '.join(manufacturer_protocols)}")
    except Exception as e:
        logger.error(f"Error listing protocols: {e}")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Test protocol handlers')
    parser.add_argument('--port', type=str, help='Serial port')
    parser.add_argument('--baudrate', type=int, default=38400, help='Serial baudrate')
    parser.add_argument('--protocol', type=str, help='Protocol name')
    parser.add_argument('--make', type=str, help='Vehicle make')
    parser.add_argument('--model', type=str, help='Vehicle model')
    parser.add_argument('--year', type=int, help='Vehicle year')
    parser.add_argument('--list', action='store_true', help='List supported protocols')
    
    args = parser.parse_args()
    
    if args.list:
        list_protocols()
    elif args.port:
        test_connection(args.port, args.baudrate, args.protocol, args.make, args.model, args.year)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
