#!/usr/bin/env python3
"""
Vehicle Data Importer Module
This module provides functionality for importing vehicle data from various sources.
"""

import os
import json
import logging
import csv
import requests
import time
import re
from typing import Dict, List, Optional, Any, Tuple, Union
from bs4 import BeautifulSoup

# Set up logging
logger = logging.getLogger("database.vehicle_data_importer")

class VehicleDataImporter:
    """Vehicle data importer"""
    
    def __init__(self, vehicle_db, output_dir="vehicle_data"):
        """
        Initialize the vehicle data importer
        
        Args:
            vehicle_db: The vehicle database
            output_dir (str): The output directory for scraped data
        """
        self.vehicle_db = vehicle_db
        self.output_dir = output_dir
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # Create output directory if it doesn't exist
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
    
    def import_from_json(self, file_path):
        """
        Import vehicle data from a JSON file
        
        Args:
            file_path (str): Path to the JSON file
            
        Returns:
            int: Number of vehicles imported
        """
        try:
            logger.info(f"Importing vehicle data from {file_path}")
            
            # Load data from file
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            # Import data
            count = self._import_vehicle_data(data)
            
            logger.info(f"Imported {count} vehicles from {file_path}")
            return count
        except Exception as e:
            logger.error(f"Error importing vehicle data from {file_path}: {e}")
            return 0
    
    def import_from_csv(self, file_path, delimiter=','):
        """
        Import vehicle data from a CSV file
        
        Args:
            file_path (str): Path to the CSV file
            delimiter (str): The delimiter used in the CSV file
            
        Returns:
            int: Number of vehicles imported
        """
        try:
            logger.info(f"Importing vehicle data from {file_path}")
            
            # Load data from file
            with open(file_path, 'r', newline='') as f:
                reader = csv.DictReader(f, delimiter=delimiter)
                data = list(reader)
            
            # Convert data to the expected format
            vehicle_data = self._convert_csv_to_vehicle_data(data)
            
            # Import data
            count = self._import_vehicle_data(vehicle_data)
            
            logger.info(f"Imported {count} vehicles from {file_path}")
            return count
        except Exception as e:
            logger.error(f"Error importing vehicle data from {file_path}: {e}")
            return 0
    
    def _convert_csv_to_vehicle_data(self, csv_data):
        """
        Convert CSV data to the expected vehicle data format
        
        Args:
            csv_data (list): List of dictionaries from CSV
            
        Returns:
            dict: Vehicle data in the expected format
        """
        vehicle_data = {}
        
        for row in csv_data:
            make = row.get('Make', '')
            model = row.get('Model', '')
            year = row.get('Year', '')
            
            if not make or not model or not year:
                continue
            
            try:
                year = int(year)
            except ValueError:
                continue
            
            # Create make if it doesn't exist
            if make not in vehicle_data:
                vehicle_data[make] = {}
            
            # Create model if it doesn't exist
            if model not in vehicle_data[make]:
                vehicle_data[make][model] = []
            
            # Create vehicle data
            vehicle = {
                'year': year,
                'engine_type': row.get('EngineType', ''),
                'engine_displacement': float(row.get('EngineDisplacement', 0)) if row.get('EngineDisplacement', '') else None,
                'fuel_type': row.get('FuelType', ''),
                'transmission_type': row.get('TransmissionType', ''),
                'num_cylinders': int(row.get('NumCylinders', 0)) if row.get('NumCylinders', '') else None,
                'obd_protocol': row.get('OBDProtocol', ''),
                'manufacturer_protocol': row.get('ManufacturerProtocol', ''),
                'generation': row.get('Generation', ''),
                'variant': row.get('Variant', ''),
                'body_style': row.get('BodyStyle', ''),
                'drive_type': row.get('DriveType', ''),
                'obd_port_location': row.get('OBDPortLocation', ''),
                'ecu_location': row.get('ECULocation', ''),
                'security_level': int(row.get('SecurityLevel', 0)) if row.get('SecurityLevel', '') else None,
                'special_notes': row.get('SpecialNotes', ''),
                'vin_pattern': row.get('VINPattern', ''),
                'engine_code': row.get('EngineCode', '')
            }
            
            # Add ECU data if available
            if 'ECUName' in row and row['ECUName']:
                ecu = {
                    'name': row.get('ECUName', ''),
                    'code': row.get('ECUCode', ''),
                    'location': row.get('ECULocation', ''),
                    'protocol': row.get('ECUProtocol', ''),
                    'address': row.get('ECUAddress', ''),
                    'security_type': row.get('ECUSecurityType', ''),
                    'flash_method': row.get('ECUFlashMethod', ''),
                    'special_notes': row.get('ECUSpecialNotes', '')
                }
                vehicle['ecus'] = [ecu]
            
            # Add vehicle to model
            vehicle_data[make][model].append(vehicle)
        
        return vehicle_data
    
    def _import_vehicle_data(self, data):
        """
        Import vehicle data
        
        Args:
            data (dict): Vehicle data
            
        Returns:
            int: Number of vehicles imported
        """
        count = 0
        
        # Import data
        for make_name, models in data.items():
            # Add make
            make_id = self.vehicle_db.add_make(make_name)
            
            if not make_id:
                continue
            
            for model_name, vehicles in models.items():
                # Add model
                model_id = self.vehicle_db.add_model(make_id, model_name)
                
                if not model_id:
                    continue
                
                for vehicle_data in vehicles:
                    # Add vehicle
                    vehicle_id = self.vehicle_db.add_vehicle(
                        model_id=model_id,
                        year=vehicle_data.get('year'),
                        engine_type=vehicle_data.get('engine_type'),
                        engine_displacement=vehicle_data.get('engine_displacement'),
                        fuel_type=vehicle_data.get('fuel_type'),
                        transmission_type=vehicle_data.get('transmission_type'),
                        num_cylinders=vehicle_data.get('num_cylinders'),
                        obd_protocol=vehicle_data.get('obd_protocol'),
                        manufacturer_protocol=vehicle_data.get('manufacturer_protocol'),
                        generation=vehicle_data.get('generation'),
                        variant=vehicle_data.get('variant'),
                        body_style=vehicle_data.get('body_style'),
                        drive_type=vehicle_data.get('drive_type'),
                        obd_port_location=vehicle_data.get('obd_port_location'),
                        ecu_location=vehicle_data.get('ecu_location'),
                        security_level=vehicle_data.get('security_level'),
                        special_notes=vehicle_data.get('special_notes'),
                        vin_pattern=vehicle_data.get('vin_pattern'),
                        engine_code=vehicle_data.get('engine_code')
                    )
                    
                    if not vehicle_id:
                        continue
                    
                    # Add ECUs
                    if 'ecus' in vehicle_data:
                        for ecu_data in vehicle_data['ecus']:
                            self.vehicle_db.add_ecu(
                                vehicle_id=vehicle_id,
                                name=ecu_data.get('name', ''),
                                code=ecu_data.get('code', ''),
                                location=ecu_data.get('location', ''),
                                protocol=ecu_data.get('protocol', ''),
                                address=ecu_data.get('address', ''),
                                security_type=ecu_data.get('security_type', ''),
                                flash_method=ecu_data.get('flash_method', ''),
                                special_notes=ecu_data.get('special_notes', '')
                            )
                    
                    count += 1
        
        return count
    
    def scrape_car_data(self, make, model=None, year_range=None):
        """
        Scrape car data from online sources
        
        Args:
            make (str): The car make
            model (str, optional): The car model
            year_range (tuple, optional): The year range (start_year, end_year)
            
        Returns:
            dict: The scraped car data
        """
        try:
            logger.info(f"Scraping car data for {make} {model if model else ''} {year_range if year_range else ''}")
            
            # Scrape data from multiple sources
            data = {}
            
            # Scrape from CarQuery API
            carquery_data = self._scrape_from_carquery(make, model, year_range)
            if carquery_data:
                self._merge_car_data(data, carquery_data)
            
            # Scrape from Car-Part.com
            carpart_data = self._scrape_from_carpart(make, model, year_range)
            if carpart_data:
                self._merge_car_data(data, carpart_data)
            
            # Scrape from Edmunds
            edmunds_data = self._scrape_from_edmunds(make, model, year_range)
            if edmunds_data:
                self._merge_car_data(data, edmunds_data)
            
            # Save data to file
            file_name = f"{make.lower()}_{'_' + model.lower() if model else ''}_data.json"
            file_path = os.path.join(self.output_dir, file_name)
            
            with open(file_path, 'w') as f:
                json.dump(data, f, indent=2)
            
            logger.info(f"Saved car data to {file_path}")
            
            # Import data
            count = self._import_vehicle_data(data)
            
            logger.info(f"Imported {count} vehicles")
            
            return data
        except Exception as e:
            logger.error(f"Error scraping car data: {e}")
            return {}
    
    def _scrape_from_carquery(self, make, model=None, year_range=None):
        """
        Scrape car data from CarQuery API
        
        Args:
            make (str): The car make
            model (str, optional): The car model
            year_range (tuple, optional): The year range (start_year, end_year)
            
        Returns:
            dict: The scraped car data
        """
        try:
            logger.info(f"Scraping from CarQuery API: {make} {model if model else ''} {year_range if year_range else ''}")
            
            # Build API URL
            url = "https://www.carqueryapi.com/api/0.3/?callback=?&cmd=getTrims"
            
            # Add parameters
            params = {
                'make': make,
                'model': model,
                'min_year': year_range[0] if year_range else None,
                'max_year': year_range[1] if year_range else None
            }
            
            # Remove None values
            params = {k: v for k, v in params.items() if v is not None}
            
            # Make request
            response = self.session.get(url, params=params)
            
            # Parse response
            data_str = response.text.strip('?(').rstrip(')')
            data = json.loads(data_str)
            
            # Convert to our format
            return self._convert_carquery_data(data)
        except Exception as e:
            logger.error(f"Error scraping from CarQuery API: {e}")
            return {}
    
    def _convert_carquery_data(self, data):
        """
        Convert CarQuery data to our format
        
        Args:
            data (dict): The CarQuery data
            
        Returns:
            dict: The converted data
        """
        vehicle_data = {}
        
        for trim in data.get('Trims', []):
            make = trim.get('model_make_display', '')
            model = trim.get('model_name', '')
            year = trim.get('model_year', '')
            
            if not make or not model or not year:
                continue
            
            try:
                year = int(year)
            except ValueError:
                continue
            
            # Create make if it doesn't exist
            if make not in vehicle_data:
                vehicle_data[make] = {}
            
            # Create model if it doesn't exist
            if model not in vehicle_data[make]:
                vehicle_data[make][model] = []
            
            # Create vehicle data
            vehicle = {
                'year': year,
                'engine_type': trim.get('model_engine_type', ''),
                'engine_displacement': float(trim.get('model_engine_cc', 0)) / 1000 if trim.get('model_engine_cc', '') else None,
                'fuel_type': trim.get('model_engine_fuel', ''),
                'transmission_type': trim.get('model_transmission_type', ''),
                'num_cylinders': int(trim.get('model_engine_cyl', 0)) if trim.get('model_engine_cyl', '') else None,
                'generation': trim.get('model_generation', ''),
                'variant': trim.get('model_trim', ''),
                'body_style': trim.get('model_body', ''),
                'drive_type': trim.get('model_drive', ''),
                'engine_code': trim.get('model_engine_code', '')
            }
            
            # Add vehicle to model
            vehicle_data[make][model].append(vehicle)
        
        return vehicle_data
    
    def _scrape_from_carpart(self, make, model=None, year_range=None):
        """
        Scrape car data from Car-Part.com
        
        Args:
            make (str): The car make
            model (str, optional): The car model
            year_range (tuple, optional): The year range (start_year, end_year)
            
        Returns:
            dict: The scraped car data
        """
        # This would normally scrape data from Car-Part.com
        # For now, we'll just return an empty dictionary
        return {}
    
    def _scrape_from_edmunds(self, make, model=None, year_range=None):
        """
        Scrape car data from Edmunds
        
        Args:
            make (str): The car make
            model (str, optional): The car model
            year_range (tuple, optional): The year range (start_year, end_year)
            
        Returns:
            dict: The scraped car data
        """
        # This would normally scrape data from Edmunds
        # For now, we'll just return an empty dictionary
        return {}
    
    def _merge_car_data(self, target, source):
        """
        Merge car data
        
        Args:
            target (dict): The target data
            source (dict): The source data
        """
        for make, models in source.items():
            # Create make if it doesn't exist
            if make not in target:
                target[make] = {}
            
            for model, vehicles in models.items():
                # Create model if it doesn't exist
                if model not in target[make]:
                    target[make][model] = []
                
                # Add vehicles
                for vehicle in vehicles:
                    # Check if vehicle already exists
                    existing_vehicle = None
                    for v in target[make][model]:
                        if v.get('year') == vehicle.get('year') and v.get('variant') == vehicle.get('variant'):
                            existing_vehicle = v
                            break
                    
                    if existing_vehicle:
                        # Update existing vehicle
                        for key, value in vehicle.items():
                            if value and not existing_vehicle.get(key):
                                existing_vehicle[key] = value
                    else:
                        # Add new vehicle
                        target[make][model].append(vehicle)
