#!/usr/bin/env python3
"""
General Motors Protocol Handler
This module provides the protocol handler for GM-specific protocols.
"""

import logging
import time
import struct
from enum import Enum, auto

from protocols.protocol_handler import BaseProtocol, ProtocolType, SecurityLevel, SecurityAccessStatus, ProtocolError, CommunicationError
from protocols.standard.iso15765 import ISO15765Protocol, CANFrameType
from protocols.standard.j1850 import J1850Protocol, J1850Variant

logger = logging.getLogger("protocol.GM")

class GMVariant(Enum):
    """GM protocol variants"""
    ALDL = "ALDL"        # Assembly Line Diagnostic Link (pre-1996)
    J1850_VPW = "J1850_VPW"  # J1850 VPW (1996-2007)
    UDS = "UDS"          # UDS over CAN (2008+)

class GMProtocol(BaseProtocol):
    """General Motors protocol handler"""
    
    def __init__(self, interface=None, variant=GMVariant.UDS, baudrate=500000):
        """
        Initialize the GM protocol handler
        
        Args:
            interface: The hardware interface to use
            variant (GMVariant): The protocol variant (default: UDS)
            baudrate (int): The CAN baudrate (default: 500000)
        """
        super().__init__(interface)
        
        if isinstance(variant, str):
            try:
                self.variant = GMVariant(variant)
            except ValueError:
                self.variant = GMVariant.UDS
                logger.warning(f"Invalid GM protocol variant: {variant}, using UDS")
        else:
            self.variant = variant
        
        self.baudrate = baudrate
        
        # Initialize the appropriate protocol handler based on the variant
        if self.variant == GMVariant.UDS:
            # UDS over ISO15765 (CAN)
            self.protocol = ISO15765Protocol(
                interface=interface,
                baudrate=baudrate,
                frame_type=CANFrameType.STANDARD,
                tx_id=0x7E0,  # GM-specific diagnostic CAN ID
                rx_id=0x7E8   # GM-specific response CAN ID
            )
        elif self.variant == GMVariant.J1850_VPW:
            # J1850 VPW protocol
            self.protocol = J1850Protocol(
                interface=interface,
                variant=J1850Variant.VPW,
                tx_id=0xF4,
                rx_id=0x74
            )
        elif self.variant == GMVariant.ALDL:
            # ALDL protocol (older GM vehicles)
            # For now, we'll use a modified J1850 protocol
            self.protocol = J1850Protocol(
                interface=interface,
                variant=J1850Variant.VPW,
                tx_id=0xF4,
                rx_id=0x74
            )
        
        # GM-specific PIDs
        self.pid_vin = 0x09
        self.pid_ecu_info = 0x1A
        self.pid_software_version = 0x04
        self.pid_calibration_id = 0x1C
        self.pid_cvn = 0x1D
        self.pid_fuel_system_status = 0x03
        self.pid_engine_load = 0x04
        self.pid_engine_coolant_temp = 0x05
        self.pid_fuel_pressure = 0x0A
        self.pid_intake_manifold_pressure = 0x0B
        self.pid_engine_rpm = 0x0C
        self.pid_vehicle_speed = 0x0D
        self.pid_timing_advance = 0x0E
        self.pid_intake_air_temp = 0x0F
        self.pid_maf_air_flow = 0x10
        self.pid_throttle_position = 0x11
        
        # GM-specific ECUs
        self.ecu_engine = 0x10
        self.ecu_transmission = 0x18
        self.ecu_abs = 0x28
        self.ecu_airbag = 0x29
        self.ecu_instrument_cluster = 0x30
        self.ecu_body_control = 0x40
        self.ecu_hvac = 0x50
        self.ecu_radio = 0x60
        
        # GM-specific security access seeds/keys
        self.security_algorithms = {
            SecurityLevel.LEVEL_1: self._calculate_key_level_1,
            SecurityLevel.LEVEL_2: self._calculate_key_level_2,
            SecurityLevel.LEVEL_3: self._calculate_key_level_3,
            SecurityLevel.LEVEL_4: self._calculate_key_level_4,
            SecurityLevel.LEVEL_5: self._calculate_key_level_5
        }
    
    def connect(self):
        """
        Connect to the vehicle
        
        Returns:
            bool: True if connected, False otherwise
        """
        try:
            # Connect using the underlying protocol
            if self.protocol.connect():
                self.connected = True
                logger.info(f"Connected to GM vehicle using {self.variant.value} protocol")
                
                # Enter diagnostic session for GM
                if self.variant == GMVariant.UDS:
                    self.protocol.enter_diagnostic_session(self.protocol.session_extended_diagnostic)
                
                return True
            else:
                logger.error("Failed to connect to GM vehicle")
                return False
        except Exception as e:
            logger.error(f"Error connecting to GM vehicle: {e}")
            return False
    
    def disconnect(self):
        """
        Disconnect from the vehicle
        
        Returns:
            bool: True if disconnected, False otherwise
        """
        try:
            # Disconnect using the underlying protocol
            if self.protocol.disconnect():
                self.connected = False
                logger.info("Disconnected from GM vehicle")
                return True
            else:
                logger.error("Failed to disconnect from GM vehicle")
                return False
        except Exception as e:
            logger.error(f"Error disconnecting from GM vehicle: {e}")
            return False
    
    def send_command(self, command, response_required=True):
        """
        Send a command to the vehicle
        
        Args:
            command: The command to send
            response_required (bool): Whether a response is required
            
        Returns:
            The response from the vehicle
        """
        try:
            # Send command using the underlying protocol
            return self.protocol.send_command(command, response_required)
        except Exception as e:
            logger.error(f"Error sending command to GM vehicle: {e}")
            raise CommunicationError(f"Error sending command to GM vehicle: {e}")
    
    def read_dtc(self):
        """
        Read diagnostic trouble codes
        
        Returns:
            list: List of DTCs
        """
        try:
            # Read DTCs using the underlying protocol
            dtcs = self.protocol.read_dtc()
            
            # Add GM-specific information to DTCs
            for dtc in dtcs:
                # Add GM-specific DTC information if available
                # This would typically come from a GM-specific DTC database
                pass
            
            return dtcs
        except Exception as e:
            logger.error(f"Error reading DTCs from GM vehicle: {e}")
            return []
    
    def clear_dtc(self):
        """
        Clear diagnostic trouble codes
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Clear DTCs using the underlying protocol
            return self.protocol.clear_dtc()
        except Exception as e:
            logger.error(f"Error clearing DTCs from GM vehicle: {e}")
            return False
    
    def read_data(self, pid):
        """
        Read data from the vehicle
        
        Args:
            pid: The parameter ID to read
            
        Returns:
            The data read from the vehicle
        """
        try:
            # Read data using the underlying protocol
            data = self.protocol.read_data(pid)
            
            # Process GM-specific data
            if pid == self.pid_vin:
                # VIN is already processed by the underlying protocol
                pass
            elif pid == self.pid_ecu_info:
                # Process ECU information
                if data and len(data) >= 8:
                    return {
                        'hardware_number': ''.join(chr(b) for b in data[:4]),
                        'software_number': ''.join(chr(b) for b in data[4:8])
                    }
            elif pid == self.pid_software_version:
                # Process software version
                if data and len(data) >= 4:
                    return {
                        'version': ''.join(chr(b) for b in data[:4])
                    }
            elif pid == self.pid_calibration_id:
                # Process calibration ID
                if data and len(data) >= 8:
                    return {
                        'calibration_id': ''.join(chr(b) for b in data[:8])
                    }
            elif pid == self.pid_cvn:
                # Process Calibration Verification Number
                if data and len(data) >= 4:
                    return {
                        'cvn': ''.join(f"{b:02X}" for b in data[:4])
                    }
            elif pid == self.pid_fuel_system_status:
                # Process fuel system status
                if data and len(data) >= 2:
                    return {
                        'system1': data[0],
                        'system2': data[1]
                    }
            elif pid == self.pid_engine_load:
                # Process calculated engine load
                if data and len(data) >= 1:
                    return data[0] * 100 / 255
            elif pid == self.pid_engine_coolant_temp:
                # Process engine coolant temperature
                if data and len(data) >= 1:
                    return data[0] - 40
            elif pid == self.pid_fuel_pressure:
                # Process fuel pressure
                if data and len(data) >= 1:
                    return data[0] * 3
            elif pid == self.pid_intake_manifold_pressure:
                # Process intake manifold pressure
                if data and len(data) >= 1:
                    return data[0]
            elif pid == self.pid_engine_rpm:
                # Process engine RPM
                if data and len(data) >= 2:
                    return ((data[0] * 256) + data[1]) / 4
            elif pid == self.pid_vehicle_speed:
                # Process vehicle speed
                if data and len(data) >= 1:
                    return data[0]
            elif pid == self.pid_timing_advance:
                # Process timing advance
                if data and len(data) >= 1:
                    return (data[0] - 128) / 2
            elif pid == self.pid_intake_air_temp:
                # Process intake air temperature
                if data and len(data) >= 1:
                    return data[0] - 40
            elif pid == self.pid_maf_air_flow:
                # Process MAF air flow
                if data and len(data) >= 2:
                    return ((data[0] * 256) + data[1]) / 100
            elif pid == self.pid_throttle_position:
                # Process throttle position
                if data and len(data) >= 1:
                    return data[0] * 100 / 255
            
            return data
        except Exception as e:
            logger.error(f"Error reading data from GM vehicle: {e}")
            return None
    
    def write_data(self, pid, data):
        """
        Write data to the vehicle
        
        Args:
            pid: The parameter ID to write
            data: The data to write
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Write data using the underlying protocol
            return self.protocol.write_data(pid, data)
        except Exception as e:
            logger.error(f"Error writing data to GM vehicle: {e}")
            return False
    
    def get_protocol_info(self):
        """
        Get information about the protocol
        
        Returns:
            dict: Protocol information
        """
        protocol_info = self.protocol.get_protocol_info()
        protocol_info.update({
            'name': f'GM {self.variant.value}',
            'type': ProtocolType.MANUFACTURER,
            'manufacturer': 'General Motors'
        })
        return protocol_info
    
    def request_security_access(self, level):
        """
        Request security access
        
        Args:
            level (SecurityLevel): The security level to request
            
        Returns:
            bool: True if access granted, False otherwise
        """
        try:
            # Override the key calculation method
            original_calculate_key = self.protocol.calculate_key
            self.protocol.calculate_key = self.calculate_key
            
            # Request security access using the underlying protocol
            result = self.protocol.request_security_access(level)
            
            # Restore the original key calculation method
            self.protocol.calculate_key = original_calculate_key
            
            return result
        except Exception as e:
            logger.error(f"Error requesting security access from GM vehicle: {e}")
            return False
    
    def calculate_key(self, seed, level):
        """
        Calculate the key from the seed
        
        Args:
            seed: The seed received from the ECU
            level (SecurityLevel): The security level
            
        Returns:
            The calculated key
        """
        # Use the appropriate algorithm for the security level
        algorithm = self.security_algorithms.get(level, self._calculate_key_default)
        return algorithm(seed)
    
    def _calculate_key_default(self, seed):
        """Default key calculation algorithm"""
        # Simple XOR with a fixed value
        return [b ^ 0x55 for b in seed]
    
    def _calculate_key_level_1(self, seed):
        """Level 1 key calculation algorithm"""
        # GM-specific algorithm for level 1
        key = []
        for b in seed:
            key.append((b + 0x45) & 0xFF)
        return key
    
    def _calculate_key_level_2(self, seed):
        """Level 2 key calculation algorithm"""
        # GM-specific algorithm for level 2
        key = []
        for b in seed:
            key.append((b ^ 0x34) & 0xFF)
        return key
    
    def _calculate_key_level_3(self, seed):
        """Level 3 key calculation algorithm"""
        # GM-specific algorithm for level 3
        key = []
        for b in seed:
            key.append(((b << 2) | (b >> 6)) & 0xFF)
        return key
    
    def _calculate_key_level_4(self, seed):
        """Level 4 key calculation algorithm"""
        # GM-specific algorithm for level 4
        key = []
        for b in seed:
            key.append(((b + 0x12) ^ 0xCD) & 0xFF)
        return key
    
    def _calculate_key_level_5(self, seed):
        """Level 5 key calculation algorithm"""
        # GM-specific algorithm for level 5
        key = []
        prev = 0x73
        for b in seed:
            k = (b ^ prev) & 0xFF
            key.append(k)
            prev = b
        return key
    
    def get_vin(self):
        """
        Get the vehicle identification number
        
        Returns:
            str: The VIN
        """
        try:
            # Get VIN using the underlying protocol
            vin = self.protocol.get_vin()
            
            # GM-specific VIN processing
            if vin:
                # Validate GM VIN
                if len(vin) == 17 and vin[0] in '1,2,3,4,5,G':
                    return vin
            
            return vin
        except Exception as e:
            logger.error(f"Error getting VIN from GM vehicle: {e}")
            return None
    
    def get_ecu_info(self):
        """
        Get information about the ECU
        
        Returns:
            dict: ECU information
        """
        try:
            # Get ECU information using GM-specific PID
            ecu_info = self.read_data(self.pid_ecu_info)
            
            if not ecu_info:
                # Try to get basic ECU information
                return self.protocol.get_ecu_info()
            
            return ecu_info
        except Exception as e:
            logger.error(f"Error getting ECU information from GM vehicle: {e}")
            return {}
    
    def test_connection(self, connection):
        """
        Test if the connection uses GM protocol
        
        Args:
            connection: The connection to test
            
        Returns:
            bool: True if the connection uses GM protocol, False otherwise
        """
        try:
            # Test connection using the underlying protocol
            if not self.protocol.test_connection(connection):
                return False
            
            # Try to read a GM-specific PID
            self.protocol.interface = connection
            data = self.protocol.read_data(self.pid_ecu_info)
            
            if data:
                return True
            
            return False
        except Exception as e:
            logger.debug(f"Error testing GM connection: {e}")
            return False
    
    def get_calibration_id(self):
        """
        Get the calibration ID
        
        Returns:
            str: The calibration ID
        """
        try:
            # Get calibration ID using GM-specific PID
            cal_id = self.read_data(self.pid_calibration_id)
            
            if isinstance(cal_id, dict) and 'calibration_id' in cal_id:
                return cal_id['calibration_id']
            
            return None
        except Exception as e:
            logger.error(f"Error getting calibration ID from GM vehicle: {e}")
            return None
    
    def get_cvn(self):
        """
        Get the Calibration Verification Number
        
        Returns:
            str: The CVN
        """
        try:
            # Get CVN using GM-specific PID
            cvn = self.read_data(self.pid_cvn)
            
            if isinstance(cvn, dict) and 'cvn' in cvn:
                return cvn['cvn']
            
            return None
        except Exception as e:
            logger.error(f"Error getting CVN from GM vehicle: {e}")
            return None
