#!/usr/bin/env python3
"""
Multi-ECU Manager
This module provides support for managing multiple ECUs in a vehicle.
"""

import logging
import time
from enum import Enum, auto
from typing import Dict, List, Optional, Any, Tuple

from protocols.protocol_handler import BaseProtocol, ProtocolType, SecurityLevel, SecurityAccessStatus, ProtocolError, CommunicationError
from diagnostic.session_manager import DiagnosticSession, SessionState

logger = logging.getLogger("diagnostic.multi_ecu_manager")

class ECUType(Enum):
    """ECU types"""
    ENGINE = auto()
    TRANSMISSION = auto()
    ABS = auto()
    AIRBAG = auto()
    BODY = auto()
    CLIMATE = auto()
    INSTRUMENT = auto()
    INFOTAINMENT = auto()
    STEERING = auto()
    SUSPENSION = auto()
    UNKNOWN = auto()

class ECUInfo:
    """ECU information"""
    
    def __init__(self, ecu_id: int, ecu_type: ECUType, name: str, description: str = ""):
        """
        Initialize ECU information
        
        Args:
            ecu_id (int): The ECU ID
            ecu_type (ECUType): The ECU type
            name (str): The ECU name
            description (str): The ECU description
        """
        self.ecu_id = ecu_id
        self.ecu_type = ecu_type
        self.name = name
        self.description = description
        self.hardware_id = ""
        self.software_id = ""
        self.calibration_id = ""
        self.protocol = None
        self.session = None
        self.supported_pids = []
        self.dtcs = []
        self.connected = False
    
    def __str__(self) -> str:
        """String representation"""
        return f"{self.name} (ID: {self.ecu_id:02X})"
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert to dictionary
        
        Returns:
            dict: Dictionary representation
        """
        return {
            'ecu_id': self.ecu_id,
            'ecu_type': self.ecu_type.name,
            'name': self.name,
            'description': self.description,
            'hardware_id': self.hardware_id,
            'software_id': self.software_id,
            'calibration_id': self.calibration_id,
            'protocol': self.protocol.get_protocol_info() if self.protocol else None,
            'supported_pids': self.supported_pids,
            'dtc_count': len(self.dtcs),
            'connected': self.connected
        }

class MultiECUManager:
    """Multi-ECU manager"""
    
    def __init__(self, interface=None):
        """
        Initialize the multi-ECU manager
        
        Args:
            interface: The hardware interface to use
        """
        self.interface = interface
        self.ecus: Dict[int, ECUInfo] = {}
        self.active_ecu: Optional[ECUInfo] = None
        self.auto_scan = True
        
        # Common ECU IDs
        self.common_ecus = {
            0x01: ECUInfo(0x01, ECUType.ENGINE, "Engine Control Module", "Controls engine functions"),
            0x02: ECUInfo(0x02, ECUType.TRANSMISSION, "Transmission Control Module", "Controls transmission functions"),
            0x03: ECUInfo(0x03, ECUType.ABS, "Anti-lock Brake System", "Controls ABS functions"),
            0x07: ECUInfo(0x07, ECUType.BODY, "Body Control Module", "Controls body functions"),
            0x08: ECUInfo(0x08, ECUType.CLIMATE, "Climate Control Module", "Controls HVAC functions"),
            0x09: ECUInfo(0x09, ECUType.INSTRUMENT, "Instrument Cluster", "Controls instrument panel"),
            0x10: ECUInfo(0x10, ECUType.INFOTAINMENT, "Infotainment System", "Controls entertainment functions"),
            0x12: ECUInfo(0x12, ECUType.AIRBAG, "Airbag Control Module", "Controls airbag functions"),
            0x16: ECUInfo(0x16, ECUType.STEERING, "Power Steering Control Module", "Controls power steering"),
            0x18: ECUInfo(0x18, ECUType.SUSPENSION, "Suspension Control Module", "Controls suspension functions")
        }
    
    def set_interface(self, interface):
        """
        Set the hardware interface
        
        Args:
            interface: The hardware interface to use
        """
        self.interface = interface
        
        # Update interface for all ECUs
        for ecu in self.ecus.values():
            if ecu.session:
                ecu.session.interface = interface
    
    def add_ecu(self, ecu_info: ECUInfo) -> bool:
        """
        Add an ECU
        
        Args:
            ecu_info (ECUInfo): The ECU information
            
        Returns:
            bool: True if added, False otherwise
        """
        if ecu_info.ecu_id in self.ecus:
            logger.warning(f"ECU with ID {ecu_info.ecu_id:02X} already exists")
            return False
        
        self.ecus[ecu_info.ecu_id] = ecu_info
        logger.info(f"Added ECU: {ecu_info}")
        return True
    
    def remove_ecu(self, ecu_id: int) -> bool:
        """
        Remove an ECU
        
        Args:
            ecu_id (int): The ECU ID
            
        Returns:
            bool: True if removed, False otherwise
        """
        if ecu_id not in self.ecus:
            logger.warning(f"ECU with ID {ecu_id:02X} does not exist")
            return False
        
        # Disconnect if connected
        if self.ecus[ecu_id].connected:
            self.disconnect_ecu(ecu_id)
        
        # Remove ECU
        ecu_info = self.ecus.pop(ecu_id)
        logger.info(f"Removed ECU: {ecu_info}")
        
        # Update active ECU if needed
        if self.active_ecu and self.active_ecu.ecu_id == ecu_id:
            self.active_ecu = None
        
        return True
    
    def get_ecu(self, ecu_id: int) -> Optional[ECUInfo]:
        """
        Get an ECU
        
        Args:
            ecu_id (int): The ECU ID
            
        Returns:
            ECUInfo: The ECU information or None if not found
        """
        return self.ecus.get(ecu_id)
    
    def get_all_ecus(self) -> List[ECUInfo]:
        """
        Get all ECUs
        
        Returns:
            list: List of ECU information
        """
        return list(self.ecus.values())
    
    def set_active_ecu(self, ecu_id: int) -> bool:
        """
        Set the active ECU
        
        Args:
            ecu_id (int): The ECU ID
            
        Returns:
            bool: True if set, False otherwise
        """
        if ecu_id not in self.ecus:
            logger.warning(f"ECU with ID {ecu_id:02X} does not exist")
            return False
        
        self.active_ecu = self.ecus[ecu_id]
        logger.info(f"Set active ECU: {self.active_ecu}")
        return True
    
    def scan_ecus(self) -> List[ECUInfo]:
        """
        Scan for ECUs
        
        Returns:
            list: List of found ECUs
        """
        if not self.interface:
            logger.error("No interface set")
            return []
        
        logger.info("Scanning for ECUs...")
        found_ecus = []
        
        # Try common ECU IDs
        for ecu_id, ecu_info in self.common_ecus.items():
            # Skip if already added
            if ecu_id in self.ecus:
                continue
            
            # Try to connect to the ECU
            try:
                # Create a diagnostic session
                session = DiagnosticSession(self.interface)
                session.set_target_ecu(ecu_id)
                
                # Try to connect
                if session.connect():
                    # Get ECU information
                    ecu_info.protocol = session.protocol
                    ecu_info.session = session
                    ecu_info.connected = True
                    
                    # Try to get hardware and software IDs
                    try:
                        ecu_data = session.get_ecu_info()
                        if ecu_data:
                            if 'hardware_id' in ecu_data:
                                ecu_info.hardware_id = ecu_data['hardware_id']
                            if 'software_id' in ecu_data:
                                ecu_info.software_id = ecu_data['software_id']
                    except Exception as e:
                        logger.debug(f"Error getting ECU information: {e}")
                    
                    # Add ECU
                    self.add_ecu(ecu_info)
                    found_ecus.append(ecu_info)
                    
                    # Disconnect
                    session.disconnect()
                    ecu_info.connected = False
            except Exception as e:
                logger.debug(f"Error scanning ECU {ecu_id:02X}: {e}")
        
        logger.info(f"Found {len(found_ecus)} ECUs")
        return found_ecus
    
    def connect_ecu(self, ecu_id: int) -> bool:
        """
        Connect to an ECU
        
        Args:
            ecu_id (int): The ECU ID
            
        Returns:
            bool: True if connected, False otherwise
        """
        if ecu_id not in self.ecus:
            logger.warning(f"ECU with ID {ecu_id:02X} does not exist")
            return False
        
        ecu_info = self.ecus[ecu_id]
        
        # Check if already connected
        if ecu_info.connected:
            logger.info(f"ECU {ecu_info} already connected")
            return True
        
        # Connect to the ECU
        try:
            # Create a diagnostic session if needed
            if not ecu_info.session:
                ecu_info.session = DiagnosticSession(self.interface)
                ecu_info.session.set_target_ecu(ecu_id)
            
            # Connect
            if ecu_info.session.connect():
                ecu_info.connected = True
                ecu_info.protocol = ecu_info.session.protocol
                
                # Set as active ECU
                self.active_ecu = ecu_info
                
                logger.info(f"Connected to ECU: {ecu_info}")
                return True
            else:
                logger.error(f"Failed to connect to ECU: {ecu_info}")
                return False
        except Exception as e:
            logger.error(f"Error connecting to ECU {ecu_info}: {e}")
            return False
    
    def disconnect_ecu(self, ecu_id: int) -> bool:
        """
        Disconnect from an ECU
        
        Args:
            ecu_id (int): The ECU ID
            
        Returns:
            bool: True if disconnected, False otherwise
        """
        if ecu_id not in self.ecus:
            logger.warning(f"ECU with ID {ecu_id:02X} does not exist")
            return False
        
        ecu_info = self.ecus[ecu_id]
        
        # Check if connected
        if not ecu_info.connected:
            logger.info(f"ECU {ecu_info} not connected")
            return True
        
        # Disconnect from the ECU
        try:
            if ecu_info.session:
                ecu_info.session.disconnect()
            
            ecu_info.connected = False
            
            # Update active ECU if needed
            if self.active_ecu and self.active_ecu.ecu_id == ecu_id:
                self.active_ecu = None
            
            logger.info(f"Disconnected from ECU: {ecu_info}")
            return True
        except Exception as e:
            logger.error(f"Error disconnecting from ECU {ecu_info}: {e}")
            return False
    
    def disconnect_all(self) -> bool:
        """
        Disconnect from all ECUs
        
        Returns:
            bool: True if all disconnected, False otherwise
        """
        success = True
        
        for ecu_id in list(self.ecus.keys()):
            if not self.disconnect_ecu(ecu_id):
                success = False
        
        self.active_ecu = None
        return success
    
    def read_dtc(self, ecu_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Read DTCs from an ECU
        
        Args:
            ecu_id (int): The ECU ID (optional, uses active ECU if None)
            
        Returns:
            list: List of DTCs
        """
        # Determine ECU to use
        if ecu_id is None:
            if not self.active_ecu:
                logger.error("No active ECU")
                return []
            ecu_info = self.active_ecu
        else:
            if ecu_id not in self.ecus:
                logger.warning(f"ECU with ID {ecu_id:02X} does not exist")
                return []
            ecu_info = self.ecus[ecu_id]
        
        # Check if connected
        if not ecu_info.connected:
            logger.warning(f"ECU {ecu_info} not connected")
            return []
        
        # Read DTCs
        try:
            dtcs = ecu_info.session.read_dtc()
            
            # Store DTCs in ECU info
            ecu_info.dtcs = dtcs
            
            logger.info(f"Read {len(dtcs)} DTCs from ECU: {ecu_info}")
            return dtcs
        except Exception as e:
            logger.error(f"Error reading DTCs from ECU {ecu_info}: {e}")
            return []
    
    def clear_dtc(self, ecu_id: Optional[int] = None) -> bool:
        """
        Clear DTCs from an ECU
        
        Args:
            ecu_id (int): The ECU ID (optional, uses active ECU if None)
            
        Returns:
            bool: True if cleared, False otherwise
        """
        # Determine ECU to use
        if ecu_id is None:
            if not self.active_ecu:
                logger.error("No active ECU")
                return False
            ecu_info = self.active_ecu
        else:
            if ecu_id not in self.ecus:
                logger.warning(f"ECU with ID {ecu_id:02X} does not exist")
                return False
            ecu_info = self.ecus[ecu_id]
        
        # Check if connected
        if not ecu_info.connected:
            logger.warning(f"ECU {ecu_info} not connected")
            return False
        
        # Clear DTCs
        try:
            success = ecu_info.session.clear_dtc()
            
            if success:
                # Clear stored DTCs
                ecu_info.dtcs = []
                logger.info(f"Cleared DTCs from ECU: {ecu_info}")
            else:
                logger.error(f"Failed to clear DTCs from ECU: {ecu_info}")
            
            return success
        except Exception as e:
            logger.error(f"Error clearing DTCs from ECU {ecu_info}: {e}")
            return False
    
    def read_data(self, pid: Any, ecu_id: Optional[int] = None) -> Any:
        """
        Read data from an ECU
        
        Args:
            pid: The parameter ID
            ecu_id (int): The ECU ID (optional, uses active ECU if None)
            
        Returns:
            The data read from the ECU
        """
        # Determine ECU to use
        if ecu_id is None:
            if not self.active_ecu:
                logger.error("No active ECU")
                return None
            ecu_info = self.active_ecu
        else:
            if ecu_id not in self.ecus:
                logger.warning(f"ECU with ID {ecu_id:02X} does not exist")
                return None
            ecu_info = self.ecus[ecu_id]
        
        # Check if connected
        if not ecu_info.connected:
            logger.warning(f"ECU {ecu_info} not connected")
            return None
        
        # Read data
        try:
            data = ecu_info.session.read_data(pid)
            return data
        except Exception as e:
            logger.error(f"Error reading data from ECU {ecu_info}: {e}")
            return None
    
    def write_data(self, pid: Any, data: Any, ecu_id: Optional[int] = None) -> bool:
        """
        Write data to an ECU
        
        Args:
            pid: The parameter ID
            data: The data to write
            ecu_id (int): The ECU ID (optional, uses active ECU if None)
            
        Returns:
            bool: True if written, False otherwise
        """
        # Determine ECU to use
        if ecu_id is None:
            if not self.active_ecu:
                logger.error("No active ECU")
                return False
            ecu_info = self.active_ecu
        else:
            if ecu_id not in self.ecus:
                logger.warning(f"ECU with ID {ecu_id:02X} does not exist")
                return False
            ecu_info = self.ecus[ecu_id]
        
        # Check if connected
        if not ecu_info.connected:
            logger.warning(f"ECU {ecu_info} not connected")
            return False
        
        # Write data
        try:
            success = ecu_info.session.write_data(pid, data)
            return success
        except Exception as e:
            logger.error(f"Error writing data to ECU {ecu_info}: {e}")
            return False
