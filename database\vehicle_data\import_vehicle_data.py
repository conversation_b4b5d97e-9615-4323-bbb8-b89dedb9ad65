#!/usr/bin/env python3
"""
Vehicle Data Import Script
This script imports vehicle data from JSON files and adds it to the database.
"""

import os
import json
import sys
import argparse

# Add parent directory to path to import database modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from vehicle_db import VehicleDatabase, Make, Model, Vehicle

def import_vehicle_data(db, file_path):
    """
    Import vehicle data from a JSON file
    
    Args:
        db (VehicleDatabase): The vehicle database
        file_path (str): Path to the JSON file
        
    Returns:
        int: Number of vehicles imported
    """
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        session = db.Session()
        vehicles_imported = 0
        
        for make_name, models in data.items():
            # Check if make exists
            make = session.query(Make).filter(Make.name == make_name).first()
            
            if not make:
                # Create new make
                make = Make(name=make_name)
                session.add(make)
                session.flush()
            
            for model_data in models:
                model_name = model_data["model"]
                
                # Check if model exists
                model = session.query(Model).filter(
                    Model.name == model_name,
                    Model.make_id == make.id
                ).first()
                
                if not model:
                    # Create new model
                    model = Model(name=model_name, make_id=make.id)
                    session.add(model)
                    session.flush()
                
                # Process variants and years
                variants = model_data.get("variants", [model_name])
                years_data = model_data.get("years", {})
                
                # Flatten years from all generations
                all_years = []
                for generation, years in years_data.items():
                    all_years.extend(years)
                
                # If no years specified, use a default range
                if not all_years:
                    all_years = list(range(2000, 2024))
                
                # Get other vehicle data
                engine_types = model_data.get("engine_types", ["Unknown"])
                fuel_types = model_data.get("fuel_types", ["Unknown"])
                transmission_types = model_data.get("transmission_types", ["Unknown"])
                obd_protocols = model_data.get("obd_protocols", ["ISO15765-4 (CAN)"])
                manufacturer_protocols = model_data.get("manufacturer_protocols", [])
                
                # Create vehicles for each variant and year
                for variant in variants:
                    for year in all_years:
                        # Check if vehicle already exists
                        existing_vehicle = session.query(Vehicle).filter(
                            Vehicle.model_id == model.id,
                            Vehicle.year == year,
                            Vehicle.engine_type.like(f"%{variant}%")
                        ).first()
                        
                        if not existing_vehicle:
                            # Create new vehicle
                            vehicle = Vehicle(
                                model_id=model.id,
                                year=year,
                                engine_type=f"{variant} {engine_types[0]}",
                                engine_displacement=2.0,  # Default value
                                fuel_type=fuel_types[0],
                                transmission_type=transmission_types[0],
                                num_cylinders=4,  # Default value
                                obd_protocol=obd_protocols[0],
                                manufacturer_protocol=manufacturer_protocols[0] if manufacturer_protocols else None
                            )
                            session.add(vehicle)
                            vehicles_imported += 1
        
        session.commit()
        session.close()
        
        return vehicles_imported
    except Exception as e:
        print(f"Error importing vehicle data from {file_path}: {e}")
        session.rollback()
        session.close()
        return 0

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Import vehicle data from JSON files')
    parser.add_argument('--all', action='store_true', help='Import all vehicle data')
    parser.add_argument('--bmw', action='store_true', help='Import BMW vehicle data')
    parser.add_argument('--vw', action='store_true', help='Import VW vehicle data')
    parser.add_argument('--mercedes', action='store_true', help='Import Mercedes vehicle data')
    parser.add_argument('--audi', action='store_true', help='Import Audi vehicle data')
    parser.add_argument('--nissan', action='store_true', help='Import Nissan vehicle data')
    
    args = parser.parse_args()
    
    # Initialize database
    db = VehicleDatabase()
    
    # Get the directory containing the JSON files
    data_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Import vehicle data
    total_imported = 0
    
    if args.all or args.bmw:
        file_path = os.path.join(data_dir, 'bmw_models.json')
        if os.path.exists(file_path):
            imported = import_vehicle_data(db, file_path)
            print(f"Imported {imported} BMW vehicles")
            total_imported += imported
    
    if args.all or args.vw:
        file_path = os.path.join(data_dir, 'vw_models.json')
        if os.path.exists(file_path):
            imported = import_vehicle_data(db, file_path)
            print(f"Imported {imported} VW vehicles")
            total_imported += imported
    
    if args.all or args.mercedes:
        file_path = os.path.join(data_dir, 'mercedes_models.json')
        if os.path.exists(file_path):
            imported = import_vehicle_data(db, file_path)
            print(f"Imported {imported} Mercedes vehicles")
            total_imported += imported
    
    if args.all or args.audi:
        file_path = os.path.join(data_dir, 'audi_models.json')
        if os.path.exists(file_path):
            imported = import_vehicle_data(db, file_path)
            print(f"Imported {imported} Audi vehicles")
            total_imported += imported
    
    if args.all or args.nissan:
        file_path = os.path.join(data_dir, 'nissan_models.json')
        if os.path.exists(file_path):
            imported = import_vehicle_data(db, file_path)
            print(f"Imported {imported} Nissan vehicles")
            total_imported += imported
    
    if total_imported > 0:
        print(f"Total vehicles imported: {total_imported}")
    else:
        if not any(vars(args).values()):
            parser.print_help()
        else:
            print("No vehicles imported. Check if the JSON files exist.")

if __name__ == "__main__":
    main()
