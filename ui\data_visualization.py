"""
Data Visualization Module
Provides visualization tools for OBD data
"""

import time
import numpy as np
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QCheckBox, QGroupBox, QSplitter, QFrame
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QColor

import matplotlib
matplotlib.use('Qt5Agg')
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.backends.backend_qt5agg import NavigationToolbar2QT as NavigationToolbar
from matplotlib.figure import Figure
import matplotlib.pyplot as plt
import matplotlib.animation as animation

class MplCanvas(FigureCanvas):
    """Matplotlib canvas for embedding in Qt"""
    
    def __init__(self, parent=None, width=5, height=4, dpi=100):
        """
        Initialize the canvas
        
        Args:
            parent: The parent widget
            width (int): The width in inches
            height (int): The height in inches
            dpi (int): The resolution in dots per inch
        """
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        self.axes = self.fig.add_subplot(111)
        super(MplCanvas, self).__init__(self.fig)

class RealTimeGraph(QWidget):
    """Real-time graph widget"""
    
    def __init__(self, parent=None):
        """
        Initialize the real-time graph
        
        Args:
            parent: The parent widget
        """
        super().__init__(parent)
        
        # Data
        self.data_sources = {}
        self.active_sources = []
        self.max_points = 100
        self.update_interval = 1000  # ms
        
        # Setup UI
        self.init_ui()
    
    def init_ui(self):
        """Initialize the user interface"""
        # Create layout
        layout = QVBoxLayout(self)
        
        # Create controls
        controls_layout = QHBoxLayout()
        
        # Data source selection
        self.source_combo = QComboBox()
        self.source_combo.setEditable(False)
        
        self.add_source_button = QPushButton("Add")
        self.add_source_button.clicked.connect(self.add_data_source)
        
        # Update interval
        self.interval_combo = QComboBox()
        self.interval_combo.addItems(["0.5s", "1s", "2s", "5s"])
        self.interval_combo.setCurrentIndex(1)  # Default to 1s
        self.interval_combo.currentIndexChanged.connect(self.set_update_interval)
        
        # Start/stop button
        self.start_button = QPushButton("Start")
        self.start_button.setCheckable(True)
        self.start_button.clicked.connect(self.toggle_graph)
        
        # Add widgets to controls layout
        controls_layout.addWidget(QLabel("Data:"))
        controls_layout.addWidget(self.source_combo)
        controls_layout.addWidget(self.add_source_button)
        controls_layout.addWidget(QLabel("Interval:"))
        controls_layout.addWidget(self.interval_combo)
        controls_layout.addWidget(self.start_button)
        controls_layout.addStretch()
        
        layout.addLayout(controls_layout)
        
        # Create active sources group
        self.active_sources_group = QGroupBox("Active Data Sources")
        self.active_sources_layout = QVBoxLayout(self.active_sources_group)
        
        layout.addWidget(self.active_sources_group)
        
        # Create graph
        self.canvas = MplCanvas(self, width=5, height=4, dpi=100)
        self.toolbar = NavigationToolbar(self.canvas, self)
        
        layout.addWidget(self.toolbar)
        layout.addWidget(self.canvas)
        
        # Create timer for updating the graph
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_graph)
    
    def set_data_sources(self, sources):
        """
        Set the available data sources
        
        Args:
            sources (dict): Dictionary of data sources
        """
        self.data_sources = sources
        self.source_combo.clear()
        self.source_combo.addItems(sources.keys())
    
    def add_data_source(self):
        """Add a data source to the graph"""
        source = self.source_combo.currentText()
        
        if not source or source in self.active_sources:
            return
        
        # Add to active sources
        self.active_sources.append(source)
        
        # Create a checkbox for the source
        source_layout = QHBoxLayout()
        
        checkbox = QCheckBox(source)
        checkbox.setChecked(True)
        checkbox.stateChanged.connect(self.update_graph)
        
        color_label = QLabel()
        color_label.setFixedSize(16, 16)
        color_label.setStyleSheet(f"background-color: {self._get_color(len(self.active_sources) - 1)}")
        color_label.setFrameShape(QFrame.Box)
        
        remove_button = QPushButton("Remove")
        remove_button.clicked.connect(lambda: self.remove_data_source(source))
        
        source_layout.addWidget(color_label)
        source_layout.addWidget(checkbox)
        source_layout.addStretch()
        source_layout.addWidget(remove_button)
        
        self.active_sources_layout.addLayout(source_layout)
        
        # Initialize data for the source
        if not hasattr(self, 'x_data'):
            self.x_data = np.linspace(0, self.max_points - 1, self.max_points)
            self.y_data = {}
        
        self.y_data[source] = np.zeros(self.max_points)
        
        # Update the graph
        self.update_graph()
    
    def remove_data_source(self, source):
        """
        Remove a data source from the graph
        
        Args:
            source (str): The data source to remove
        """
        if source in self.active_sources:
            # Remove from active sources
            index = self.active_sources.index(source)
            self.active_sources.remove(source)
            
            # Remove the layout
            layout = self.active_sources_layout.itemAt(index)
            self._remove_layout(layout)
            
            # Remove data
            if source in self.y_data:
                del self.y_data[source]
            
            # Update the graph
            self.update_graph()
    
    def _remove_layout(self, layout):
        """
        Remove a layout and its widgets
        
        Args:
            layout: The layout to remove
        """
        if layout is not None:
            while layout.count():
                item = layout.takeAt(0)
                widget = item.widget()
                if widget is not None:
                    widget.deleteLater()
                else:
                    self._remove_layout(item.layout())
            self.active_sources_layout.removeItem(layout)
    
    def set_update_interval(self, index):
        """
        Set the update interval
        
        Args:
            index (int): The index of the selected interval
        """
        intervals = [500, 1000, 2000, 5000]
        self.update_interval = intervals[index]
        
        if self.timer.isActive():
            self.timer.stop()
            self.timer.start(self.update_interval)
    
    def toggle_graph(self, checked):
        """
        Toggle the graph on/off
        
        Args:
            checked (bool): Whether the button is checked
        """
        if checked:
            self.start_button.setText("Stop")
            self.timer.start(self.update_interval)
        else:
            self.start_button.setText("Start")
            self.timer.stop()
    
    def update_graph(self):
        """Update the graph with new data"""
        # Clear the axes
        self.canvas.axes.clear()
        
        # Get active checkboxes
        active_checkboxes = []
        for i in range(len(self.active_sources)):
            layout = self.active_sources_layout.itemAt(i)
            if layout:
                checkbox = layout.itemAt(1).widget()
                if checkbox and checkbox.isChecked():
                    active_checkboxes.append(checkbox.text())
        
        # Plot each active data source
        for i, source in enumerate(self.active_sources):
            if source in active_checkboxes and source in self.y_data:
                self.canvas.axes.plot(
                    self.x_data, self.y_data[source],
                    label=source, color=self._get_color(i)
                )
        
        # Add legend and grid
        self.canvas.axes.legend(loc='upper right')
        self.canvas.axes.grid(True)
        
        # Redraw the canvas
        self.canvas.draw()
    
    def add_data_point(self, source, value):
        """
        Add a data point to a source
        
        Args:
            source (str): The data source
            value (float): The data value
        """
        if source in self.y_data:
            # Shift data to the left
            self.y_data[source] = np.roll(self.y_data[source], -1)
            # Add new value at the end
            self.y_data[source][-1] = value
    
    def _get_color(self, index):
        """
        Get a color for a data source
        
        Args:
            index (int): The index of the data source
            
        Returns:
            str: The color as a hex string
        """
        colors = [
            '#1f77b4',  # blue
            '#ff7f0e',  # orange
            '#2ca02c',  # green
            '#d62728',  # red
            '#9467bd',  # purple
            '#8c564b',  # brown
            '#e377c2',  # pink
            '#7f7f7f',  # gray
            '#bcbd22',  # olive
            '#17becf'   # cyan
        ]
        return colors[index % len(colors)]

class GaugeWidget(QWidget):
    """Gauge widget for displaying a single value"""
    
    def __init__(self, parent=None):
        """
        Initialize the gauge widget
        
        Args:
            parent: The parent widget
        """
        super().__init__(parent)
        
        # Data
        self.title = "Gauge"
        self.value = 0
        self.min_value = 0
        self.max_value = 100
        self.units = ""
        self.color = "#1f77b4"
        
        # Setup UI
        self.init_ui()
    
    def init_ui(self):
        """Initialize the user interface"""
        # Create layout
        layout = QVBoxLayout(self)
        
        # Create gauge
        self.canvas = MplCanvas(self, width=3, height=3, dpi=100)
        self.canvas.axes.set_aspect('equal')
        self.canvas.axes.set_facecolor('none')
        
        layout.addWidget(self.canvas)
        
        # Create value label
        self.value_label = QLabel("0")
        self.value_label.setAlignment(Qt.AlignCenter)
        self.value_label.setStyleSheet("font-size: 24pt; font-weight: bold;")
        
        layout.addWidget(self.value_label)
        
        # Create title label
        self.title_label = QLabel(self.title)
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setStyleSheet("font-size: 12pt;")
        
        layout.addWidget(self.title_label)
        
        # Draw the initial gauge
        self.draw_gauge()
    
    def set_title(self, title):
        """
        Set the gauge title
        
        Args:
            title (str): The gauge title
        """
        self.title = title
        self.title_label.setText(title)
    
    def set_range(self, min_value, max_value):
        """
        Set the gauge range
        
        Args:
            min_value (float): The minimum value
            max_value (float): The maximum value
        """
        self.min_value = min_value
        self.max_value = max_value
        self.draw_gauge()
    
    def set_units(self, units):
        """
        Set the gauge units
        
        Args:
            units (str): The units
        """
        self.units = units
        self.draw_gauge()
    
    def set_color(self, color):
        """
        Set the gauge color
        
        Args:
            color (str): The color
        """
        self.color = color
        self.draw_gauge()
    
    def set_value(self, value):
        """
        Set the gauge value
        
        Args:
            value (float): The value
        """
        self.value = value
        self.value_label.setText(f"{value:.1f} {self.units}")
        self.draw_gauge()
    
    def draw_gauge(self):
        """Draw the gauge"""
        # Clear the axes
        self.canvas.axes.clear()
        
        # Calculate the angle based on the value
        angle = np.pi * (1 - (self.value - self.min_value) / (self.max_value - self.min_value))
        
        # Draw the gauge background
        self.canvas.axes.add_patch(plt.Circle((0, 0), 1, fill=False, linewidth=2, color='gray'))
        
        # Draw the gauge value
        self.canvas.axes.add_patch(plt.Wedge((0, 0), 1, 180, 180 + (180 - angle * 180 / np.pi), 
                                            width=0.2, color=self.color))
        
        # Draw the gauge ticks
        for i in range(11):
            angle_tick = np.pi * (1 - i / 10)
            x1 = 0.8 * np.cos(angle_tick)
            y1 = 0.8 * np.sin(angle_tick)
            x2 = 0.95 * np.cos(angle_tick)
            y2 = 0.95 * np.sin(angle_tick)
            self.canvas.axes.plot([x1, x2], [y1, y2], color='black', linewidth=2)
            
            # Add tick labels
            if i % 2 == 0:
                x3 = 0.7 * np.cos(angle_tick)
                y3 = 0.7 * np.sin(angle_tick)
                value = self.min_value + (self.max_value - self.min_value) * i / 10
                self.canvas.axes.text(x3, y3, f"{value:.0f}", 
                                    horizontalalignment='center', 
                                    verticalalignment='center')
        
        # Set limits
        self.canvas.axes.set_xlim(-1.1, 1.1)
        self.canvas.axes.set_ylim(-0.1, 1.1)
        
        # Remove axes
        self.canvas.axes.axis('off')
        
        # Redraw the canvas
        self.canvas.draw()

class DashboardWidget(QWidget):
    """Dashboard widget for displaying multiple gauges"""
    
    def __init__(self, parent=None):
        """
        Initialize the dashboard widget
        
        Args:
            parent: The parent widget
        """
        super().__init__(parent)
        
        # Data
        self.gauges = {}
        
        # Setup UI
        self.init_ui()
    
    def init_ui(self):
        """Initialize the user interface"""
        # Create layout
        self.layout = QHBoxLayout(self)
    
    def add_gauge(self, name, title, min_value=0, max_value=100, units="", color="#1f77b4"):
        """
        Add a gauge to the dashboard
        
        Args:
            name (str): The gauge name
            title (str): The gauge title
            min_value (float): The minimum value
            max_value (float): The maximum value
            units (str): The units
            color (str): The color
            
        Returns:
            GaugeWidget: The created gauge
        """
        gauge = GaugeWidget(self)
        gauge.set_title(title)
        gauge.set_range(min_value, max_value)
        gauge.set_units(units)
        gauge.set_color(color)
        
        self.layout.addWidget(gauge)
        self.gauges[name] = gauge
        
        return gauge
    
    def set_gauge_value(self, name, value):
        """
        Set a gauge value
        
        Args:
            name (str): The gauge name
            value (float): The value
        """
        if name in self.gauges:
            self.gauges[name].set_value(value)
    
    def get_gauge(self, name):
        """
        Get a gauge by name
        
        Args:
            name (str): The gauge name
            
        Returns:
            GaugeWidget: The gauge or None if not found
        """
        return self.gauges.get(name)
