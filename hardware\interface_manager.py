#!/usr/bin/env python3
"""
Hardware Interface Manager
This module provides a manager for hardware interfaces.
"""

import logging
import time
import serial
import serial.tools.list_ports
from enum import Enum, auto
from typing import Dict, List, Optional, Any, Tuple

from hardware.elm327 import ELM327Interface
from hardware.j2534 import J2534Interface
from hardware.can_interface import CANInterface

logger = logging.getLogger("hardware.interface_manager")

class InterfaceType(Enum):
    """Hardware interface types"""
    ELM327 = auto()
    J2534 = auto()
    CAN = auto()
    UNKNOWN = auto()

class InterfaceInfo:
    """Hardware interface information"""
    
    def __init__(self, interface_id: str, interface_type: InterfaceType, name: str, description: str = ""):
        """
        Initialize interface information
        
        Args:
            interface_id (str): The interface ID
            interface_type (InterfaceType): The interface type
            name (str): The interface name
            description (str): The interface description
        """
        self.interface_id = interface_id
        self.interface_type = interface_type
        self.name = name
        self.description = description
        self.interface = None
        self.connected = False
        self.firmware_version = ""
        self.hardware_version = ""
        self.supported_protocols = []
    
    def __str__(self) -> str:
        """String representation"""
        return f"{self.name} ({self.interface_id})"
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert to dictionary
        
        Returns:
            dict: Dictionary representation
        """
        return {
            'interface_id': self.interface_id,
            'interface_type': self.interface_type.name,
            'name': self.name,
            'description': self.description,
            'firmware_version': self.firmware_version,
            'hardware_version': self.hardware_version,
            'supported_protocols': self.supported_protocols,
            'connected': self.connected
        }

class InterfaceManager:
    """Hardware interface manager"""
    
    def __init__(self):
        """Initialize the interface manager"""
        self.interfaces: Dict[str, InterfaceInfo] = {}
        self.active_interface: Optional[InterfaceInfo] = None
        self.auto_detect = True
    
    def add_interface(self, interface_info: InterfaceInfo) -> bool:
        """
        Add an interface
        
        Args:
            interface_info (InterfaceInfo): The interface information
            
        Returns:
            bool: True if added, False otherwise
        """
        if interface_info.interface_id in self.interfaces:
            logger.warning(f"Interface with ID {interface_info.interface_id} already exists")
            return False
        
        self.interfaces[interface_info.interface_id] = interface_info
        logger.info(f"Added interface: {interface_info}")
        return True
    
    def remove_interface(self, interface_id: str) -> bool:
        """
        Remove an interface
        
        Args:
            interface_id (str): The interface ID
            
        Returns:
            bool: True if removed, False otherwise
        """
        if interface_id not in self.interfaces:
            logger.warning(f"Interface with ID {interface_id} does not exist")
            return False
        
        # Disconnect if connected
        if self.interfaces[interface_id].connected:
            self.disconnect_interface(interface_id)
        
        # Remove interface
        interface_info = self.interfaces.pop(interface_id)
        logger.info(f"Removed interface: {interface_info}")
        
        # Update active interface if needed
        if self.active_interface and self.active_interface.interface_id == interface_id:
            self.active_interface = None
        
        return True
    
    def get_interface(self, interface_id: str) -> Optional[InterfaceInfo]:
        """
        Get an interface
        
        Args:
            interface_id (str): The interface ID
            
        Returns:
            InterfaceInfo: The interface information or None if not found
        """
        return self.interfaces.get(interface_id)
    
    def get_all_interfaces(self) -> List[InterfaceInfo]:
        """
        Get all interfaces
        
        Returns:
            list: List of interface information
        """
        return list(self.interfaces.values())
    
    def set_active_interface(self, interface_id: str) -> bool:
        """
        Set the active interface
        
        Args:
            interface_id (str): The interface ID
            
        Returns:
            bool: True if set, False otherwise
        """
        if interface_id not in self.interfaces:
            logger.warning(f"Interface with ID {interface_id} does not exist")
            return False
        
        self.active_interface = self.interfaces[interface_id]
        logger.info(f"Set active interface: {self.active_interface}")
        return True
    
    def detect_interfaces(self) -> List[InterfaceInfo]:
        """
        Detect available interfaces
        
        Returns:
            list: List of detected interfaces
        """
        logger.info("Detecting interfaces...")
        detected_interfaces = []
        
        # Detect serial ports
        try:
            for port in serial.tools.list_ports.comports():
                # Skip if already added
                if port.device in self.interfaces:
                    continue
                
                # Try to identify the interface type
                interface_type = InterfaceType.UNKNOWN
                interface_name = "Unknown Interface"
                
                # Check if it's an ELM327 interface
                if "ELM327" in port.description or "OBD" in port.description:
                    interface_type = InterfaceType.ELM327
                    interface_name = "ELM327 Interface"
                # Check if it's a J2534 interface
                elif "J2534" in port.description or "PassThru" in port.description:
                    interface_type = InterfaceType.J2534
                    interface_name = "J2534 Interface"
                # Check if it's a CAN interface
                elif "CAN" in port.description:
                    interface_type = InterfaceType.CAN
                    interface_name = "CAN Interface"
                
                # Create interface information
                interface_info = InterfaceInfo(
                    interface_id=port.device,
                    interface_type=interface_type,
                    name=interface_name,
                    description=port.description
                )
                
                # Add interface
                self.add_interface(interface_info)
                detected_interfaces.append(interface_info)
        except Exception as e:
            logger.error(f"Error detecting serial ports: {e}")
        
        logger.info(f"Detected {len(detected_interfaces)} interfaces")
        return detected_interfaces
    
    def connect_interface(self, interface_id: str) -> bool:
        """
        Connect to an interface
        
        Args:
            interface_id (str): The interface ID
            
        Returns:
            bool: True if connected, False otherwise
        """
        if interface_id not in self.interfaces:
            logger.warning(f"Interface with ID {interface_id} does not exist")
            return False
        
        interface_info = self.interfaces[interface_id]
        
        # Check if already connected
        if interface_info.connected:
            logger.info(f"Interface {interface_info} already connected")
            return True
        
        # Connect to the interface
        try:
            # Create interface if needed
            if not interface_info.interface:
                if interface_info.interface_type == InterfaceType.ELM327:
                    interface_info.interface = ELM327Interface(port=interface_id)
                elif interface_info.interface_type == InterfaceType.J2534:
                    interface_info.interface = J2534Interface(port=interface_id)
                elif interface_info.interface_type == InterfaceType.CAN:
                    interface_info.interface = CANInterface(port=interface_id)
                else:
                    # Try ELM327 as a fallback
                    interface_info.interface = ELM327Interface(port=interface_id)
            
            # Connect
            if interface_info.interface.connect():
                interface_info.connected = True
                
                # Get interface information
                try:
                    interface_info.firmware_version = interface_info.interface.version
                    interface_info.supported_protocols = interface_info.interface.get_supported_protocols()
                except Exception as e:
                    logger.debug(f"Error getting interface information: {e}")
                
                # Set as active interface
                self.active_interface = interface_info
                
                logger.info(f"Connected to interface: {interface_info}")
                return True
            else:
                logger.error(f"Failed to connect to interface: {interface_info}")
                return False
        except Exception as e:
            logger.error(f"Error connecting to interface {interface_info}: {e}")
            return False
    
    def disconnect_interface(self, interface_id: str) -> bool:
        """
        Disconnect from an interface
        
        Args:
            interface_id (str): The interface ID
            
        Returns:
            bool: True if disconnected, False otherwise
        """
        if interface_id not in self.interfaces:
            logger.warning(f"Interface with ID {interface_id} does not exist")
            return False
        
        interface_info = self.interfaces[interface_id]
        
        # Check if connected
        if not interface_info.connected:
            logger.info(f"Interface {interface_info} not connected")
            return True
        
        # Disconnect from the interface
        try:
            if interface_info.interface:
                interface_info.interface.disconnect()
            
            interface_info.connected = False
            
            # Update active interface if needed
            if self.active_interface and self.active_interface.interface_id == interface_id:
                self.active_interface = None
            
            logger.info(f"Disconnected from interface: {interface_info}")
            return True
        except Exception as e:
            logger.error(f"Error disconnecting from interface {interface_info}: {e}")
            return False
    
    def disconnect_all(self) -> bool:
        """
        Disconnect from all interfaces
        
        Returns:
            bool: True if all disconnected, False otherwise
        """
        success = True
        
        for interface_id in list(self.interfaces.keys()):
            if not self.disconnect_interface(interface_id):
                success = False
        
        self.active_interface = None
        return success
    
    def get_active_interface(self) -> Optional[Any]:
        """
        Get the active interface
        
        Returns:
            The active interface or None if not set
        """
        if not self.active_interface or not self.active_interface.connected:
            return None
        
        return self.active_interface.interface
    
    def update_firmware(self, interface_id: str, firmware_file: str) -> bool:
        """
        Update interface firmware
        
        Args:
            interface_id (str): The interface ID
            firmware_file (str): The firmware file path
            
        Returns:
            bool: True if updated, False otherwise
        """
        if interface_id not in self.interfaces:
            logger.warning(f"Interface with ID {interface_id} does not exist")
            return False
        
        interface_info = self.interfaces[interface_id]
        
        # Check if connected
        if not interface_info.connected:
            logger.warning(f"Interface {interface_info} not connected")
            return False
        
        # Update firmware
        try:
            # Check if the interface supports firmware updates
            if hasattr(interface_info.interface, 'update_firmware'):
                success = interface_info.interface.update_firmware(firmware_file)
                
                if success:
                    logger.info(f"Updated firmware for interface: {interface_info}")
                    
                    # Reconnect to get updated information
                    self.disconnect_interface(interface_id)
                    self.connect_interface(interface_id)
                else:
                    logger.error(f"Failed to update firmware for interface: {interface_info}")
                
                return success
            else:
                logger.error(f"Interface {interface_info} does not support firmware updates")
                return False
        except Exception as e:
            logger.error(f"Error updating firmware for interface {interface_info}: {e}")
            return False
