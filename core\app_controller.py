"""
Application Controller
Manages the application's core functionality and coordinates between components
"""

from database.vehicle_db import VehicleDatabase
from database.dtc_db import DTCDatabase
from hardware.obd_interface import OBDInterface
from protocols.protocol_handler import ProtocolHandler

class AppController:
    """Main application controller that coordinates all components"""

    def __init__(self):
        """Initialize the application controller"""
        self.vehicle_db = VehicleDatabase()
        self.dtc_db = DTCDatabase()
        self.protocol_handler = ProtocolHandler()
        self.obd_interface = OBDInterface(self.protocol_handler)
        self.connected = False
        self.current_vehicle = None

    def connect_to_vehicle(self, port, protocol=None):
        """
        Connect to a vehicle using the specified port and protocol

        Args:
            port (str): The serial port to connect to
            protocol (str, optional): The protocol to use. If None, auto-detect.

        Returns:
            bool: True if connection successful, False otherwise
        """
        self.connected = self.obd_interface.connect(port, protocol)
        return self.connected

    def disconnect(self):
        """Disconnect from the vehicle"""
        if self.connected:
            self.obd_interface.disconnect()
            self.connected = False

    def get_available_ports(self):
        """
        Get a list of available serial ports

        Returns:
            list: List of available serial ports
        """
        return self.obd_interface.get_available_ports()

    def get_vehicle_makes(self):
        """
        Get a list of all vehicle makes in the database

        Returns:
            list: List of vehicle makes
        """
        return self.vehicle_db.get_makes()

    def get_vehicle_models(self, make):
        """
        Get a list of vehicle models for a specific make

        Args:
            make (str): The vehicle make

        Returns:
            list: List of vehicle models
        """
        return self.vehicle_db.get_models(make)

    def set_current_vehicle(self, make, model, year):
        """
        Set the current vehicle

        Args:
            make (str): The vehicle make
            model (str): The vehicle model
            year (int): The vehicle year

        Returns:
            bool: True if vehicle found in database, False otherwise
        """
        self.current_vehicle = self.vehicle_db.get_vehicle(make, model, year)
        return self.current_vehicle is not None

    def read_dtc_codes(self):
        """
        Read Diagnostic Trouble Codes from the vehicle

        Returns:
            list: List of DTC codes with descriptions from the database
        """
        if not self.connected:
            return []

        # Get DTC codes from the vehicle
        dtc_codes = self.obd_interface.read_dtc_codes()

        # Enhance with database information
        for dtc in dtc_codes:
            code = dtc["code"]
            # Try to get more detailed information from the database
            dtc_info = self.dtc_db.get_dtc_info(code)
            if dtc_info:
                dtc["description"] = dtc_info["description"]
                dtc["possible_causes"] = dtc_info["possible_causes"]
                dtc["solutions"] = dtc_info["solutions"]
                dtc["severity"] = dtc_info["severity"]
                dtc["category"] = dtc_info["category"]

        return dtc_codes

    def clear_dtc_codes(self):
        """
        Clear Diagnostic Trouble Codes from the vehicle

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.connected:
            return False
        return self.obd_interface.clear_dtc_codes()

    def get_live_data(self, pid):
        """
        Get live data for a specific Parameter ID

        Args:
            pid (str): The Parameter ID to query

        Returns:
            dict: The response data
        """
        if not self.connected:
            return None
        return self.obd_interface.query(pid)

    def search_dtc_codes(self, search_term):
        """
        Search for DTC codes in the database

        Args:
            search_term (str): The search term

        Returns:
            list: List of matching DTC codes
        """
        return self.dtc_db.search_dtc_codes(search_term)

    def get_dtc_categories(self):
        """
        Get all DTC categories

        Returns:
            list: List of DTC categories
        """
        return self.dtc_db.get_all_categories()

    def get_dtc_codes_by_category(self, category_prefix):
        """
        Get DTC codes by category

        Args:
            category_prefix (str): The category prefix (e.g., 'P0', 'B1')

        Returns:
            list: List of DTC codes in the category
        """
        return self.dtc_db.get_codes_by_category(category_prefix)

    def read_ecu(self, ecu, file_path=None, callback=None):
        """
        Read data from an ECU

        Args:
            ecu (str): The ECU to read from
            file_path (str, optional): The file path to save the data to
            callback (function, optional): Callback function for progress updates

        Returns:
            bool: True if read successfully, False otherwise
        """
        if not self.connected:
            return False
        return self.obd_interface.read_ecu(ecu, file_path, callback)

    def write_ecu(self, ecu, file_path, callback=None):
        """
        Write data to an ECU

        Args:
            ecu (str): The ECU to write to
            file_path (str): The file path to read the data from
            callback (function, optional): Callback function for progress updates

        Returns:
            bool: True if written successfully, False otherwise
        """
        if not self.connected:
            return False
        return self.obd_interface.write_ecu(ecu, file_path, callback)

    def verify_ecu(self, ecu, file_path, callback=None):
        """
        Verify ECU data against a file

        Args:
            ecu (str): The ECU to verify
            file_path (str): The file path to compare against
            callback (function, optional): Callback function for progress updates

        Returns:
            bool: True if verified successfully, False otherwise
        """
        if not self.connected:
            return False
        return self.obd_interface.verify_ecu(ecu, file_path, callback)

    def get_programming_status(self):
        """
        Get the current programming status

        Returns:
            dict: The current status
        """
        if not self.connected:
            return {"status": "Not connected"}
        return self.obd_interface.get_programming_status()

    def cancel_programming(self):
        """
        Cancel the current programming operation

        Returns:
            bool: True if cancelled successfully, False otherwise
        """
        if not self.connected:
            return False
        return self.obd_interface.cancel_programming()
