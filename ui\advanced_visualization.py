#!/usr/bin/env python3
"""
Advanced Data Visualization Module
This module provides advanced data visualization components for the UI.
"""

import sys
import os
import logging
import time
import math
import json
from collections import deque
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple, Union

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
                            QComboBox, QCheckBox, QGroupBox, QFormLayout, QSplitter,
                            QFrame, QSizePolicy, QTabWidget, QTableWidget, QTableWidgetItem,
                            QHeaderView, QScrollArea, QSlider, QSpinBox, QDoubleSpinBox,
                            QColorDialog, QFileDialog, QMessageBox)
from PyQt5.QtCore import Qt, QRectF, QPointF, QSize, QTimer, pyqtSignal
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor, QFont, Q<PERSON><PERSON>terPath, QLinearGradient, QPixmap

# Try to import matplotlib for advanced plotting
try:
    import matplotlib
    matplotlib.use('Qt5Agg')
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    import numpy as np
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

logger = logging.getLogger("ui.advanced_visualization")

class FreezeFrameViewer(QWidget):
    """Freeze frame data viewer"""
    
    def __init__(self, parent=None):
        """Initialize the freeze frame viewer"""
        super().__init__(parent)
        
        # Set up the layout
        self.layout = QVBoxLayout(self)
        
        # Create the freeze frame table
        self.create_freeze_frame_table()
        
        # Create the visualization panel
        self.create_visualization_panel()
        
        # Initialize data
        self.freeze_frame_data = {}
    
    def create_freeze_frame_table(self):
        """Create the freeze frame table"""
        # Create group box
        group_box = QGroupBox("Freeze Frame Data")
        group_layout = QVBoxLayout()
        
        # Create table
        self.freeze_frame_table = QTableWidget(0, 4)
        self.freeze_frame_table.setHorizontalHeaderLabels(["Parameter", "Value", "Unit", "Description"])
        self.freeze_frame_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.freeze_frame_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.freeze_frame_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.freeze_frame_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Stretch)
        self.freeze_frame_table.setAlternatingRowColors(True)
        self.freeze_frame_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        # Add table to layout
        group_layout.addWidget(self.freeze_frame_table)
        
        # Add buttons
        button_layout = QHBoxLayout()
        
        self.save_button = QPushButton("Save")
        self.save_button.clicked.connect(self.save_freeze_frame)
        
        self.load_button = QPushButton("Load")
        self.load_button.clicked.connect(self.load_freeze_frame)
        
        self.clear_button = QPushButton("Clear")
        self.clear_button.clicked.connect(self.clear_freeze_frame)
        
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.load_button)
        button_layout.addWidget(self.clear_button)
        button_layout.addStretch()
        
        group_layout.addLayout(button_layout)
        
        # Set group layout
        group_box.setLayout(group_layout)
        
        # Add to main layout
        self.layout.addWidget(group_box)
    
    def create_visualization_panel(self):
        """Create the visualization panel"""
        # Create group box
        group_box = QGroupBox("Visualization")
        group_layout = QVBoxLayout()
        
        # Create visualization type combo
        viz_type_layout = QHBoxLayout()
        viz_type_layout.addWidget(QLabel("Visualization Type:"))
        
        self.viz_type_combo = QComboBox()
        self.viz_type_combo.addItems(["Bar Chart", "Pie Chart", "Gauge"])
        self.viz_type_combo.currentIndexChanged.connect(self.update_visualization)
        
        viz_type_layout.addWidget(self.viz_type_combo)
        viz_type_layout.addStretch()
        
        group_layout.addLayout(viz_type_layout)
        
        # Create visualization widget
        self.viz_widget = QWidget()
        self.viz_layout = QVBoxLayout(self.viz_widget)
        
        # Create visualization placeholder
        if MATPLOTLIB_AVAILABLE:
            self.figure = Figure(figsize=(5, 4), dpi=100)
            self.canvas = FigureCanvas(self.figure)
            self.viz_layout.addWidget(self.canvas)
        else:
            self.viz_layout.addWidget(QLabel("Matplotlib not available. Install matplotlib for advanced visualizations."))
        
        group_layout.addWidget(self.viz_widget)
        
        # Set group layout
        group_box.setLayout(group_layout)
        
        # Add to main layout
        self.layout.addWidget(group_box)
    
    def set_freeze_frame_data(self, data):
        """
        Set the freeze frame data
        
        Args:
            data (dict): The freeze frame data
        """
        self.freeze_frame_data = data
        self.update_table()
        self.update_visualization()
    
    def update_table(self):
        """Update the freeze frame table"""
        # Clear the table
        self.freeze_frame_table.setRowCount(0)
        
        # Add rows
        for param, info in self.freeze_frame_data.items():
            row = self.freeze_frame_table.rowCount()
            self.freeze_frame_table.insertRow(row)
            
            # Parameter
            self.freeze_frame_table.setItem(row, 0, QTableWidgetItem(param))
            
            # Value
            value = info.get('value', '')
            self.freeze_frame_table.setItem(row, 1, QTableWidgetItem(str(value)))
            
            # Unit
            unit = info.get('unit', '')
            self.freeze_frame_table.setItem(row, 2, QTableWidgetItem(unit))
            
            # Description
            description = info.get('description', '')
            self.freeze_frame_table.setItem(row, 3, QTableWidgetItem(description))
    
    def update_visualization(self):
        """Update the visualization"""
        if not MATPLOTLIB_AVAILABLE or not self.freeze_frame_data:
            return
        
        # Clear the figure
        self.figure.clear()
        
        # Get visualization type
        viz_type = self.viz_type_combo.currentText()
        
        # Create subplot
        ax = self.figure.add_subplot(111)
        
        # Filter numeric data
        numeric_data = {}
        for param, info in self.freeze_frame_data.items():
            value = info.get('value', '')
            try:
                value = float(value)
                numeric_data[param] = value
            except (ValueError, TypeError):
                pass
        
        if not numeric_data:
            ax.text(0.5, 0.5, "No numeric data available", ha='center', va='center')
            self.canvas.draw()
            return
        
        # Create visualization
        if viz_type == "Bar Chart":
            # Create bar chart
            params = list(numeric_data.keys())
            values = list(numeric_data.values())
            
            ax.bar(params, values)
            ax.set_xlabel('Parameter')
            ax.set_ylabel('Value')
            ax.set_title('Freeze Frame Data')
            ax.tick_params(axis='x', rotation=45)
            
        elif viz_type == "Pie Chart":
            # Create pie chart
            params = list(numeric_data.keys())
            values = list(numeric_data.values())
            
            # Make all values positive for pie chart
            values = [abs(v) for v in values]
            
            ax.pie(values, labels=params, autopct='%1.1f%%')
            ax.set_title('Freeze Frame Data')
            
        elif viz_type == "Gauge":
            # Create gauge chart (using a custom function)
            self.create_gauge_chart(ax, numeric_data)
        
        # Adjust layout
        self.figure.tight_layout()
        
        # Draw the canvas
        self.canvas.draw()
    
    def create_gauge_chart(self, ax, data):
        """
        Create a gauge chart
        
        Args:
            ax: The matplotlib axis
            data (dict): The data to visualize
        """
        # Clear the axis
        ax.clear()
        
        # Set up the gauge
        ax.set_xlim([-1, 1])
        ax.set_ylim([-1, 1])
        ax.set_aspect('equal')
        ax.axis('off')
        
        # Draw gauge background
        theta = np.linspace(np.pi/2, 2.5*np.pi, 100)
        r = 0.8
        x = r * np.cos(theta)
        y = r * np.sin(theta)
        ax.plot(x, y, 'k-', linewidth=2)
        
        # Draw ticks
        for i in range(11):
            angle = np.pi/2 + i * np.pi/5
            x1 = r * np.cos(angle)
            y1 = r * np.sin(angle)
            x2 = 0.7 * r * np.cos(angle)
            y2 = 0.7 * r * np.sin(angle)
            ax.plot([x1, x2], [y1, y2], 'k-', linewidth=1)
            
            # Add tick label
            x3 = 0.6 * r * np.cos(angle)
            y3 = 0.6 * r * np.sin(angle)
            ax.text(x3, y3, str(i*10), ha='center', va='center')
        
        # Draw needles for each parameter
        colors = ['r', 'g', 'b', 'c', 'm', 'y']
        legend_elements = []
        
        for i, (param, value) in enumerate(data.items()):
            # Normalize value to 0-100
            min_val = 0
            max_val = 100
            
            # Try to get min/max from the freeze frame data
            if param in self.freeze_frame_data:
                min_val = self.freeze_frame_data[param].get('min', 0)
                max_val = self.freeze_frame_data[param].get('max', 100)
            
            # Ensure min/max are valid
            if min_val >= max_val:
                min_val = 0
                max_val = 100
            
            # Normalize value
            norm_value = (value - min_val) / (max_val - min_val) * 100
            norm_value = max(0, min(100, norm_value))
            
            # Calculate needle angle
            angle = np.pi/2 + norm_value * np.pi/100
            
            # Draw needle
            color = colors[i % len(colors)]
            x1 = 0
            y1 = 0
            x2 = 0.7 * r * np.cos(angle)
            y2 = 0.7 * r * np.sin(angle)
            ax.plot([x1, x2], [y1, y2], color=color, linewidth=2)
            
            # Add to legend
            legend_elements.append(plt.Line2D([0], [0], color=color, lw=2, label=f"{param}: {value}"))
        
        # Add legend
        ax.legend(handles=legend_elements, loc='lower center', bbox_to_anchor=(0.5, -0.2))
        
        # Add title
        ax.set_title('Freeze Frame Data Gauge')
    
    def save_freeze_frame(self):
        """Save freeze frame data to file"""
        if not self.freeze_frame_data:
            QMessageBox.warning(self, "Save Freeze Frame", "No freeze frame data to save")
            return
        
        # Get file path
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Freeze Frame Data", "", "JSON Files (*.json);;All Files (*)"
        )
        
        if not file_path:
            return
        
        try:
            # Save to JSON file
            with open(file_path, 'w') as file:
                json.dump(self.freeze_frame_data, file, indent=2)
            
            QMessageBox.information(self, "Save Freeze Frame", "Freeze frame data saved successfully")
        except Exception as e:
            QMessageBox.critical(self, "Save Freeze Frame", f"Error saving freeze frame data: {e}")
    
    def load_freeze_frame(self):
        """Load freeze frame data from file"""
        # Get file path
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Load Freeze Frame Data", "", "JSON Files (*.json);;All Files (*)"
        )
        
        if not file_path:
            return
        
        try:
            # Load from JSON file
            with open(file_path, 'r') as file:
                data = json.load(file)
            
            # Set freeze frame data
            self.set_freeze_frame_data(data)
            
            QMessageBox.information(self, "Load Freeze Frame", "Freeze frame data loaded successfully")
        except Exception as e:
            QMessageBox.critical(self, "Load Freeze Frame", f"Error loading freeze frame data: {e}")
    
    def clear_freeze_frame(self):
        """Clear freeze frame data"""
        self.freeze_frame_data = {}
        self.update_table()
        self.update_visualization()

class DataMonitor(QWidget):
    """Data monitor with alerts"""
    
    # Signals
    alert_triggered = pyqtSignal(str, str)
    
    def __init__(self, parent=None):
        """Initialize the data monitor"""
        super().__init__(parent)
        
        # Set up the layout
        self.layout = QVBoxLayout(self)
        
        # Create the data table
        self.create_data_table()
        
        # Create the alert panel
        self.create_alert_panel()
        
        # Initialize data
        self.monitored_data = {}
        self.alerts = {}
        
        # Create timer for monitoring
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.check_alerts)
        self.monitor_timer.start(1000)  # Check every second
    
    def create_data_table(self):
        """Create the data table"""
        # Create group box
        group_box = QGroupBox("Monitored Data")
        group_layout = QVBoxLayout()
        
        # Create table
        self.data_table = QTableWidget(0, 5)
        self.data_table.setHorizontalHeaderLabels(["Parameter", "Value", "Unit", "Min", "Max"])
        self.data_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.data_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.data_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.data_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.data_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)
        self.data_table.setAlternatingRowColors(True)
        self.data_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        # Add table to layout
        group_layout.addWidget(self.data_table)
        
        # Set group layout
        group_box.setLayout(group_layout)
        
        # Add to main layout
        self.layout.addWidget(group_box)
    
    def create_alert_panel(self):
        """Create the alert panel"""
        # Create group box
        group_box = QGroupBox("Alerts")
        group_layout = QVBoxLayout()
        
        # Create alert table
        self.alert_table = QTableWidget(0, 4)
        self.alert_table.setHorizontalHeaderLabels(["Parameter", "Condition", "Threshold", "Actions"])
        self.alert_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.alert_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.alert_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.alert_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Stretch)
        self.alert_table.setAlternatingRowColors(True)
        self.alert_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        # Add table to layout
        group_layout.addWidget(self.alert_table)
        
        # Add buttons
        button_layout = QHBoxLayout()
        
        self.add_alert_button = QPushButton("Add Alert")
        self.add_alert_button.clicked.connect(self.add_alert)
        
        self.remove_alert_button = QPushButton("Remove Alert")
        self.remove_alert_button.clicked.connect(self.remove_alert)
        
        button_layout.addWidget(self.add_alert_button)
        button_layout.addWidget(self.remove_alert_button)
        button_layout.addStretch()
        
        group_layout.addLayout(button_layout)
        
        # Set group layout
        group_box.setLayout(group_layout)
        
        # Add to main layout
        self.layout.addWidget(group_box)
    
    def update_data(self, data):
        """
        Update the monitored data
        
        Args:
            data (dict): The data to update
        """
        # Update monitored data
        for param, info in data.items():
            if param in self.monitored_data:
                # Update existing data
                self.monitored_data[param].update(info)
            else:
                # Add new data
                self.monitored_data[param] = info
        
        # Update table
        self.update_table()
        
        # Check alerts
        self.check_alerts()
    
    def update_table(self):
        """Update the data table"""
        # Clear the table
        self.data_table.setRowCount(0)
        
        # Add rows
        for param, info in self.monitored_data.items():
            row = self.data_table.rowCount()
            self.data_table.insertRow(row)
            
            # Parameter
            self.data_table.setItem(row, 0, QTableWidgetItem(param))
            
            # Value
            value = info.get('value', '')
            value_item = QTableWidgetItem(str(value))
            
            # Check if value is outside min/max range
            try:
                value_float = float(value)
                min_val = info.get('min', float('-inf'))
                max_val = info.get('max', float('inf'))
                
                if value_float < min_val or value_float > max_val:
                    value_item.setBackground(QColor(255, 200, 200))  # Light red
            except (ValueError, TypeError):
                pass
            
            self.data_table.setItem(row, 1, value_item)
            
            # Unit
            unit = info.get('unit', '')
            self.data_table.setItem(row, 2, QTableWidgetItem(unit))
            
            # Min
            min_val = info.get('min', '')
            self.data_table.setItem(row, 3, QTableWidgetItem(str(min_val)))
            
            # Max
            max_val = info.get('max', '')
            self.data_table.setItem(row, 4, QTableWidgetItem(str(max_val)))
    
    def add_alert(self):
        """Add an alert"""
        # Get parameters
        params = list(self.monitored_data.keys())
        if not params:
            QMessageBox.warning(self, "Add Alert", "No parameters available")
            return
        
        # Create dialog
        dialog = QDialog(self)
        dialog.setWindowTitle("Add Alert")
        dialog.setMinimumWidth(400)
        
        # Create layout
        layout = QFormLayout(dialog)
        
        # Parameter combo
        param_combo = QComboBox()
        param_combo.addItems(params)
        layout.addRow("Parameter:", param_combo)
        
        # Condition combo
        condition_combo = QComboBox()
        condition_combo.addItems(["<", "<=", "=", ">=", ">", "!="])
        layout.addRow("Condition:", condition_combo)
        
        # Threshold input
        threshold_input = QDoubleSpinBox()
        threshold_input.setRange(-1000000, 1000000)
        threshold_input.setDecimals(2)
        layout.addRow("Threshold:", threshold_input)
        
        # Action input
        action_input = QLineEdit()
        action_input.setText("Show alert")
        layout.addRow("Action:", action_input)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addRow(button_box)
        
        # Show dialog
        if dialog.exec_() == QDialog.Accepted:
            # Get values
            param = param_combo.currentText()
            condition = condition_combo.currentText()
            threshold = threshold_input.value()
            action = action_input.text()
            
            # Add alert
            alert_id = f"{param}_{condition}_{threshold}"
            self.alerts[alert_id] = {
                'param': param,
                'condition': condition,
                'threshold': threshold,
                'action': action,
                'triggered': False
            }
            
            # Update alert table
            self.update_alert_table()
    
    def remove_alert(self):
        """Remove an alert"""
        # Get selected row
        selected_rows = self.alert_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "Remove Alert", "No alert selected")
            return
        
        # Get alert ID
        row = selected_rows[0].row()
        param = self.alert_table.item(row, 0).text()
        condition = self.alert_table.item(row, 1).text()
        threshold = float(self.alert_table.item(row, 2).text())
        
        alert_id = f"{param}_{condition}_{threshold}"
        
        # Remove alert
        if alert_id in self.alerts:
            del self.alerts[alert_id]
        
        # Update alert table
        self.update_alert_table()
    
    def update_alert_table(self):
        """Update the alert table"""
        # Clear the table
        self.alert_table.setRowCount(0)
        
        # Add rows
        for alert_id, alert in self.alerts.items():
            row = self.alert_table.rowCount()
            self.alert_table.insertRow(row)
            
            # Parameter
            self.alert_table.setItem(row, 0, QTableWidgetItem(alert['param']))
            
            # Condition
            self.alert_table.setItem(row, 1, QTableWidgetItem(alert['condition']))
            
            # Threshold
            self.alert_table.setItem(row, 2, QTableWidgetItem(str(alert['threshold'])))
            
            # Action
            self.alert_table.setItem(row, 3, QTableWidgetItem(alert['action']))
    
    def check_alerts(self):
        """Check alerts"""
        for alert_id, alert in self.alerts.items():
            param = alert['param']
            condition = alert['condition']
            threshold = alert['threshold']
            
            # Skip if parameter not in monitored data
            if param not in self.monitored_data:
                continue
            
            # Get value
            try:
                value = float(self.monitored_data[param].get('value', 0))
            except (ValueError, TypeError):
                continue
            
            # Check condition
            triggered = False
            if condition == "<" and value < threshold:
                triggered = True
            elif condition == "<=" and value <= threshold:
                triggered = True
            elif condition == "=" and value == threshold:
                triggered = True
            elif condition == ">=" and value >= threshold:
                triggered = True
            elif condition == ">" and value > threshold:
                triggered = True
            elif condition == "!=" and value != threshold:
                triggered = True
            
            # Handle trigger
            if triggered and not alert['triggered']:
                # Mark as triggered
                alert['triggered'] = True
                
                # Emit signal
                self.alert_triggered.emit(param, alert['action'])
                
                # Log alert
                logger.info(f"Alert triggered: {param} {condition} {threshold}")
            elif not triggered and alert['triggered']:
                # Reset trigger
                alert['triggered'] = False
