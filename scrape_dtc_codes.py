#!/usr/bin/env python3
"""
Scrape DTC Codes Script
This script scrapes DTC codes from various sources and imports them into the database.
"""

import os
import sys
import logging
import argparse
from database.dtc_db import DTCDatabase
from database.dtc_scraper import DTCScraper

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("dtc_scrape.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("scrape_dtc_codes")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Scrape DTC codes and import them into the database")
    parser.add_argument("--db", default="database/dtc.db", help="Path to the database file")
    parser.add_argument("--output-dir", default="database/dtc_codes", help="Path to the output directory")
    parser.add_argument("--source", choices=["all", "obd", "trouble", "manufacturer"], default="all", help="Source to scrape")
    parser.add_argument("--manufacturer", help="Manufacturer to scrape (only used with --source=manufacturer)")
    parser.add_argument("--import-only", action="store_true", help="Only import existing files, don't scrape")
    
    args = parser.parse_args()
    
    # Initialize database
    dtc_db = DTCDatabase(args.db)
    
    # Initialize scraper
    scraper = DTCScraper(args.output_dir)
    
    if not args.import_only:
        # Scrape data
        if args.source == "all":
            codes = scraper.scrape_all()
        elif args.source == "obd":
            codes = scraper.scrape_obd_codes()
        elif args.source == "trouble":
            codes = scraper.scrape_trouble_code_help()
        elif args.source == "manufacturer":
            if not args.manufacturer:
                logger.error("Manufacturer is required when source is 'manufacturer'")
                return 1
            
            codes = scraper.scrape_manufacturer_codes(args.manufacturer)
        
        logger.info(f"Scraped {len(codes)} DTC codes")
    
    # Import data
    if args.source == "all":
        # Import all codes
        file_path = os.path.join(args.output_dir, "all_dtc_codes.json")
        if os.path.exists(file_path):
            count = dtc_db.import_dtc_codes_from_file(file_path)
            logger.info(f"Imported {count} DTC codes from {file_path}")
    elif args.source == "obd":
        # Import OBD codes
        file_path = os.path.join(args.output_dir, "obd_codes.json")
        if os.path.exists(file_path):
            count = dtc_db.import_dtc_codes_from_file(file_path)
            logger.info(f"Imported {count} DTC codes from {file_path}")
    elif args.source == "trouble":
        # Import trouble codes
        file_path = os.path.join(args.output_dir, "trouble_code_help.json")
        if os.path.exists(file_path):
            count = dtc_db.import_dtc_codes_from_file(file_path)
            logger.info(f"Imported {count} DTC codes from {file_path}")
    elif args.source == "manufacturer":
        # Import manufacturer codes
        file_path = os.path.join(args.output_dir, f"{args.manufacturer.lower()}_codes.json")
        if os.path.exists(file_path):
            count = dtc_db.import_dtc_codes_from_file(file_path)
            logger.info(f"Imported {count} DTC codes from {file_path}")
    
    # Get statistics
    stats = dtc_db.get_dtc_statistics()
    logger.info(f"DTC database statistics: {stats}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
