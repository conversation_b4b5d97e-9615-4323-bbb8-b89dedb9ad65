#!/usr/bin/env python3
"""
ELM327 Interface Handler
This module provides the interface handler for the ELM327 adapter.
"""

import logging
import time
import serial
import re
from enum import Enum, auto

logger = logging.getLogger("hardware.ELM327")

class ELM327Error(Exception):
    """Base class for ELM327 exceptions"""
    pass

class ConnectionError(ELM327Error):
    """Connection error"""
    pass

class CommandError(ELM327Error):
    """Command error"""
    pass

class ProtocolError(ELM327Error):
    """Protocol error"""
    pass

class ELM327Protocol(Enum):
    """ELM327 supported protocols"""
    AUTO = 0
    SAE_J1850_PWM = 1
    SAE_J1850_VPW = 2
    ISO_9141_2 = 3
    ISO_14230_4_KWP_5BAUD = 4
    ISO_14230_4_KWP_FAST = 5
    ISO_15765_4_CAN_11BIT_500K = 6
    ISO_15765_4_CAN_29BIT_500K = 7
    ISO_15765_4_CAN_11BIT_250K = 8
    ISO_15765_4_CAN_29BIT_250K = 9
    SAE_J1939_CAN_29BIT_250K = 10
    USER1_CAN_11BIT_125K = 11
    USER2_CAN_11BIT_50K = 12

class ELM327Interface:
    """ELM327 interface handler"""
    
    def __init__(self, port=None, baudrate=38400, timeout=10):
        """
        Initialize the ELM327 interface
        
        Args:
            port (str): The serial port to use
            baudrate (int): The baudrate to use
            timeout (float): The timeout in seconds
        """
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.serial = None
        self.connected = False
        self.protocol = None
        self.echo = True
        self.headers = False
        self.spaces = True
        self.linefeed = True
        self.adaptive_timing = 1
        self.timeout_multiplier = 4
        self.voltage = 0.0
        
        # ELM327 version
        self.version = None
        
        # ELM327 supported PIDs
        self.supported_pids = []
    
    def connect(self):
        """
        Connect to the ELM327 adapter
        
        Returns:
            bool: True if connected, False otherwise
        """
        try:
            # Open serial port
            self.serial = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=self.timeout
            )
            
            # Reset the adapter
            self.reset()
            
            # Check if the adapter is responding
            if not self._test_connection():
                self.serial.close()
                self.serial = None
                logger.error("ELM327 adapter not responding")
                return False
            
            # Get the adapter version
            self.version = self._get_version()
            
            # Configure the adapter
            self._configure()
            
            self.connected = True
            logger.info(f"Connected to ELM327 adapter (version {self.version})")
            return True
        except serial.SerialException as e:
            logger.error(f"Error connecting to ELM327 adapter: {e}")
            if self.serial:
                self.serial.close()
                self.serial = None
            return False
    
    def disconnect(self):
        """
        Disconnect from the ELM327 adapter
        
        Returns:
            bool: True if disconnected, False otherwise
        """
        if not self.connected:
            return True
        
        try:
            # Reset the adapter
            self.reset()
            
            # Close serial port
            if self.serial:
                self.serial.close()
                self.serial = None
            
            self.connected = False
            logger.info("Disconnected from ELM327 adapter")
            return True
        except Exception as e:
            logger.error(f"Error disconnecting from ELM327 adapter: {e}")
            return False
    
    def reset(self):
        """
        Reset the ELM327 adapter
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Send ATZ command (reset)
            response = self._send_command("ATZ")
            
            # Wait for the adapter to reset
            time.sleep(1)
            
            # Clear any pending data
            if self.serial:
                self.serial.flushInput()
            
            return "ELM327" in response
        except Exception as e:
            logger.error(f"Error resetting ELM327 adapter: {e}")
            return False
    
    def set_protocol(self, protocol):
        """
        Set the OBD protocol
        
        Args:
            protocol: The protocol to use
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Convert protocol name to ELM327Protocol
            if isinstance(protocol, str):
                protocol_map = {
                    "AUTO": ELM327Protocol.AUTO,
                    "SAE J1850 PWM": ELM327Protocol.SAE_J1850_PWM,
                    "SAE J1850 VPW": ELM327Protocol.SAE_J1850_VPW,
                    "ISO 9141-2": ELM327Protocol.ISO_9141_2,
                    "ISO 14230-4 (KWP 5-BAUD)": ELM327Protocol.ISO_14230_4_KWP_5BAUD,
                    "ISO 14230-4 (KWP FAST)": ELM327Protocol.ISO_14230_4_KWP_FAST,
                    "ISO 15765-4 (CAN 11/500)": ELM327Protocol.ISO_15765_4_CAN_11BIT_500K,
                    "ISO 15765-4 (CAN 29/500)": ELM327Protocol.ISO_15765_4_CAN_29BIT_500K,
                    "ISO 15765-4 (CAN 11/250)": ELM327Protocol.ISO_15765_4_CAN_11BIT_250K,
                    "ISO 15765-4 (CAN 29/250)": ELM327Protocol.ISO_15765_4_CAN_29BIT_250K,
                    "SAE J1939 (CAN 29/250)": ELM327Protocol.SAE_J1939_CAN_29BIT_250K,
                    "USER1 (CAN 11/125)": ELM327Protocol.USER1_CAN_11BIT_125K,
                    "USER2 (CAN 11/50)": ELM327Protocol.USER2_CAN_11BIT_50K,
                    "CAN": ELM327Protocol.ISO_15765_4_CAN_11BIT_500K
                }
                
                protocol = protocol_map.get(protocol, ELM327Protocol.AUTO)
            
            # Send ATSP command (set protocol)
            response = self._send_command(f"ATSP{protocol.value}")
            
            if "OK" in response:
                self.protocol = protocol
                logger.info(f"Set protocol to {protocol}")
                return True
            
            return False
        except Exception as e:
            logger.error(f"Error setting protocol: {e}")
            return False
    
    def set_baudrate(self, baudrate):
        """
        Set the CAN baudrate
        
        Args:
            baudrate (int): The baudrate to use
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Map baudrate to protocol
            if baudrate == 500000:
                protocol = ELM327Protocol.ISO_15765_4_CAN_11BIT_500K
            elif baudrate == 250000:
                protocol = ELM327Protocol.ISO_15765_4_CAN_11BIT_250K
            elif baudrate == 125000:
                protocol = ELM327Protocol.USER1_CAN_11BIT_125K
            elif baudrate == 50000:
                protocol = ELM327Protocol.USER2_CAN_11BIT_50K
            else:
                logger.error(f"Unsupported baudrate: {baudrate}")
                return False
            
            # Set protocol
            return self.set_protocol(protocol)
        except Exception as e:
            logger.error(f"Error setting baudrate: {e}")
            return False
    
    def set_can_id_filter(self, can_id):
        """
        Set the CAN ID filter
        
        Args:
            can_id (int): The CAN ID to filter
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Send ATCF command (set CAN ID filter)
            response = self._send_command(f"ATCF{can_id:X}")
            
            return "OK" in response
        except Exception as e:
            logger.error(f"Error setting CAN ID filter: {e}")
            return False
    
    def set_can_id_mask(self, mask):
        """
        Set the CAN ID mask
        
        Args:
            mask (int): The CAN ID mask
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Send ATCM command (set CAN ID mask)
            response = self._send_command(f"ATCM{mask:X}")
            
            return "OK" in response
        except Exception as e:
            logger.error(f"Error setting CAN ID mask: {e}")
            return False
    
    def send_can_message(self, can_id, data, extended=False):
        """
        Send a CAN message
        
        Args:
            can_id (int): The CAN ID
            data (bytes): The data to send
            extended (bool): Whether to use extended CAN ID
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Format CAN ID
            if extended:
                can_id_str = f"{can_id:08X}"
            else:
                can_id_str = f"{can_id:03X}"
            
            # Format data
            data_str = ''.join(f"{b:02X}" for b in data)
            
            # Send command
            response = self._send_command(f"{can_id_str}{data_str}")
            
            return not ("?" in response or "ERROR" in response)
        except Exception as e:
            logger.error(f"Error sending CAN message: {e}")
            return False
    
    def receive_can_message(self, can_id=None, timeout=None):
        """
        Receive a CAN message
        
        Args:
            can_id (int): The CAN ID to filter (None for any)
            timeout (float): The timeout in seconds
            
        Returns:
            bytes: The received data
        """
        try:
            # Set timeout
            if timeout is None:
                timeout = self.timeout
            
            # Set CAN ID filter if specified
            if can_id is not None:
                self.set_can_id_filter(can_id)
            
            # Send ATMA command (monitor all)
            self._send_command("ATMA", expect_prompt=False)
            
            # Wait for data
            start_time = time.time()
            data = b""
            
            while time.time() - start_time < timeout:
                if self.serial.in_waiting:
                    byte = self.serial.read(1)
                    if byte == b'>':
                        # End of response
                        break
                    data += byte
                else:
                    time.sleep(0.01)
            
            # Stop monitoring
            self.serial.write(b'\r')
            time.sleep(0.1)
            self.serial.flushInput()
            
            # Parse data
            if data:
                # Convert to string
                data_str = data.decode('ascii', errors='ignore').strip()
                
                # Parse CAN message
                if can_id is not None:
                    # Look for messages with the specified CAN ID
                    pattern = re.compile(f"{can_id:X}\\s+([0-9A-F\\s]+)")
                    match = pattern.search(data_str)
                    
                    if match:
                        # Extract data
                        data_hex = match.group(1).replace(" ", "")
                        return bytes.fromhex(data_hex)
                else:
                    # Return the first valid CAN message
                    pattern = re.compile("([0-9A-F]+)\\s+([0-9A-F\\s]+)")
                    match = pattern.search(data_str)
                    
                    if match:
                        # Extract data
                        data_hex = match.group(2).replace(" ", "")
                        return bytes.fromhex(data_hex)
            
            return None
        except Exception as e:
            logger.error(f"Error receiving CAN message: {e}")
            return None
    
    def send_command(self, command):
        """
        Send a command to the ELM327 adapter
        
        Args:
            command: The command to send
            
        Returns:
            str: The response
        """
        try:
            return self._send_command(command)
        except Exception as e:
            logger.error(f"Error sending command: {e}")
            return None
    
    def get_voltage(self):
        """
        Get the voltage
        
        Returns:
            float: The voltage
        """
        try:
            # Send ATRV command (read voltage)
            response = self._send_command("ATRV")
            
            # Parse voltage
            match = re.search(r"([0-9.]+)V", response)
            if match:
                self.voltage = float(match.group(1))
                return self.voltage
            
            return 0.0
        except Exception as e:
            logger.error(f"Error getting voltage: {e}")
            return 0.0
    
    def get_supported_pids(self):
        """
        Get the supported PIDs
        
        Returns:
            list: The supported PIDs
        """
        try:
            # Send 0100 command (get supported PIDs 01-20)
            response = self._send_command("0100")
            
            # Parse response
            if "NO DATA" in response:
                return []
            
            # Extract data
            data = self._parse_response(response)
            
            if not data or len(data) < 4:
                return []
            
            # Parse supported PIDs
            pids = []
            for i in range(32):
                if data[i // 8] & (1 << (7 - (i % 8))):
                    pids.append(i + 1)
            
            self.supported_pids = pids
            return pids
        except Exception as e:
            logger.error(f"Error getting supported PIDs: {e}")
            return []
    
    def _test_connection(self):
        """
        Test if the ELM327 adapter is responding
        
        Returns:
            bool: True if responding, False otherwise
        """
        try:
            # Send AT command (attention)
            response = self._send_command("AT")
            
            return "OK" in response
        except Exception:
            return False
    
    def _get_version(self):
        """
        Get the ELM327 version
        
        Returns:
            str: The version
        """
        try:
            # Send ATI command (identify)
            response = self._send_command("ATI")
            
            # Parse version
            match = re.search(r"ELM327\s+v([0-9.]+)", response)
            if match:
                return match.group(1)
            
            return "Unknown"
        except Exception as e:
            logger.error(f"Error getting version: {e}")
            return "Unknown"
    
    def _configure(self):
        """
        Configure the ELM327 adapter
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Set echo off
            self._send_command("ATE0")
            self.echo = False
            
            # Set headers on
            self._send_command("ATH1")
            self.headers = True
            
            # Set spaces on
            self._send_command("ATS1")
            self.spaces = True
            
            # Set linefeed off
            self._send_command("ATL0")
            self.linefeed = False
            
            # Set adaptive timing
            self._send_command(f"ATAT{self.adaptive_timing}")
            
            # Set timeout multiplier
            self._send_command(f"ATST{self.timeout_multiplier:02X}")
            
            # Set protocol
            if self.protocol:
                self.set_protocol(self.protocol)
            
            return True
        except Exception as e:
            logger.error(f"Error configuring ELM327 adapter: {e}")
            return False
    
    def _send_command(self, command, expect_prompt=True):
        """
        Send a command to the ELM327 adapter
        
        Args:
            command: The command to send
            expect_prompt (bool): Whether to expect a prompt
            
        Returns:
            str: The response
        """
        if not self.serial:
            raise ConnectionError("Not connected to ELM327 adapter")
        
        try:
            # Clear input buffer
            self.serial.flushInput()
            
            # Send command
            self.serial.write((command + "\r").encode())
            
            # Read response
            response = ""
            start_time = time.time()
            
            while time.time() - start_time < self.timeout:
                if self.serial.in_waiting:
                    char = self.serial.read(1).decode('ascii', errors='ignore')
                    
                    if char == '>':
                        # End of response
                        if expect_prompt:
                            break
                    
                    response += char
                else:
                    time.sleep(0.01)
            
            # Process response
            response = response.strip()
            
            # Remove echo
            if self.echo and response.startswith(command):
                response = response[len(command):].strip()
            
            # Remove linefeeds
            response = response.replace("\r", "").replace("\n", " ").strip()
            
            # Check for errors
            if "?" in response:
                raise CommandError(f"Invalid command: {command}")
            
            if "ERROR" in response:
                raise CommandError(f"Command error: {command}")
            
            if "UNABLE TO CONNECT" in response:
                raise ProtocolError("Unable to connect to vehicle")
            
            if "NO DATA" in response:
                # This is not an error, just no data available
                return "NO DATA"
            
            return response
        except serial.SerialException as e:
            raise ConnectionError(f"Serial communication error: {e}")
    
    def _parse_response(self, response):
        """
        Parse a response from the ELM327 adapter
        
        Args:
            response: The response to parse
            
        Returns:
            bytes: The parsed data
        """
        try:
            # Remove spaces
            response = response.replace(" ", "")
            
            # Find data
            if ":" in response:
                # Response with headers
                parts = response.split(":")
                if len(parts) > 1:
                    response = parts[1]
            
            # Convert to bytes
            return bytes.fromhex(response)
        except Exception as e:
            logger.error(f"Error parsing response: {e}")
            return None
