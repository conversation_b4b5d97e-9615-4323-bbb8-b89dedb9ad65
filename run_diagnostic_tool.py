#!/usr/bin/env python3
"""
Vehicle Diagnostic Tool Launcher
This script initializes and runs the vehicle diagnostic tool.
"""

import os
import sys
import logging
import argparse
from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QFont

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from controller import VehicleDiagnosticController
from ui.main_window import MainWindow

# Set up logging
def setup_logging(log_level="INFO", log_file="diagnostic_tool.log"):
    """Set up logging configuration"""
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f'Invalid log level: {log_level}')
    
    logging.basicConfig(
        level=numeric_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )

def check_dependencies():
    """Check if all required dependencies are available"""
    missing_deps = []
    
    try:
        import PyQt5
    except ImportError:
        missing_deps.append("PyQt5")
    
    try:
        import serial
    except ImportError:
        missing_deps.append("pyserial")
    
    try:
        import requests
    except ImportError:
        missing_deps.append("requests")
    
    try:
        import bs4
    except ImportError:
        missing_deps.append("beautifulsoup4")
    
    try:
        import sqlalchemy
    except ImportError:
        missing_deps.append("sqlalchemy")
    
    try:
        import matplotlib
    except ImportError:
        missing_deps.append("matplotlib")
    
    if missing_deps:
        print("Missing required dependencies:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\nPlease install missing dependencies using:")
        print(f"pip install {' '.join(missing_deps)}")
        return False
    
    return True

def initialize_databases():
    """Initialize databases if they don't exist"""
    logger = logging.getLogger("startup")
    
    try:
        # Check if databases exist
        vehicle_db_exists = os.path.exists("database/vehicles.db")
        dtc_db_exists = os.path.exists("database/dtc.db")
        
        if not vehicle_db_exists or not dtc_db_exists:
            logger.info("Initializing databases...")
            
            # Import and run database initialization
            from initialize_databases import initialize_vehicle_database, initialize_dtc_database
            
            if not vehicle_db_exists:
                vehicle_count = initialize_vehicle_database()
                logger.info(f"Initialized vehicle database with {vehicle_count} vehicles")
            
            if not dtc_db_exists:
                dtc_count = initialize_dtc_database()
                logger.info(f"Initialized DTC database with {dtc_count} codes")
        else:
            logger.info("Databases already exist")
        
        return True
    except Exception as e:
        logger.error(f"Error initializing databases: {e}")
        return False

def create_splash_screen():
    """Create and show splash screen"""
    # Create a simple splash screen
    splash_pix = QPixmap(400, 300)
    splash_pix.fill(Qt.darkBlue)
    
    splash = QSplashScreen(splash_pix, Qt.WindowStaysOnTopHint)
    splash.setMask(splash_pix.mask())
    
    # Add text to splash screen
    splash.showMessage(
        "Vehicle Diagnostic Tool\nLoading...",
        Qt.AlignCenter | Qt.AlignBottom,
        Qt.white
    )
    
    splash.show()
    return splash

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Vehicle Diagnostic Tool")
    parser.add_argument("--log-level", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                       help="Set the logging level")
    parser.add_argument("--log-file", default="diagnostic_tool.log", help="Set the log file path")
    parser.add_argument("--no-splash", action="store_true", help="Disable splash screen")
    parser.add_argument("--test", action="store_true", help="Run system tests instead of GUI")
    parser.add_argument("--init-db", action="store_true", help="Initialize databases and exit")
    
    args = parser.parse_args()
    
    # Set up logging
    setup_logging(args.log_level, args.log_file)
    logger = logging.getLogger("startup")
    
    logger.info("Starting Vehicle Diagnostic Tool...")
    
    # Check dependencies
    if not check_dependencies():
        logger.error("Missing required dependencies")
        return 1
    
    # Initialize databases if requested
    if args.init_db:
        logger.info("Initializing databases...")
        success = initialize_databases()
        if success:
            logger.info("Database initialization completed successfully")
            return 0
        else:
            logger.error("Database initialization failed")
            return 1
    
    # Run system tests if requested
    if args.test:
        logger.info("Running system tests...")
        try:
            from test_system import main as test_main
            return test_main()
        except Exception as e:
            logger.error(f"Error running tests: {e}")
            return 1
    
    # Create QApplication
    app = QApplication(sys.argv)
    app.setApplicationName("Vehicle Diagnostic Tool")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Vehicle Diagnostics")
    
    # Set application style
    app.setStyle("Fusion")
    
    # Create splash screen
    splash = None
    if not args.no_splash:
        splash = create_splash_screen()
        app.processEvents()
    
    try:
        # Initialize databases
        if splash:
            splash.showMessage("Initializing databases...", Qt.AlignCenter | Qt.AlignBottom, Qt.white)
            app.processEvents()
        
        db_success = initialize_databases()
        if not db_success:
            if splash:
                splash.close()
            QMessageBox.critical(None, "Database Error", "Failed to initialize databases")
            return 1
        
        # Create controller
        if splash:
            splash.showMessage("Initializing controller...", Qt.AlignCenter | Qt.AlignBottom, Qt.white)
            app.processEvents()
        
        controller = VehicleDiagnosticController()
        
        # Create main window
        if splash:
            splash.showMessage("Creating user interface...", Qt.AlignCenter | Qt.AlignBottom, Qt.white)
            app.processEvents()
        
        main_window = MainWindow(controller)
        
        # Close splash screen and show main window
        if splash:
            splash.finish(main_window)
        
        main_window.show()
        
        logger.info("Vehicle Diagnostic Tool started successfully")
        
        # Run the application
        return app.exec_()
        
    except Exception as e:
        logger.error(f"Error starting application: {e}")
        if splash:
            splash.close()
        
        QMessageBox.critical(None, "Startup Error", f"Failed to start application:\n{e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
