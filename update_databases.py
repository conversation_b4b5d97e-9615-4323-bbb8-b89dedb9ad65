#!/usr/bin/env python3
"""
Update Databases Script
This script updates the vehicle and DTC databases.
"""

import os
import sys
import logging
import argparse
from database.vehicle_db import VehicleDatabase
from database.dtc_db import DTCDatabase
from database.vehicle_data_importer import VehicleDataImporter
from database.dtc_scraper import DTCScraper

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("database_update.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("update_databases")

def update_vehicle_database(vehicle_db_path="database/vehicles.db", data_dir="database/vehicle_data"):
    """
    Update the vehicle database
    
    Args:
        vehicle_db_path (str): Path to the vehicle database file
        data_dir (str): Path to the vehicle data directory
        
    Returns:
        int: Number of vehicles imported
    """
    try:
        # Initialize database
        vehicle_db = VehicleDatabase(vehicle_db_path)
        
        # Initialize importer
        importer = VehicleDataImporter(vehicle_db, data_dir)
        
        # Get list of JSON files
        json_files = [f for f in os.listdir(data_dir) if f.endswith('.json')]
        
        if not json_files:
            logger.warning(f"No JSON files found in {data_dir}")
            return 0
        
        # Import data from each file
        total_count = 0
        for file_name in json_files:
            file_path = os.path.join(data_dir, file_name)
            count = importer.import_from_json(file_path)
            total_count += count
            logger.info(f"Imported {count} vehicles from {file_name}")
        
        logger.info(f"Total vehicles imported: {total_count}")
        return total_count
    except Exception as e:
        logger.error(f"Error updating vehicle database: {e}")
        return 0

def update_dtc_database(dtc_db_path="database/dtc.db", data_dir="database/dtc_codes"):
    """
    Update the DTC database
    
    Args:
        dtc_db_path (str): Path to the DTC database file
        data_dir (str): Path to the DTC data directory
        
    Returns:
        int: Number of DTC codes imported
    """
    try:
        # Initialize database
        dtc_db = DTCDatabase(dtc_db_path)
        
        # Initialize scraper
        scraper = DTCScraper(data_dir)
        
        # Scrape OBD codes
        logger.info("Scraping OBD codes")
        obd_codes = scraper.scrape_obd_codes()
        
        # Import OBD codes
        file_path = os.path.join(data_dir, "obd_codes.json")
        if os.path.exists(file_path):
            count = dtc_db.import_dtc_codes_from_file(file_path)
            logger.info(f"Imported {count} OBD codes from {file_path}")
        
        # Scrape trouble codes
        logger.info("Scraping trouble codes")
        trouble_codes = scraper.scrape_trouble_code_help()
        
        # Import trouble codes
        file_path = os.path.join(data_dir, "trouble_code_help.json")
        if os.path.exists(file_path):
            count = dtc_db.import_dtc_codes_from_file(file_path)
            logger.info(f"Imported {count} trouble codes from {file_path}")
        
        # Get statistics
        stats = dtc_db.get_dtc_statistics()
        logger.info(f"DTC database statistics: {stats}")
        
        return stats.get('total_codes', 0)
    except Exception as e:
        logger.error(f"Error updating DTC database: {e}")
        return 0

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Update the vehicle and DTC databases")
    parser.add_argument("--vehicle-db", default="database/vehicles.db", help="Path to the vehicle database file")
    parser.add_argument("--dtc-db", default="database/dtc.db", help="Path to the DTC database file")
    parser.add_argument("--vehicle-data-dir", default="database/vehicle_data", help="Path to the vehicle data directory")
    parser.add_argument("--dtc-data-dir", default="database/dtc_codes", help="Path to the DTC data directory")
    parser.add_argument("--vehicle-only", action="store_true", help="Only update the vehicle database")
    parser.add_argument("--dtc-only", action="store_true", help="Only update the DTC database")
    
    args = parser.parse_args()
    
    if args.vehicle_only:
        # Update vehicle database only
        update_vehicle_database(args.vehicle_db, args.vehicle_data_dir)
    elif args.dtc_only:
        # Update DTC database only
        update_dtc_database(args.dtc_db, args.dtc_data_dir)
    else:
        # Update both databases
        update_vehicle_database(args.vehicle_db, args.vehicle_data_dir)
        update_dtc_database(args.dtc_db, args.dtc_data_dir)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
