"""
Vehicle Database Module
Provides access to vehicle specifications database
"""

import os
import json
import sqlite3
from sqlalchemy import create_engine, Column, Integer, String, Float, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship

Base = declarative_base()

class Make(Base):
    """Vehicle make model"""
    __tablename__ = 'makes'

    id = Column(Integer, primary_key=True)
    name = Column(String, unique=True, nullable=False)
    models = relationship("Model", back_populates="make", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Make(name='{self.name}')>"

class Model(Base):
    """Vehicle model model"""
    __tablename__ = 'models'

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False)
    make_id = Column(Integer, ForeignKey('makes.id'), nullable=False)
    make = relationship("Make", back_populates="models")
    vehicles = relationship("Vehicle", back_populates="model", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Model(name='{self.name}')>"

class Vehicle(Base):
    """Vehicle specification model"""
    __tablename__ = 'vehicles'

    id = Column(Integer, primary_key=True)
    year = Column(Integer, nullable=False)
    model_id = Column(Integer, ForeignKey('models.id'), nullable=False)
    model = relationship("Model", back_populates="vehicles")

    # Basic specifications
    engine_type = Column(String)
    engine_displacement = Column(Float)
    fuel_type = Column(String)
    transmission_type = Column(String)
    num_cylinders = Column(Integer)

    # Diagnostic information
    obd_protocol = Column(String)
    manufacturer_protocol = Column(String)

    # Additional specifications
    generation = Column(String)  # e.g., "E46", "W204", "Mk5"
    variant = Column(String)     # e.g., "320i", "C300", "GTI"
    body_style = Column(String)  # e.g., "Sedan", "Coupe", "Wagon"
    drive_type = Column(String)  # e.g., "FWD", "RWD", "AWD"

    # Diagnostic connector information
    obd_port_location = Column(String)  # e.g., "Under dashboard, driver's side"
    ecu_location = Column(String)       # e.g., "Engine bay, passenger side"

    # Additional diagnostic information
    security_level = Column(Integer)    # Security access level required
    special_notes = Column(String)      # Special notes for diagnostics

    # Identifiers
    vin_pattern = Column(String)        # Pattern for VIN validation
    engine_code = Column(String)        # Engine code (e.g., "N55", "EA888")

    def __repr__(self):
        return f"<Vehicle(year={self.year}, model={self.model.name}, variant='{self.variant}')>"


class ECU(Base):
    """ECU information model"""
    __tablename__ = 'ecus'

    id = Column(Integer, primary_key=True)
    vehicle_id = Column(Integer, ForeignKey('vehicles.id'), nullable=False)
    vehicle = relationship("Vehicle")

    name = Column(String, nullable=False)  # e.g., "Engine Control Module", "Transmission Control Module"
    code = Column(String)                  # Manufacturer code for the ECU
    location = Column(String)              # Physical location in the vehicle
    protocol = Column(String)              # Communication protocol
    address = Column(String)               # Network address
    security_type = Column(String)         # Security access type
    flash_method = Column(String)          # Flashing method
    special_notes = Column(String)         # Special notes for this ECU

    def __repr__(self):
        return f"<ECU(name='{self.name}', vehicle_id={self.vehicle_id})>"

class VehicleDatabase:
    """Vehicle database manager"""

    def __init__(self, db_path='database/vehicles.db'):
        """
        Initialize the vehicle database

        Args:
            db_path (str): Path to the SQLite database file
        """
        self.db_path = db_path
        self.engine = create_engine(f'sqlite:///{db_path}')
        self.Session = sessionmaker(bind=self.engine)

        # Create tables if they don't exist
        Base.metadata.create_all(self.engine)

        # Load sample data if database is empty
        self._load_sample_data_if_empty()

    def _load_sample_data_if_empty(self):
        """Load sample vehicle data if the database is empty"""
        session = self.Session()

        # Check if database is empty
        if session.query(Make).count() == 0:
            # Sample data - in a real application, this would be much more comprehensive
            sample_data = {
                "Toyota": {
                    "Camry": [
                        {"year": 2020, "engine_type": "Inline-4", "engine_displacement": 2.5,
                         "fuel_type": "Gasoline", "transmission_type": "Automatic",
                         "num_cylinders": 4, "obd_protocol": "ISO15765-4 (CAN 11/500)",
                         "manufacturer_protocol": "Toyota ISO-TP"},
                        {"year": 2021, "engine_type": "Inline-4", "engine_displacement": 2.5,
                         "fuel_type": "Gasoline", "transmission_type": "Automatic",
                         "num_cylinders": 4, "obd_protocol": "ISO15765-4 (CAN 11/500)",
                         "manufacturer_protocol": "Toyota ISO-TP"}
                    ],
                    "Corolla": [
                        {"year": 2020, "engine_type": "Inline-4", "engine_displacement": 1.8,
                         "fuel_type": "Gasoline", "transmission_type": "CVT",
                         "num_cylinders": 4, "obd_protocol": "ISO15765-4 (CAN 11/500)",
                         "manufacturer_protocol": "Toyota ISO-TP"}
                    ]
                },
                "Honda": {
                    "Civic": [
                        {"year": 2020, "engine_type": "Inline-4", "engine_displacement": 1.5,
                         "fuel_type": "Gasoline", "transmission_type": "CVT",
                         "num_cylinders": 4, "obd_protocol": "ISO15765-4 (CAN 11/500)",
                         "manufacturer_protocol": "Honda Diag-H"}
                    ],
                    "Accord": [
                        {"year": 2020, "engine_type": "Inline-4", "engine_displacement": 1.5,
                         "fuel_type": "Gasoline", "transmission_type": "CVT",
                         "num_cylinders": 4, "obd_protocol": "ISO15765-4 (CAN 11/500)",
                         "manufacturer_protocol": "Honda Diag-H"}
                    ]
                },
                "Ford": {
                    "F-150": [
                        {"year": 2020, "engine_type": "V8", "engine_displacement": 5.0,
                         "fuel_type": "Gasoline", "transmission_type": "Automatic",
                         "num_cylinders": 8, "obd_protocol": "ISO15765-4 (CAN 11/500)",
                         "manufacturer_protocol": "Ford UDS"}
                    ],
                    "Mustang": [
                        {"year": 2020, "engine_type": "V8", "engine_displacement": 5.0,
                         "fuel_type": "Gasoline", "transmission_type": "Manual",
                         "num_cylinders": 8, "obd_protocol": "ISO15765-4 (CAN 11/500)",
                         "manufacturer_protocol": "Ford UDS"}
                    ]
                }
            }

            # Add sample data to database
            for make_name, models in sample_data.items():
                make = Make(name=make_name)
                session.add(make)
                session.flush()  # Flush to get the make ID

                for model_name, vehicles in models.items():
                    model = Model(name=model_name, make_id=make.id)
                    session.add(model)
                    session.flush()  # Flush to get the model ID

                    for vehicle_data in vehicles:
                        vehicle = Vehicle(
                            model_id=model.id,
                            year=vehicle_data["year"],
                            engine_type=vehicle_data["engine_type"],
                            engine_displacement=vehicle_data["engine_displacement"],
                            fuel_type=vehicle_data["fuel_type"],
                            transmission_type=vehicle_data["transmission_type"],
                            num_cylinders=vehicle_data["num_cylinders"],
                            obd_protocol=vehicle_data["obd_protocol"],
                            manufacturer_protocol=vehicle_data["manufacturer_protocol"]
                        )
                        session.add(vehicle)

            session.commit()

            # Try to import additional vehicle data
            self._import_additional_vehicle_data(session)

        session.close()

    def _import_additional_vehicle_data(self, session):
        """Import additional vehicle data from JSON files"""
        try:
            import os
            import json
            import importlib.util

            # Check if the vehicle_data directory exists
            vehicle_data_dir = os.path.join(os.path.dirname(self.db_path), 'vehicle_data')
            if not os.path.exists(vehicle_data_dir):
                return

            # Check if the import script exists
            import_script_path = os.path.join(vehicle_data_dir, 'import_vehicle_data.py')
            if os.path.exists(import_script_path):
                # Try to import the script
                spec = importlib.util.spec_from_file_location("import_vehicle_data", import_script_path)
                import_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(import_module)

                # Import all vehicle data
                for file_name in ['bmw_models.json', 'vw_models.json', 'mercedes_models.json',
                                 'audi_models.json', 'nissan_models.json']:
                    file_path = os.path.join(vehicle_data_dir, file_name)
                    if os.path.exists(file_path):
                        try:
                            import_module.import_vehicle_data(self, file_path)
                        except Exception as e:
                            print(f"Error importing vehicle data from {file_path}: {e}")
        except Exception as e:
            print(f"Error importing additional vehicle data: {e}")

    def get_makes(self):
        """
        Get a list of all vehicle makes

        Returns:
            list: List of make names
        """
        session = self.Session()
        makes = [make.name for make in session.query(Make).order_by(Make.name).all()]
        session.close()
        return makes

    def get_models(self, make):
        """
        Get a list of models for a specific make

        Args:
            make (str): The vehicle make

        Returns:
            list: List of model names
        """
        session = self.Session()
        make_obj = session.query(Make).filter(Make.name == make).first()

        if not make_obj:
            session.close()
            return []

        models = [model.name for model in session.query(Model).filter(Model.make_id == make_obj.id).order_by(Model.name).all()]
        session.close()
        return models

    def get_years(self, make, model):
        """
        Get a list of years for a specific make and model

        Args:
            make (str): The vehicle make
            model (str): The vehicle model

        Returns:
            list: List of years
        """
        session = self.Session()
        make_obj = session.query(Make).filter(Make.name == make).first()

        if not make_obj:
            session.close()
            return []

        model_obj = session.query(Model).filter(Model.name == model, Model.make_id == make_obj.id).first()

        if not model_obj:
            session.close()
            return []

        years = [vehicle.year for vehicle in session.query(Vehicle).filter(Vehicle.model_id == model_obj.id).order_by(Vehicle.year.desc()).all()]
        session.close()
        return years

    def get_vehicle(self, make, model, year, variant=None):
        """
        Get vehicle specifications for a specific make, model, and year

        Args:
            make (str): The vehicle make
            model (str): The vehicle model
            year (int): The vehicle year
            variant (str, optional): The vehicle variant

        Returns:
            dict: Vehicle specifications or None if not found
        """
        session = self.Session()
        make_obj = session.query(Make).filter(Make.name == make).first()

        if not make_obj:
            session.close()
            return None

        model_obj = session.query(Model).filter(Model.name == model, Model.make_id == make_obj.id).first()

        if not model_obj:
            session.close()
            return None

        # Build the query
        query = session.query(Vehicle).filter(
            Vehicle.model_id == model_obj.id,
            Vehicle.year == year
        )

        # Add variant filter if provided
        if variant:
            query = query.filter(Vehicle.variant == variant)

        vehicle = query.first()

        if not vehicle:
            session.close()
            return None

        # Convert to dictionary
        vehicle_dict = {
            'make': make,
            'model': model,
            'year': vehicle.year,

            # Basic specifications
            'engine_type': vehicle.engine_type,
            'engine_displacement': vehicle.engine_displacement,
            'fuel_type': vehicle.fuel_type,
            'transmission_type': vehicle.transmission_type,
            'num_cylinders': vehicle.num_cylinders,

            # Diagnostic information
            'obd_protocol': vehicle.obd_protocol,
            'manufacturer_protocol': vehicle.manufacturer_protocol,

            # Additional specifications
            'generation': vehicle.generation,
            'variant': vehicle.variant,
            'body_style': vehicle.body_style,
            'drive_type': vehicle.drive_type,

            # Diagnostic connector information
            'obd_port_location': vehicle.obd_port_location,
            'ecu_location': vehicle.ecu_location,

            # Additional diagnostic information
            'security_level': vehicle.security_level,
            'special_notes': vehicle.special_notes,

            # Identifiers
            'vin_pattern': vehicle.vin_pattern,
            'engine_code': vehicle.engine_code
        }

        # Get ECUs for this vehicle
        ecus = session.query(ECU).filter(ECU.vehicle_id == vehicle.id).all()
        vehicle_dict['ecus'] = []

        for ecu in ecus:
            ecu_dict = {
                'name': ecu.name,
                'code': ecu.code,
                'location': ecu.location,
                'protocol': ecu.protocol,
                'address': ecu.address,
                'security_type': ecu.security_type,
                'flash_method': ecu.flash_method,
                'special_notes': ecu.special_notes
            }
            vehicle_dict['ecus'].append(ecu_dict)

        session.close()
        return vehicle_dict

    def get_variants(self, make, model, year):
        """
        Get a list of variants for a specific make, model, and year

        Args:
            make (str): The vehicle make
            model (str): The vehicle model
            year (int): The vehicle year

        Returns:
            list: List of variant names
        """
        session = self.Session()
        make_obj = session.query(Make).filter(Make.name == make).first()

        if not make_obj:
            session.close()
            return []

        model_obj = session.query(Model).filter(Model.name == model, Model.make_id == make_obj.id).first()

        if not model_obj:
            session.close()
            return []

        variants = [vehicle.variant for vehicle in session.query(Vehicle).filter(
            Vehicle.model_id == model_obj.id,
            Vehicle.year == year
        ).all() if vehicle.variant]

        session.close()
        return variants

    def add_ecu(self, make, model, year, variant, ecu_data):
        """
        Add an ECU to a vehicle

        Args:
            make (str): The vehicle make
            model (str): The vehicle model
            year (int): The vehicle year
            variant (str): The vehicle variant
            ecu_data (dict): ECU data

        Returns:
            bool: True if successful, False otherwise
        """
        session = self.Session()
        make_obj = session.query(Make).filter(Make.name == make).first()

        if not make_obj:
            session.close()
            return False

        model_obj = session.query(Model).filter(Model.name == model, Make.id == make_obj.id).first()

        if not model_obj:
            session.close()
            return False

        vehicle = session.query(Vehicle).filter(
            Vehicle.model_id == model_obj.id,
            Vehicle.year == year,
            Vehicle.variant == variant
        ).first()

        if not vehicle:
            session.close()
            return False

        # Create new ECU
        ecu = ECU(
            vehicle_id=vehicle.id,
            name=ecu_data.get('name', ''),
            code=ecu_data.get('code', ''),
            location=ecu_data.get('location', ''),
            protocol=ecu_data.get('protocol', ''),
            address=ecu_data.get('address', ''),
            security_type=ecu_data.get('security_type', ''),
            flash_method=ecu_data.get('flash_method', ''),
            special_notes=ecu_data.get('special_notes', '')
        )

        session.add(ecu)
        session.commit()
        session.close()

        return True
