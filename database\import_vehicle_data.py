#!/usr/bin/env python3
"""
Import Vehicle Data Script
This script imports vehicle data from JSON files into the vehicle database.
"""

import os
import sys
import json
import logging
import argparse
from vehicle_db import VehicleDatabase
from vehicle_data_importer import VehicleDataImporter

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("vehicle_import.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("import_vehicle_data")

def import_vehicle_data(db_path="database/vehicles.db", data_dir="database/vehicle_data"):
    """
    Import vehicle data from JSON files
    
    Args:
        db_path (str): Path to the database file
        data_dir (str): Path to the data directory
        
    Returns:
        int: Number of vehicles imported
    """
    try:
        # Initialize database
        vehicle_db = VehicleDatabase(db_path)
        
        # Initialize importer
        importer = VehicleDataImporter(vehicle_db, data_dir)
        
        # Get list of JSON files
        json_files = [f for f in os.listdir(data_dir) if f.endswith('.json')]
        
        if not json_files:
            logger.warning(f"No JSON files found in {data_dir}")
            return 0
        
        # Import data from each file
        total_count = 0
        for file_name in json_files:
            file_path = os.path.join(data_dir, file_name)
            count = importer.import_from_json(file_path)
            total_count += count
            logger.info(f"Imported {count} vehicles from {file_name}")
        
        logger.info(f"Total vehicles imported: {total_count}")
        return total_count
    except Exception as e:
        logger.error(f"Error importing vehicle data: {e}")
        return 0

def import_vehicle_data_from_file(db_path, file_path):
    """
    Import vehicle data from a single JSON file
    
    Args:
        db_path (str): Path to the database file
        file_path (str): Path to the JSON file
        
    Returns:
        int: Number of vehicles imported
    """
    try:
        # Initialize database
        vehicle_db = VehicleDatabase(db_path)
        
        # Initialize importer
        importer = VehicleDataImporter(vehicle_db, os.path.dirname(file_path))
        
        # Import data
        count = importer.import_from_json(file_path)
        
        logger.info(f"Imported {count} vehicles from {file_path}")
        return count
    except Exception as e:
        logger.error(f"Error importing vehicle data from {file_path}: {e}")
        return 0

def scrape_vehicle_data(db_path, make, model=None, year_range=None, output_dir="database/vehicle_data"):
    """
    Scrape vehicle data from online sources
    
    Args:
        db_path (str): Path to the database file
        make (str): The vehicle make
        model (str, optional): The vehicle model
        year_range (tuple, optional): The year range (start_year, end_year)
        output_dir (str): Path to the output directory
        
    Returns:
        int: Number of vehicles imported
    """
    try:
        # Initialize database
        vehicle_db = VehicleDatabase(db_path)
        
        # Initialize importer
        importer = VehicleDataImporter(vehicle_db, output_dir)
        
        # Scrape data
        data = importer.scrape_car_data(make, model, year_range)
        
        # Count vehicles
        count = 0
        for make_name, models in data.items():
            for model_name, vehicles in models.items():
                count += len(vehicles)
        
        logger.info(f"Scraped and imported {count} vehicles")
        return count
    except Exception as e:
        logger.error(f"Error scraping vehicle data: {e}")
        return 0

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Import vehicle data into the database")
    parser.add_argument("--db", default="database/vehicles.db", help="Path to the database file")
    parser.add_argument("--dir", default="database/vehicle_data", help="Path to the data directory")
    parser.add_argument("--file", help="Path to a specific JSON file to import")
    parser.add_argument("--scrape", action="store_true", help="Scrape vehicle data from online sources")
    parser.add_argument("--make", help="Vehicle make (for scraping)")
    parser.add_argument("--model", help="Vehicle model (for scraping)")
    parser.add_argument("--start-year", type=int, help="Start year (for scraping)")
    parser.add_argument("--end-year", type=int, help="End year (for scraping)")
    
    args = parser.parse_args()
    
    if args.scrape:
        if not args.make:
            logger.error("Make is required for scraping")
            return 1
        
        year_range = None
        if args.start_year and args.end_year:
            year_range = (args.start_year, args.end_year)
        
        scrape_vehicle_data(args.db, args.make, args.model, year_range, args.dir)
    elif args.file:
        import_vehicle_data_from_file(args.db, args.file)
    else:
        import_vehicle_data(args.db, args.dir)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
