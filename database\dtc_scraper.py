#!/usr/bin/env python3
"""
DTC Scraper Module
This module provides functionality for scraping DTC information from online sources.
"""

import logging
import time
import json
import os
import re
import random
import requests
from bs4 import BeautifulSoup
from typing import Dict, List, Optional, Any, Tuple, Union
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

logger = logging.getLogger("database.dtc_scraper")

class DTCScraper:
    """DTC scraper"""
    
    def __init__(self, output_dir="dtc_data"):
        """
        Initialize the DTC scraper
        
        Args:
            output_dir (str): The output directory for scraped data
        """
        self.output_dir = output_dir
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # Create output directory if it doesn't exist
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
    
    def scrape_obd_codes(self):
        """
        Scrape OBD codes from obd-codes.com
        
        Returns:
            dict: Dictionary of DTC codes
        """
        try:
            logger.info("Scraping OBD codes from obd-codes.com")
            
            # Base URL
            base_url = "https://www.obd-codes.com"
            
            # Get the main page
            response = self.session.get(f"{base_url}/trouble-codes")
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find all code categories
            categories = []
            for link in soup.select('a[href^="/trouble-codes"]'):
                href = link.get('href')
                if href and href != "/trouble-codes" and "manufacturer" not in href:
                    categories.append(href)
            
            # Remove duplicates
            categories = list(set(categories))
            
            # Dictionary to store all codes
            all_codes = {}
            
            # Process each category
            for category in categories:
                logger.info(f"Processing category: {category}")
                
                # Get the category page
                category_url = f"{base_url}{category}"
                response = self.session.get(category_url)
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Find all code links
                code_links = []
                for link in soup.select('a[href^="/p"]'):
                    href = link.get('href')
                    if href and re.match(r'/[PBCU][0-9]{4}', href):
                        code_links.append(href)
                
                # Remove duplicates
                code_links = list(set(code_links))
                
                # Process each code
                for code_link in code_links:
                    # Add delay to avoid overloading the server
                    time.sleep(random.uniform(0.5, 1.5))
                    
                    # Get the code page
                    code_url = f"{base_url}{code_link}"
                    response = self.session.get(code_url)
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # Extract code information
                    code = code_link.strip('/')
                    title = soup.select_one('h1').text.strip() if soup.select_one('h1') else ""
                    description = ""
                    possible_causes = []
                    
                    # Extract description and possible causes
                    content = soup.select_one('#content')
                    if content:
                        # Extract description
                        for p in content.select('p'):
                            if p.text.strip() and "Possible causes" not in p.text:
                                description += p.text.strip() + " "
                        
                        # Extract possible causes
                        causes_list = content.select_one('ul')
                        if causes_list:
                            for li in causes_list.select('li'):
                                possible_causes.append(li.text.strip())
                    
                    # Store code information
                    all_codes[code] = {
                        'code': code,
                        'title': title,
                        'description': description.strip(),
                        'possible_causes': possible_causes,
                        'source': 'obd-codes.com'
                    }
                    
                    logger.info(f"Processed code: {code}")
            
            # Save all codes to file
            self._save_codes(all_codes, "obd_codes.json")
            
            return all_codes
        except Exception as e:
            logger.error(f"Error scraping OBD codes: {e}")
            return {}
    
    def scrape_trouble_code_help(self):
        """
        Scrape DTC codes from troublecodehelp.com
        
        Returns:
            dict: Dictionary of DTC codes
        """
        try:
            logger.info("Scraping DTC codes from troublecodehelp.com")
            
            # Base URL
            base_url = "https://www.troublecodehelp.com"
            
            # Get the main page
            response = self.session.get(f"{base_url}/dtc-codes")
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find all code categories
            categories = []
            for link in soup.select('a[href^="/dtc-codes/"]'):
                href = link.get('href')
                if href and href != "/dtc-codes":
                    categories.append(href)
            
            # Remove duplicates
            categories = list(set(categories))
            
            # Dictionary to store all codes
            all_codes = {}
            
            # Process each category
            for category in categories:
                logger.info(f"Processing category: {category}")
                
                # Get the category page
                category_url = f"{base_url}{category}"
                response = self.session.get(category_url)
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Find all code links
                code_links = []
                for link in soup.select('a[href^="/dtc-codes/"]'):
                    href = link.get('href')
                    if href and href != category and re.search(r'/[PBCU][0-9]{4}', href):
                        code_links.append(href)
                
                # Remove duplicates
                code_links = list(set(code_links))
                
                # Process each code
                for code_link in code_links:
                    # Add delay to avoid overloading the server
                    time.sleep(random.uniform(0.5, 1.5))
                    
                    # Get the code page
                    code_url = f"{base_url}{code_link}"
                    response = self.session.get(code_url)
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # Extract code information
                    code_match = re.search(r'([PBCU][0-9]{4})', code_link)
                    if not code_match:
                        continue
                    
                    code = code_match.group(1)
                    title = soup.select_one('h1').text.strip() if soup.select_one('h1') else ""
                    description = ""
                    possible_causes = []
                    
                    # Extract description and possible causes
                    content = soup.select_one('.entry-content')
                    if content:
                        # Extract description
                        for p in content.select('p'):
                            if p.text.strip() and "Possible causes" not in p.text:
                                description += p.text.strip() + " "
                        
                        # Extract possible causes
                        causes_list = content.select_one('ul')
                        if causes_list:
                            for li in causes_list.select('li'):
                                possible_causes.append(li.text.strip())
                    
                    # Store code information
                    all_codes[code] = {
                        'code': code,
                        'title': title,
                        'description': description.strip(),
                        'possible_causes': possible_causes,
                        'source': 'troublecodehelp.com'
                    }
                    
                    logger.info(f"Processed code: {code}")
            
            # Save all codes to file
            self._save_codes(all_codes, "trouble_code_help.json")
            
            return all_codes
        except Exception as e:
            logger.error(f"Error scraping DTC codes: {e}")
            return {}
    
    def scrape_manufacturer_codes(self, manufacturer):
        """
        Scrape manufacturer-specific codes
        
        Args:
            manufacturer (str): The manufacturer name
            
        Returns:
            dict: Dictionary of manufacturer-specific codes
        """
        try:
            logger.info(f"Scraping manufacturer-specific codes for {manufacturer}")
            
            # Base URL
            base_url = "https://www.obd-codes.com"
            
            # Get the manufacturer page
            response = self.session.get(f"{base_url}/trouble-codes/manufacturer/{manufacturer.lower()}")
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find all code links
            code_links = []
            for link in soup.select('a[href^="/p"]'):
                href = link.get('href')
                if href and re.match(r'/[PBCU][0-9]{4}', href):
                    code_links.append(href)
            
            # Remove duplicates
            code_links = list(set(code_links))
            
            # Dictionary to store all codes
            all_codes = {}
            
            # Process each code
            for code_link in code_links:
                # Add delay to avoid overloading the server
                time.sleep(random.uniform(0.5, 1.5))
                
                # Get the code page
                code_url = f"{base_url}{code_link}"
                response = self.session.get(code_url)
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Extract code information
                code = code_link.strip('/')
                title = soup.select_one('h1').text.strip() if soup.select_one('h1') else ""
                description = ""
                possible_causes = []
                
                # Extract description and possible causes
                content = soup.select_one('#content')
                if content:
                    # Extract description
                    for p in content.select('p'):
                        if p.text.strip() and "Possible causes" not in p.text:
                            description += p.text.strip() + " "
                    
                    # Extract possible causes
                    causes_list = content.select_one('ul')
                    if causes_list:
                        for li in causes_list.select('li'):
                            possible_causes.append(li.text.strip())
                
                # Store code information
                all_codes[code] = {
                    'code': code,
                    'title': title,
                    'description': description.strip(),
                    'possible_causes': possible_causes,
                    'manufacturer': manufacturer,
                    'source': 'obd-codes.com'
                }
                
                logger.info(f"Processed code: {code}")
            
            # Save all codes to file
            self._save_codes(all_codes, f"{manufacturer.lower()}_codes.json")
            
            return all_codes
        except Exception as e:
            logger.error(f"Error scraping manufacturer-specific codes: {e}")
            return {}
    
    def scrape_all_manufacturers(self):
        """
        Scrape codes for all manufacturers
        
        Returns:
            dict: Dictionary of all manufacturer-specific codes
        """
        try:
            logger.info("Scraping codes for all manufacturers")
            
            # List of manufacturers
            manufacturers = [
                'Toyota', 'Honda', 'Nissan', 'Hyundai', 'Kia', 'Mazda', 'Subaru', 'Mitsubishi',
                'BMW', 'Mercedes', 'Volkswagen', 'Audi', 'Porsche', 'Volvo',
                'Ford', 'GM', 'Chrysler', 'Dodge', 'Jeep'
            ]
            
            # Dictionary to store all codes
            all_codes = {}
            
            # Process each manufacturer
            for manufacturer in manufacturers:
                manufacturer_codes = self.scrape_manufacturer_codes(manufacturer)
                all_codes.update(manufacturer_codes)
            
            # Save all codes to file
            self._save_codes(all_codes, "all_manufacturer_codes.json")
            
            return all_codes
        except Exception as e:
            logger.error(f"Error scraping all manufacturers: {e}")
            return {}
    
    def scrape_all(self):
        """
        Scrape all DTC codes
        
        Returns:
            dict: Dictionary of all DTC codes
        """
        try:
            logger.info("Scraping all DTC codes")
            
            # Scrape OBD codes
            obd_codes = self.scrape_obd_codes()
            
            # Scrape trouble code help
            trouble_codes = self.scrape_trouble_code_help()
            
            # Scrape manufacturer-specific codes
            manufacturer_codes = self.scrape_all_manufacturers()
            
            # Merge all codes
            all_codes = {}
            all_codes.update(obd_codes)
            
            # Update with trouble codes
            for code, info in trouble_codes.items():
                if code in all_codes:
                    # Merge information
                    if info['description'] and not all_codes[code]['description']:
                        all_codes[code]['description'] = info['description']
                    
                    if info['possible_causes']:
                        all_codes[code]['possible_causes'].extend(info['possible_causes'])
                        all_codes[code]['possible_causes'] = list(set(all_codes[code]['possible_causes']))
                else:
                    all_codes[code] = info
            
            # Update with manufacturer codes
            for code, info in manufacturer_codes.items():
                if code in all_codes:
                    # Merge information
                    if 'manufacturer' in info:
                        all_codes[code]['manufacturer'] = info['manufacturer']
                    
                    if info['description'] and not all_codes[code]['description']:
                        all_codes[code]['description'] = info['description']
                    
                    if info['possible_causes']:
                        all_codes[code]['possible_causes'].extend(info['possible_causes'])
                        all_codes[code]['possible_causes'] = list(set(all_codes[code]['possible_causes']))
                else:
                    all_codes[code] = info
            
            # Save all codes to file
            self._save_codes(all_codes, "all_dtc_codes.json")
            
            return all_codes
        except Exception as e:
            logger.error(f"Error scraping all DTC codes: {e}")
            return {}
    
    def _save_codes(self, codes, filename):
        """
        Save codes to file
        
        Args:
            codes (dict): Dictionary of codes
            filename (str): The filename
        """
        try:
            # Save to file
            with open(os.path.join(self.output_dir, filename), 'w') as f:
                json.dump(codes, f, indent=2)
            
            logger.info(f"Saved {len(codes)} codes to {filename}")
        except Exception as e:
            logger.error(f"Error saving codes to file: {e}")
    
    def load_codes(self, filename):
        """
        Load codes from file
        
        Args:
            filename (str): The filename
            
        Returns:
            dict: Dictionary of codes
        """
        try:
            # Load from file
            with open(os.path.join(self.output_dir, filename), 'r') as f:
                codes = json.load(f)
            
            logger.info(f"Loaded {len(codes)} codes from {filename}")
            return codes
        except Exception as e:
            logger.error(f"Error loading codes from file: {e}")
            return {}
