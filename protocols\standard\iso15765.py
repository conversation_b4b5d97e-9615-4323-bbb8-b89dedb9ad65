#!/usr/bin/env python3
"""
ISO15765 Protocol Handler
This module provides the protocol handler for ISO15765-4 (CAN) protocol.
"""

import logging
import time
import struct
from enum import Enum, auto

from protocols.protocol_handler import BaseProtocol, ProtocolType, SecurityLevel, SecurityAccessStatus, ProtocolError, CommunicationError

logger = logging.getLogger("protocol.ISO15765")

class CANFrameType(Enum):
    """CAN frame types"""
    STANDARD = auto()  # 11-bit identifier
    EXTENDED = auto()  # 29-bit identifier

class ISO15765Protocol(BaseProtocol):
    """ISO15765-4 (CAN) protocol handler"""
    
    def __init__(self, interface=None, baudrate=500000, frame_type=CANFrameType.STANDARD, tx_id=0x7E0, rx_id=0x7E8):
        """
        Initialize the ISO15765 protocol handler
        
        Args:
            interface: The hardware interface to use
            baudrate (int): The CAN baudrate (default: 500000)
            frame_type (CANFrameType): The CAN frame type (default: STANDARD)
            tx_id (int): The transmit CAN ID (default: 0x7E0)
            rx_id (int): The receive CAN ID (default: 0x7E8)
        """
        super().__init__(interface)
        self.baudrate = baudrate
        self.frame_type = frame_type
        self.tx_id = tx_id
        self.rx_id = rx_id
        self.max_data_length = 8  # Maximum data length in a CAN frame
        self.padding_byte = 0x00  # Padding byte for CAN frames
        self.flow_control_timeout = 1.0  # Flow control timeout in seconds
        self.separation_time = 0.01  # Minimum time between consecutive frames in seconds
        
        # ISO-TP parameters
        self.block_size = 0  # Number of consecutive frames before flow control (0 = unlimited)
        self.stmin = 0  # Minimum separation time between consecutive frames in milliseconds
        
        # Service IDs
        self.sid_diagnostic_session_control = 0x10
        self.sid_ecu_reset = 0x11
        self.sid_security_access = 0x27
        self.sid_communication_control = 0x28
        self.sid_tester_present = 0x3E
        self.sid_read_data_by_identifier = 0x22
        self.sid_read_memory_by_address = 0x23
        self.sid_read_scaling_data_by_identifier = 0x24
        self.sid_read_data_by_periodic_identifier = 0x2A
        self.sid_dynamically_define_data_identifier = 0x2C
        self.sid_write_data_by_identifier = 0x2E
        self.sid_write_memory_by_address = 0x3D
        self.sid_clear_diagnostic_information = 0x14
        self.sid_read_dtc_information = 0x19
        self.sid_input_output_control_by_identifier = 0x2F
        self.sid_routine_control = 0x31
        self.sid_request_download = 0x34
        self.sid_request_upload = 0x35
        self.sid_transfer_data = 0x36
        self.sid_request_transfer_exit = 0x37
        
        # Negative response code
        self.negative_response_sid = 0x7F
        
        # Negative response codes
        self.nrc_general_reject = 0x10
        self.nrc_service_not_supported = 0x11
        self.nrc_sub_function_not_supported = 0x12
        self.nrc_incorrect_message_length_or_invalid_format = 0x13
        self.nrc_response_too_long = 0x14
        self.nrc_busy_repeat_request = 0x21
        self.nrc_conditions_not_correct = 0x22
        self.nrc_request_sequence_error = 0x24
        self.nrc_no_response_from_subnet_component = 0x25
        self.nrc_failure_prevents_execution_of_requested_action = 0x26
        self.nrc_request_out_of_range = 0x31
        self.nrc_security_access_denied = 0x33
        self.nrc_invalid_key = 0x35
        self.nrc_exceed_number_of_attempts = 0x36
        self.nrc_required_time_delay_not_expired = 0x37
        self.nrc_upload_download_not_accepted = 0x70
        self.nrc_transfer_data_suspended = 0x71
        self.nrc_general_programming_failure = 0x72
        self.nrc_wrong_block_sequence_counter = 0x73
        self.nrc_response_pending = 0x78
        self.nrc_subfunction_not_supported_in_active_session = 0x7E
        self.nrc_service_not_supported_in_active_session = 0x7F
        
        # Session types
        self.session_default = 0x01
        self.session_programming = 0x02
        self.session_extended_diagnostic = 0x03
        self.session_safety_system_diagnostic = 0x04
        
        # Reset types
        self.reset_hard = 0x01
        self.reset_key_off_on = 0x02
        self.reset_soft = 0x03
        self.reset_enable_rapid_power_shutdown = 0x04
        self.reset_disable_rapid_power_shutdown = 0x05
        
        # Security access types
        self.security_request_seed = 0x01
        self.security_send_key = 0x02
        
        # DTC status mask
        self.dtc_status_test_failed = 0x01
        self.dtc_status_test_failed_this_operation_cycle = 0x02
        self.dtc_status_pending = 0x04
        self.dtc_status_confirmed = 0x08
        self.dtc_status_test_not_completed_since_last_clear = 0x10
        self.dtc_status_test_failed_since_last_clear = 0x20
        self.dtc_status_test_not_completed_this_operation_cycle = 0x40
        self.dtc_status_warning_indicator_requested = 0x80
    
    def connect(self):
        """
        Connect to the vehicle
        
        Returns:
            bool: True if connected, False otherwise
        """
        if not self.interface:
            logger.error("No interface provided")
            return False
        
        try:
            # Configure the interface
            self.interface.set_protocol("CAN")
            self.interface.set_baudrate(self.baudrate)
            
            # Set CAN IDs
            self.interface.set_can_id_filter(self.rx_id)
            
            # Test connection
            if self.test_connection(self.interface):
                self.connected = True
                logger.info(f"Connected to vehicle using ISO15765 protocol (CAN ID: 0x{self.tx_id:X}/0x{self.rx_id:X})")
                return True
            else:
                logger.error("Failed to connect to vehicle")
                return False
        except Exception as e:
            logger.error(f"Error connecting to vehicle: {e}")
            return False
    
    def disconnect(self):
        """
        Disconnect from the vehicle
        
        Returns:
            bool: True if disconnected, False otherwise
        """
        if not self.connected:
            return True
        
        try:
            # Exit diagnostic session
            self.exit_diagnostic_session()
            
            # Reset interface
            self.interface.reset()
            
            self.connected = False
            logger.info("Disconnected from vehicle")
            return True
        except Exception as e:
            logger.error(f"Error disconnecting from vehicle: {e}")
            return False
    
    def test_connection(self, connection):
        """
        Test if the connection uses ISO15765 protocol
        
        Args:
            connection: The connection to test
            
        Returns:
            bool: True if the connection uses ISO15765 protocol, False otherwise
        """
        try:
            # Try to send a tester present message
            response = self.send_command([self.sid_tester_present, 0x00])
            
            # Check if response is valid
            if response and len(response) >= 2 and response[0] == self.sid_tester_present + 0x40:
                return True
            
            return False
        except Exception as e:
            logger.debug(f"Error testing ISO15765 connection: {e}")
            return False
    
    def send_command(self, command, response_required=True):
        """
        Send a command to the vehicle
        
        Args:
            command: The command to send (list of bytes or bytes)
            response_required (bool): Whether a response is required
            
        Returns:
            The response from the vehicle
        """
        if not self.connected:
            raise ProtocolError("Not connected to vehicle")
        
        try:
            # Convert command to bytes if it's a list
            if isinstance(command, list):
                command = bytes(command)
            
            # Send command using ISO-TP
            self._send_iso_tp_message(command)
            
            # Wait for response if required
            if response_required:
                response = self._receive_iso_tp_message()
                
                # Check for negative response
                if response and len(response) >= 3 and response[0] == self.negative_response_sid:
                    # Check if response pending
                    if response[2] == self.nrc_response_pending:
                        # Wait for the actual response
                        return self._wait_for_response()
                    
                    # Handle other negative responses
                    self._handle_negative_response(response)
                
                return response
            
            return None
        except Exception as e:
            logger.error(f"Error sending command: {e}")
            raise CommunicationError(f"Error sending command: {e}")
    
    def _send_iso_tp_message(self, data):
        """
        Send a message using ISO-TP protocol
        
        Args:
            data: The data to send
            
        Returns:
            bool: True if successful, False otherwise
        """
        if len(data) <= 7:
            # Single frame
            frame = bytes([len(data)]) + data
            # Pad to maximum data length
            frame = frame.ljust(self.max_data_length, bytes([self.padding_byte]))
            # Send frame
            self.interface.send_can_message(self.tx_id, frame)
            return True
        else:
            # First frame
            frame = bytes([0x10 | ((len(data) >> 8) & 0x0F), len(data) & 0xFF]) + data[:6]
            self.interface.send_can_message(self.tx_id, frame)
            
            # Wait for flow control
            flow_control = self._wait_for_flow_control()
            if not flow_control:
                raise CommunicationError("No flow control received")
            
            # Extract flow control parameters
            fc_flag = flow_control[0] & 0x0F
            block_size = flow_control[1]
            separation_time = flow_control[2]
            
            if fc_flag != 0:
                raise CommunicationError(f"Flow control flag not supported: {fc_flag}")
            
            # Send consecutive frames
            sequence_number = 1
            data_index = 6
            
            while data_index < len(data):
                # Calculate remaining data length
                remaining = len(data) - data_index
                # Calculate data length for this frame
                frame_data_length = min(remaining, 7)
                # Create frame
                frame = bytes([0x20 | (sequence_number & 0x0F)]) + data[data_index:data_index+frame_data_length]
                # Pad to maximum data length
                frame = frame.ljust(self.max_data_length, bytes([self.padding_byte]))
                # Send frame
                self.interface.send_can_message(self.tx_id, frame)
                
                # Update sequence number and data index
                sequence_number = (sequence_number + 1) & 0x0F
                data_index += frame_data_length
                
                # Check if we need to wait for flow control
                if block_size > 0 and sequence_number % block_size == 0 and data_index < len(data):
                    # Wait for flow control
                    flow_control = self._wait_for_flow_control()
                    if not flow_control:
                        raise CommunicationError("No flow control received")
                
                # Wait for separation time
                if separation_time > 0:
                    time.sleep(separation_time / 1000.0)
            
            return True
    
    def _receive_iso_tp_message(self):
        """
        Receive a message using ISO-TP protocol
        
        Returns:
            bytes: The received data
        """
        # Wait for first frame
        frame = self.interface.receive_can_message(self.rx_id, timeout=self.timeout)
        
        if not frame:
            return None
        
        # Check frame type
        frame_type = frame[0] & 0xF0
        
        if frame_type == 0x00:
            # Single frame
            data_length = frame[0] & 0x0F
            return frame[1:1+data_length]
        elif frame_type == 0x10:
            # First frame
            data_length = ((frame[0] & 0x0F) << 8) | frame[1]
            data = frame[2:]
            
            # Send flow control
            self._send_flow_control()
            
            # Receive consecutive frames
            sequence_number = 1
            
            while len(data) < data_length:
                # Wait for consecutive frame
                consecutive_frame = self.interface.receive_can_message(self.rx_id, timeout=self.timeout)
                
                if not consecutive_frame:
                    raise CommunicationError("No consecutive frame received")
                
                # Check frame type and sequence number
                if (consecutive_frame[0] & 0xF0) != 0x20:
                    raise CommunicationError(f"Invalid consecutive frame type: {consecutive_frame[0] & 0xF0:X}")
                
                if (consecutive_frame[0] & 0x0F) != sequence_number:
                    raise CommunicationError(f"Invalid sequence number: {consecutive_frame[0] & 0x0F}, expected: {sequence_number}")
                
                # Extract data
                frame_data = consecutive_frame[1:]
                # Calculate remaining data length
                remaining = data_length - len(data)
                # Calculate data length for this frame
                frame_data_length = min(remaining, 7)
                # Append data
                data += frame_data[:frame_data_length]
                
                # Update sequence number
                sequence_number = (sequence_number + 1) & 0x0F
            
            return data
        else:
            raise CommunicationError(f"Invalid frame type: {frame_type:X}")
    
    def _send_flow_control(self, block_size=0, separation_time=0):
        """
        Send a flow control frame
        
        Args:
            block_size (int): The block size (0 = unlimited)
            separation_time (int): The minimum separation time in milliseconds
            
        Returns:
            bool: True if successful, False otherwise
        """
        # Create flow control frame
        frame = bytes([0x30, block_size, separation_time])
        # Pad to maximum data length
        frame = frame.ljust(self.max_data_length, bytes([self.padding_byte]))
        # Send frame
        self.interface.send_can_message(self.tx_id, frame)
        return True
    
    def _wait_for_flow_control(self):
        """
        Wait for a flow control frame
        
        Returns:
            bytes: The flow control frame data
        """
        # Wait for flow control frame
        frame = self.interface.receive_can_message(self.rx_id, timeout=self.flow_control_timeout)
        
        if not frame:
            return None
        
        # Check frame type
        if (frame[0] & 0xF0) != 0x30:
            raise CommunicationError(f"Invalid flow control frame type: {frame[0] & 0xF0:X}")
        
        return frame
    
    def _wait_for_response(self):
        """
        Wait for a response after receiving a response pending
        
        Returns:
            bytes: The response data
        """
        # Wait for response
        for _ in range(10):  # Try up to 10 times
            time.sleep(0.1)  # Wait 100ms
            
            # Send tester present to keep the session alive
            self.send_command([self.sid_tester_present, 0x00], response_required=False)
            
            # Try to receive a response
            response = self._receive_iso_tp_message()
            
            if response and len(response) >= 3 and response[0] == self.negative_response_sid:
                # Check if still pending
                if response[2] == self.nrc_response_pending:
                    continue
                
                # Handle other negative responses
                self._handle_negative_response(response)
            
            return response
        
        raise CommunicationError("Timeout waiting for response")
    
    def _handle_negative_response(self, response):
        """
        Handle a negative response
        
        Args:
            response: The negative response
            
        Raises:
            ProtocolError: If the response is a negative response
        """
        if len(response) < 3:
            raise ProtocolError("Invalid negative response format")
        
        service_id = response[1]
        nrc = response[2]
        
        error_messages = {
            self.nrc_general_reject: "General reject",
            self.nrc_service_not_supported: "Service not supported",
            self.nrc_sub_function_not_supported: "Sub-function not supported",
            self.nrc_incorrect_message_length_or_invalid_format: "Incorrect message length or invalid format",
            self.nrc_response_too_long: "Response too long",
            self.nrc_busy_repeat_request: "Busy, repeat request",
            self.nrc_conditions_not_correct: "Conditions not correct",
            self.nrc_request_sequence_error: "Request sequence error",
            self.nrc_no_response_from_subnet_component: "No response from subnet component",
            self.nrc_failure_prevents_execution_of_requested_action: "Failure prevents execution of requested action",
            self.nrc_request_out_of_range: "Request out of range",
            self.nrc_security_access_denied: "Security access denied",
            self.nrc_invalid_key: "Invalid key",
            self.nrc_exceed_number_of_attempts: "Exceed number of attempts",
            self.nrc_required_time_delay_not_expired: "Required time delay not expired",
            self.nrc_upload_download_not_accepted: "Upload/download not accepted",
            self.nrc_transfer_data_suspended: "Transfer data suspended",
            self.nrc_general_programming_failure: "General programming failure",
            self.nrc_wrong_block_sequence_counter: "Wrong block sequence counter",
            self.nrc_subfunction_not_supported_in_active_session: "Sub-function not supported in active session",
            self.nrc_service_not_supported_in_active_session: "Service not supported in active session"
        }
        
        error_message = error_messages.get(nrc, f"Unknown negative response code: 0x{nrc:02X}")
        
        raise ProtocolError(f"Negative response for service 0x{service_id:02X}: {error_message}")
    
    def read_dtc(self):
        """
        Read diagnostic trouble codes
        
        Returns:
            list: List of DTCs
        """
        try:
            # Send request for stored DTCs
            response = self.send_command([self.sid_read_dtc_information, 0x02])
            
            if not response or len(response) < 3:
                return []
            
            # Extract DTCs
            dtcs = []
            for i in range(3, len(response), 4):
                if i + 3 < len(response):
                    dtc_bytes = response[i:i+3]
                    status = response[i+3]
                    
                    # Format DTC
                    dtc_type = dtc_bytes[0] >> 6
                    dtc_prefix = {0: 'P', 1: 'C', 2: 'B', 3: 'U'}[dtc_type]
                    dtc_value = ((dtc_bytes[0] & 0x3F) << 8) | dtc_bytes[1]
                    dtc = f"{dtc_prefix}{dtc_value:04X}"
                    
                    dtcs.append({
                        'code': dtc,
                        'status': status
                    })
            
            return dtcs
        except Exception as e:
            logger.error(f"Error reading DTCs: {e}")
            return []
    
    def clear_dtc(self):
        """
        Clear diagnostic trouble codes
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Send clear DTCs command
            response = self.send_command([self.sid_clear_diagnostic_information, 0xFF, 0xFF, 0xFF])
            
            # Check if response is positive
            if response and len(response) >= 1 and response[0] == self.sid_clear_diagnostic_information + 0x40:
                return True
            
            return False
        except Exception as e:
            logger.error(f"Error clearing DTCs: {e}")
            return False
    
    def read_data(self, pid):
        """
        Read data from the vehicle
        
        Args:
            pid: The parameter ID to read
            
        Returns:
            The data read from the vehicle
        """
        try:
            # Send read data command
            if isinstance(pid, int):
                # Single PID
                response = self.send_command([self.sid_read_data_by_identifier, (pid >> 8) & 0xFF, pid & 0xFF])
            elif isinstance(pid, list):
                # Multiple PIDs
                command = [self.sid_read_data_by_identifier]
                for p in pid:
                    command.extend([(p >> 8) & 0xFF, p & 0xFF])
                response = self.send_command(command)
            else:
                raise ValueError("Invalid PID type")
            
            # Check if response is positive
            if response and len(response) >= 3 and response[0] == self.sid_read_data_by_identifier + 0x40:
                # Extract data
                return response[3:]
            
            return None
        except Exception as e:
            logger.error(f"Error reading data: {e}")
            return None
    
    def write_data(self, pid, data):
        """
        Write data to the vehicle
        
        Args:
            pid: The parameter ID to write
            data: The data to write
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Send write data command
            command = [self.sid_write_data_by_identifier, (pid >> 8) & 0xFF, pid & 0xFF]
            command.extend(data)
            response = self.send_command(command)
            
            # Check if response is positive
            if response and len(response) >= 3 and response[0] == self.sid_write_data_by_identifier + 0x40:
                return True
            
            return False
        except Exception as e:
            logger.error(f"Error writing data: {e}")
            return False
    
    def get_protocol_info(self):
        """
        Get information about the protocol
        
        Returns:
            dict: Protocol information
        """
        return {
            'name': 'ISO15765-4 (CAN)',
            'type': ProtocolType.STANDARD,
            'baudrate': self.baudrate,
            'frame_type': self.frame_type,
            'tx_id': self.tx_id,
            'rx_id': self.rx_id
        }
    
    def request_security_access(self, level):
        """
        Request security access
        
        Args:
            level (SecurityLevel): The security level to request
            
        Returns:
            bool: True if access granted, False otherwise
        """
        try:
            # Convert security level to access level
            access_level = level.value * 2 - 1
            
            # Send request seed command
            response = self.send_command([self.sid_security_access, access_level])
            
            # Check if response is positive
            if response and len(response) >= 2 and response[0] == self.sid_security_access + 0x40:
                # Extract seed
                seed = response[2:]
                
                # Calculate key
                key = self.calculate_key(seed, level)
                
                # Send key
                response = self.send_command([self.sid_security_access, access_level + 1, *key])
                
                # Check if response is positive
                if response and len(response) >= 2 and response[0] == self.sid_security_access + 0x40:
                    self.security_level = level
                    self.security_status = SecurityAccessStatus.ACCESS_GRANTED
                    return True
                else:
                    self.security_status = SecurityAccessStatus.ACCESS_DENIED
                    return False
            else:
                self.security_status = SecurityAccessStatus.ACCESS_DENIED
                return False
        except Exception as e:
            logger.error(f"Error requesting security access: {e}")
            self.security_status = SecurityAccessStatus.ACCESS_DENIED
            return False
    
    def calculate_key(self, seed, level):
        """
        Calculate the key from the seed
        
        Args:
            seed: The seed received from the ECU
            level (SecurityLevel): The security level
            
        Returns:
            The calculated key
        """
        # Default implementation (should be overridden by subclasses)
        # XOR each byte with 0xFF
        return [b ^ 0xFF for b in seed]
    
    def enter_diagnostic_session(self, session_type):
        """
        Enter a diagnostic session
        
        Args:
            session_type: The session type to enter
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Send diagnostic session control command
            response = self.send_command([self.sid_diagnostic_session_control, session_type])
            
            # Check if response is positive
            if response and len(response) >= 2 and response[0] == self.sid_diagnostic_session_control + 0x40:
                self.session_type = session_type
                return True
            
            return False
        except Exception as e:
            logger.error(f"Error entering diagnostic session: {e}")
            return False
    
    def exit_diagnostic_session(self):
        """
        Exit the current diagnostic session
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Enter default session
            if self.enter_diagnostic_session(self.session_default):
                self.session_type = None
                return True
            
            return False
        except Exception as e:
            logger.error(f"Error exiting diagnostic session: {e}")
            return False
    
    def reset_ecu(self, reset_type="soft"):
        """
        Reset the ECU
        
        Args:
            reset_type (str): The type of reset to perform
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Map reset type to reset code
            reset_codes = {
                "hard": self.reset_hard,
                "key_off_on": self.reset_key_off_on,
                "soft": self.reset_soft,
                "enable_rapid_power_shutdown": self.reset_enable_rapid_power_shutdown,
                "disable_rapid_power_shutdown": self.reset_disable_rapid_power_shutdown
            }
            
            reset_code = reset_codes.get(reset_type, self.reset_soft)
            
            # Send ECU reset command
            response = self.send_command([self.sid_ecu_reset, reset_code])
            
            # Check if response is positive
            if response and len(response) >= 2 and response[0] == self.sid_ecu_reset + 0x40:
                return True
            
            return False
        except Exception as e:
            logger.error(f"Error resetting ECU: {e}")
            return False
    
    def get_vin(self):
        """
        Get the vehicle identification number
        
        Returns:
            str: The VIN
        """
        try:
            # VIN is stored in PID 0xF190
            response = self.read_data(0xF190)
            
            if response and len(response) >= 17:
                # Convert bytes to ASCII
                vin = ''.join(chr(b) for b in response[:17])
                return vin
            
            return None
        except Exception as e:
            logger.error(f"Error getting VIN: {e}")
            return None
