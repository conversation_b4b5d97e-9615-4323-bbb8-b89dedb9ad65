#!/usr/bin/env python3
"""
Query DTC Database
This script queries the DTC database to verify the imported data.
"""

import os
import sys
from database.dtc_db import DTCDatabase

def main():
    """Main function"""
    print("Querying DTC database...")

    # Initialize database
    db = DTCDatabase()

    # Get all categories
    categories = db.get_all_categories()
    print(f"\nAvailable Categories ({len(categories)}):")
    for category in categories:
        print(f"  {category['code_prefix']} - {category['name']}")

    # Get all manufacturers
    manufacturers = db.get_all_manufacturers()
    print(f"\nAvailable Manufacturers ({len(manufacturers)}):")
    for manufacturer in manufacturers:
        print(f"  {manufacturer['name']} (Prefix: {manufacturer['code_prefix']})")

    # Get all systems
    systems = db.get_all_systems()
    print(f"\nAvailable Systems ({len(systems)}):")
    for system in systems:
        if not system['parent_id']:
            print(f"  {system['name']}")
            # Find subsystems
            subsystems = [s for s in systems if s['parent_id'] == system['id']]
            for subsystem in subsystems:
                print(f"    - {subsystem['name']}")

    # Query some sample codes
    sample_codes = ["P0100", "P0101", "P0102", "P1083", "P1084"]
    print("\nSample DTC Codes:")

    for code in sample_codes:
        dtc_info = db.get_dtc_info(code)
        if dtc_info:
            print(f"\n  {code} - {dtc_info['description']}")
            print(f"    Category: {dtc_info['category']}")
            print(f"    Severity: {dtc_info['severity']}")
            print(f"    Possible Causes: {dtc_info['possible_causes']}")
            print(f"    Solutions: {dtc_info['solutions']}")

            # Enhanced information
            if dtc_info['symptoms']:
                print(f"    Symptoms: {dtc_info['symptoms']}")
            if dtc_info['component_location']:
                print(f"    Component Location: {dtc_info['component_location']}")
            if dtc_info['repair_difficulty']:
                print(f"    Repair Difficulty: {dtc_info['repair_difficulty']}")
            if dtc_info['repair_cost']:
                print(f"    Repair Cost: {dtc_info['repair_cost']}")
            if dtc_info['repair_time']:
                print(f"    Repair Time: {dtc_info['repair_time']}")

            # Manufacturer-specific information
            if dtc_info['manufacturer']:
                print(f"    Manufacturer: {dtc_info['manufacturer']}")
            if dtc_info['system']:
                print(f"    System: {dtc_info['system']}")
            if dtc_info['subsystem']:
                print(f"    Subsystem: {dtc_info['subsystem']}")
            if dtc_info['applicable_models']:
                print(f"    Applicable Models: {dtc_info['applicable_models']}")
        else:
            print(f"\n  {code} - Not found")

    # Search for codes
    search_terms = ["air flow", "fuel", "BMW"]
    print("\nSearch Results:")

    for term in search_terms:
        print(f"\n  Search for '{term}':")
        results = db.search_dtc_codes(term)

        if results:
            print(f"    Found {len(results)} codes:")
            for i, result in enumerate(results[:5]):  # Show only first 5 results
                print(f"    {i+1}. {result['code']} - {result['description']}")

            if len(results) > 5:
                print(f"    ... and {len(results) - 5} more")
        else:
            print("    No results found")

    # Search with filters
    print("\nFiltered Search Results:")

    # Search for BMW codes
    bmw_results = db.search_dtc_codes("", manufacturer="BMW")
    if bmw_results:
        print(f"\n  BMW-specific codes ({len(bmw_results)}):")
        for i, result in enumerate(bmw_results[:5]):  # Show only first 5 results
            print(f"    {i+1}. {result['code']} - {result['description']}")

        if len(bmw_results) > 5:
            print(f"    ... and {len(bmw_results) - 5} more")

    # Search for Engine system codes
    engine_results = db.search_dtc_codes("", system="Engine")
    if engine_results:
        print(f"\n  Engine system codes ({len(engine_results)}):")
        for i, result in enumerate(engine_results[:5]):  # Show only first 5 results
            print(f"    {i+1}. {result['code']} - {result['description']}")

        if len(engine_results) > 5:
            print(f"    ... and {len(engine_results) - 5} more")

    # Search for high severity codes
    severity_results = db.search_dtc_codes("", severity=4)
    if severity_results:
        print(f"\n  High severity codes ({len(severity_results)}):")
        for i, result in enumerate(severity_results[:5]):  # Show only first 5 results
            print(f"    {i+1}. {result['code']} - {result['description']} (Severity: {result['severity']})")

        if len(severity_results) > 5:
            print(f"    ... and {len(severity_results) - 5} more")

    print("\nQuery complete!")

if __name__ == "__main__":
    main()
