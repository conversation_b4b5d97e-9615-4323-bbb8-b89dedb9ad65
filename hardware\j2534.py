#!/usr/bin/env python3
"""
J2534 Interface
This module provides an interface for J2534 PassThru devices.
"""

import logging
import time
import ctypes
import platform
import os
from enum import Enum, auto
from typing import Dict, List, Optional, Any, Tuple

logger = logging.getLogger("hardware.j2534")

# J2534 API constants
class J2534Protocol(Enum):
    """J2534 protocols"""
    J1850VPW = 1
    J1850PWM = 2
    ISO9141 = 3
    ISO14230 = 4
    CAN = 5
    ISO15765 = 6
    SCI_A_ENGINE = 7
    SCI_A_TRANS = 8
    SCI_B_ENGINE = 9
    SCI_B_TRANS = 10

class J2534Error(Enum):
    """J2534 error codes"""
    STATUS_NOERROR = 0x00
    ERR_NOT_SUPPORTED = 0x01
    ERR_INVALID_CHANNEL_ID = 0x02
    ERR_INVALID_PROTOCOL_ID = 0x03
    ERR_NULL_PARAMETER = 0x04
    ERR_INVALID_IOCTL_VALUE = 0x05
    ERR_INVALID_FLAGS = 0x06
    ERR_FAILED = 0x07
    ERR_DEVICE_NOT_CONNECTED = 0x08
    ERR_TIMEOUT = 0x09
    ERR_INVALID_MSG = 0x0A
    ERR_INVALID_TIME_INTERVAL = 0x0B
    ERR_EXCEEDED_LIMIT = 0x0C
    ERR_INVALID_MSG_ID = 0x0D
    ERR_DEVICE_IN_USE = 0x0E
    ERR_INVALID_IOCTL_ID = 0x0F
    ERR_BUFFER_EMPTY = 0x10
    ERR_BUFFER_FULL = 0x11
    ERR_BUFFER_OVERFLOW = 0x12
    ERR_PIN_INVALID = 0x13
    ERR_CHANNEL_IN_USE = 0x14
    ERR_MSG_PROTOCOL_ID = 0x15
    ERR_INVALID_FILTER_ID = 0x16
    ERR_NO_FLOW_CONTROL = 0x17
    ERR_NOT_UNIQUE = 0x18
    ERR_INVALID_BAUDRATE = 0x19
    ERR_INVALID_DEVICE_ID = 0x1A

class J2534IOCTL(Enum):
    """J2534 IOCTL commands"""
    GET_CONFIG = 0x01
    SET_CONFIG = 0x02
    READ_VBATT = 0x03
    FIVE_BAUD_INIT = 0x04
    FAST_INIT = 0x05
    CLEAR_TX_BUFFER = 0x07
    CLEAR_RX_BUFFER = 0x08
    CLEAR_PERIODIC_MSGS = 0x09
    CLEAR_MSG_FILTERS = 0x0A
    CLEAR_FUNCT_MSG_LOOKUP_TABLE = 0x0B
    ADD_TO_FUNCT_MSG_LOOKUP_TABLE = 0x0C
    DELETE_FROM_FUNCT_MSG_LOOKUP_TABLE = 0x0D
    READ_PROG_VOLTAGE = 0x0E

class J2534Config(Enum):
    """J2534 configuration parameters"""
    DATA_RATE = 0x01
    LOOPBACK = 0x03
    NODE_ADDRESS = 0x04
    NETWORK_LINE = 0x05
    P1_MIN = 0x06
    P1_MAX = 0x07
    P2_MIN = 0x08
    P2_MAX = 0x09
    P3_MIN = 0x0A
    P3_MAX = 0x0B
    P4_MIN = 0x0C
    P4_MAX = 0x0D
    W1 = 0x0E
    W2 = 0x0F
    W3 = 0x10
    W4 = 0x11
    W5 = 0x12
    TIDLE = 0x13
    TINIL = 0x14
    TWUP = 0x15
    PARITY = 0x16
    BIT_SAMPLE_POINT = 0x17
    SYNC_JUMP_WIDTH = 0x18
    W0 = 0x19
    T1_MAX = 0x1A
    T2_MAX = 0x1B
    T3_MAX = 0x1C
    T4_MAX = 0x1D
    T5_MAX = 0x1E
    ISO15765_BS = 0x1F
    ISO15765_STMIN = 0x20
    DATA_BITS = 0x21
    FIVE_BAUD_MOD = 0x22
    BS_TX = 0x23
    STMIN_TX = 0x24
    T3_CMSGM = 0x25
    ISO15765_WFT_MAX = 0x26

class J2534Interface:
    """J2534 interface for PassThru devices"""
    
    def __init__(self, port=None, library_path=None):
        """
        Initialize the J2534 interface
        
        Args:
            port (str): The port to use (optional)
            library_path (str): Path to the J2534 library (optional)
        """
        self.port = port
        self.library_path = library_path
        self.library = None
        self.device_id = None
        self.channel_id = None
        self.connected = False
        self.protocol = None
        self.version = "Unknown"
        
        # Find the library if not specified
        if not self.library_path:
            self.library_path = self._find_library()
    
    def _find_library(self):
        """
        Find the J2534 library
        
        Returns:
            str: Path to the library or None if not found
        """
        # Check platform
        if platform.system() == "Windows":
            # Common locations for J2534 DLLs on Windows
            common_paths = [
                "C:\\Program Files\\OpenECU\\OpenPort 2.0\\drivers\\openport2.dll",
                "C:\\Program Files\\Drew Technologies\\J2534\\MongooseJ2534.dll",
                "C:\\Program Files\\Tactrix\\OpenPort 2.0\\drivers\\op20pt32.dll",
                "C:\\Program Files\\J2534\\passthru.dll"
            ]
            
            for path in common_paths:
                if os.path.exists(path):
                    return path
        elif platform.system() == "Linux":
            # Common locations for J2534 libraries on Linux
            common_paths = [
                "/usr/lib/libj2534.so",
                "/usr/local/lib/libj2534.so"
            ]
            
            for path in common_paths:
                if os.path.exists(path):
                    return path
        
        logger.warning("J2534 library not found")
        return None
    
    def _load_library(self):
        """
        Load the J2534 library
        
        Returns:
            bool: True if loaded, False otherwise
        """
        if not self.library_path:
            logger.error("J2534 library path not specified")
            return False
        
        try:
            self.library = ctypes.cdll.LoadLibrary(self.library_path)
            logger.info(f"Loaded J2534 library: {self.library_path}")
            return True
        except Exception as e:
            logger.error(f"Error loading J2534 library: {e}")
            return False
    
    def connect(self):
        """
        Connect to the J2534 device
        
        Returns:
            bool: True if connected, False otherwise
        """
        if self.connected:
            logger.info("Already connected to J2534 device")
            return True
        
        try:
            # Load the library if not already loaded
            if not self.library and not self._load_library():
                return False
            
            # Open the device
            device_id = ctypes.c_ulong()
            result = self.library.PassThruOpen(ctypes.c_void_p(0), ctypes.byref(device_id))
            
            if result != J2534Error.STATUS_NOERROR.value:
                logger.error(f"Error opening J2534 device: {result}")
                return False
            
            self.device_id = device_id.value
            logger.info(f"Opened J2534 device: {self.device_id}")
            
            # Get version information
            self._get_version()
            
            self.connected = True
            return True
        except Exception as e:
            logger.error(f"Error connecting to J2534 device: {e}")
            return False
    
    def disconnect(self):
        """
        Disconnect from the J2534 device
        
        Returns:
            bool: True if disconnected, False otherwise
        """
        if not self.connected:
            logger.info("Not connected to J2534 device")
            return True
        
        try:
            # Close any open channels
            if self.channel_id is not None:
                self.library.PassThruDisconnect(self.channel_id)
                self.channel_id = None
            
            # Close the device
            if self.device_id is not None:
                result = self.library.PassThruClose(self.device_id)
                
                if result != J2534Error.STATUS_NOERROR.value:
                    logger.error(f"Error closing J2534 device: {result}")
                    return False
                
                self.device_id = None
            
            self.connected = False
            logger.info("Disconnected from J2534 device")
            return True
        except Exception as e:
            logger.error(f"Error disconnecting from J2534 device: {e}")
            return False
    
    def _get_version(self):
        """
        Get the J2534 device version
        
        Returns:
            str: The device version
        """
        if not self.connected or not self.library:
            return "Unknown"
        
        try:
            # Allocate buffer for version string
            version_buffer = ctypes.create_string_buffer(80)
            
            # Get version
            result = self.library.PassThruReadVersion(
                self.device_id,
                ctypes.byref(version_buffer),
                ctypes.byref(version_buffer),
                ctypes.byref(version_buffer)
            )
            
            if result != J2534Error.STATUS_NOERROR.value:
                logger.error(f"Error reading J2534 version: {result}")
                return "Unknown"
            
            # Convert to string
            self.version = version_buffer.value.decode('utf-8', errors='ignore')
            return self.version
        except Exception as e:
            logger.error(f"Error getting J2534 version: {e}")
            return "Unknown"
    
    def connect_channel(self, protocol=J2534Protocol.ISO15765, baudrate=500000):
        """
        Connect a channel
        
        Args:
            protocol (J2534Protocol): The protocol to use
            baudrate (int): The baudrate to use
            
        Returns:
            bool: True if connected, False otherwise
        """
        if not self.connected:
            logger.error("Not connected to J2534 device")
            return False
        
        try:
            # Close any open channels
            if self.channel_id is not None:
                self.library.PassThruDisconnect(self.channel_id)
                self.channel_id = None
            
            # Connect channel
            channel_id = ctypes.c_ulong()
            flags = ctypes.c_ulong(0)
            result = self.library.PassThruConnect(
                self.device_id,
                protocol.value,
                flags,
                baudrate,
                ctypes.byref(channel_id)
            )
            
            if result != J2534Error.STATUS_NOERROR.value:
                logger.error(f"Error connecting J2534 channel: {result}")
                return False
            
            self.channel_id = channel_id.value
            self.protocol = protocol
            logger.info(f"Connected J2534 channel: {self.channel_id} (protocol: {protocol.name})")
            return True
        except Exception as e:
            logger.error(f"Error connecting J2534 channel: {e}")
            return False
    
    def disconnect_channel(self):
        """
        Disconnect the channel
        
        Returns:
            bool: True if disconnected, False otherwise
        """
        if not self.connected:
            logger.error("Not connected to J2534 device")
            return False
        
        try:
            # Close the channel
            if self.channel_id is not None:
                result = self.library.PassThruDisconnect(self.channel_id)
                
                if result != J2534Error.STATUS_NOERROR.value:
                    logger.error(f"Error disconnecting J2534 channel: {result}")
                    return False
                
                self.channel_id = None
                self.protocol = None
                logger.info("Disconnected J2534 channel")
            
            return True
        except Exception as e:
            logger.error(f"Error disconnecting J2534 channel: {e}")
            return False
    
    def send_message(self, data, timeout=1000):
        """
        Send a message
        
        Args:
            data (bytes): The data to send
            timeout (int): The timeout in milliseconds
            
        Returns:
            bool: True if sent, False otherwise
        """
        if not self.connected or self.channel_id is None:
            logger.error("Not connected to J2534 channel")
            return False
        
        try:
            # Create message structure
            class J2534Msg(ctypes.Structure):
                _fields_ = [
                    ("protocol_id", ctypes.c_ulong),
                    ("rx_status", ctypes.c_ulong),
                    ("tx_flags", ctypes.c_ulong),
                    ("timestamp", ctypes.c_ulong),
                    ("data_size", ctypes.c_ulong),
                    ("extra_data_index", ctypes.c_ulong),
                    ("data", ctypes.c_ubyte * 4128)
                ]
            
            # Create message
            msg = J2534Msg()
            msg.protocol_id = self.protocol.value
            msg.rx_status = 0
            msg.tx_flags = 0
            msg.timestamp = 0
            msg.data_size = len(data)
            msg.extra_data_index = 0
            
            # Copy data
            for i, b in enumerate(data):
                msg.data[i] = b
            
            # Send message
            num_msgs = ctypes.c_ulong(1)
            result = self.library.PassThruWriteMsgs(
                self.channel_id,
                ctypes.byref(msg),
                ctypes.byref(num_msgs),
                timeout
            )
            
            if result != J2534Error.STATUS_NOERROR.value:
                logger.error(f"Error sending J2534 message: {result}")
                return False
            
            return True
        except Exception as e:
            logger.error(f"Error sending J2534 message: {e}")
            return False
    
    def receive_message(self, timeout=1000):
        """
        Receive a message
        
        Args:
            timeout (int): The timeout in milliseconds
            
        Returns:
            bytes: The received data or None if no data
        """
        if not self.connected or self.channel_id is None:
            logger.error("Not connected to J2534 channel")
            return None
        
        try:
            # Create message structure
            class J2534Msg(ctypes.Structure):
                _fields_ = [
                    ("protocol_id", ctypes.c_ulong),
                    ("rx_status", ctypes.c_ulong),
                    ("tx_flags", ctypes.c_ulong),
                    ("timestamp", ctypes.c_ulong),
                    ("data_size", ctypes.c_ulong),
                    ("extra_data_index", ctypes.c_ulong),
                    ("data", ctypes.c_ubyte * 4128)
                ]
            
            # Create message
            msg = J2534Msg()
            
            # Receive message
            num_msgs = ctypes.c_ulong(1)
            result = self.library.PassThruReadMsgs(
                self.channel_id,
                ctypes.byref(msg),
                ctypes.byref(num_msgs),
                timeout
            )
            
            if result != J2534Error.STATUS_NOERROR.value:
                if result != J2534Error.ERR_BUFFER_EMPTY.value:
                    logger.error(f"Error receiving J2534 message: {result}")
                return None
            
            # Check if any messages received
            if num_msgs.value == 0:
                return None
            
            # Extract data
            data = bytes(msg.data[:msg.data_size])
            return data
        except Exception as e:
            logger.error(f"Error receiving J2534 message: {e}")
            return None
    
    def get_supported_protocols(self):
        """
        Get supported protocols
        
        Returns:
            list: List of supported protocols
        """
        if not self.connected:
            logger.error("Not connected to J2534 device")
            return []
        
        # This is not directly supported by the J2534 API
        # We'll return a list of common protocols
        return [
            "J1850VPW",
            "J1850PWM",
            "ISO9141",
            "ISO14230",
            "CAN",
            "ISO15765"
        ]
    
    def update_firmware(self, firmware_file):
        """
        Update firmware
        
        Args:
            firmware_file (str): Path to the firmware file
            
        Returns:
            bool: True if updated, False otherwise
        """
        logger.error("Firmware update not supported for J2534 devices")
        return False
