"""
Protocol Handler Module
Manages different OBD and manufacturer-specific protocols
"""

from abc import ABC, abstractmethod
from enum import Enum, auto
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("protocol.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("protocol")

class ProtocolType(Enum):
    """Protocol types"""
    STANDARD = auto()  # Standard OBD-II protocols
    MANUFACTURER = auto()  # Manufacturer-specific protocols

class SecurityLevel(Enum):
    """Security access levels"""
    NONE = 0  # No security
    LEVEL_1 = 1  # Basic security (e.g., read-only)
    LEVEL_2 = 2  # Medium security (e.g., read/write non-critical)
    LEVEL_3 = 3  # High security (e.g., read/write critical)
    LEVEL_4 = 4  # Programming security (e.g., ECU programming)
    LEVEL_5 = 5  # Factory security (e.g., factory reset, VIN write)

class SecurityAccessStatus(Enum):
    """Security access status"""
    NOT_REQUESTED = auto()  # Security access not requested
    SEED_REQUESTED = auto()  # Seed requested
    ACCESS_GRANTED = auto()  # Access granted
    ACCESS_DENIED = auto()  # Access denied
    LOCKED = auto()  # Security access locked (too many attempts)

class ProtocolError(Exception):
    """Base class for protocol exceptions"""
    pass

class CommunicationError(ProtocolError):
    """Communication error"""
    pass

class SecurityAccessError(ProtocolError):
    """Security access error"""
    pass

class BaseProtocol(ABC):
    """Base class for all protocols"""

    def __init__(self, interface=None):
        """
        Initialize the protocol

        Args:
            interface: The hardware interface to use
        """
        self.interface = interface
        self.connected = False
        self.security_level = SecurityLevel.NONE
        self.security_status = SecurityAccessStatus.NOT_REQUESTED
        self.session_type = None
        self.timeout = 5  # Default timeout in seconds
        self.max_retries = 3  # Default maximum retries
        self.logger = logging.getLogger(f"protocol.{self.__class__.__name__}")

    @abstractmethod
    def connect(self):
        """
        Connect to the vehicle

        Returns:
            bool: True if connected, False otherwise
        """
        pass

    @abstractmethod
    def disconnect(self):
        """
        Disconnect from the vehicle

        Returns:
            bool: True if disconnected, False otherwise
        """
        pass

    @abstractmethod
    def send_command(self, command, response_required=True):
        """
        Send a command to the vehicle

        Args:
            command: The command to send
            response_required (bool): Whether a response is required

        Returns:
            The response from the vehicle
        """
        pass

    @abstractmethod
    def read_dtc(self):
        """
        Read diagnostic trouble codes

        Returns:
            list: List of DTCs
        """
        pass

    @abstractmethod
    def clear_dtc(self):
        """
        Clear diagnostic trouble codes

        Returns:
            bool: True if successful, False otherwise
        """
        pass

    @abstractmethod
    def read_data(self, pid):
        """
        Read data from the vehicle

        Args:
            pid: The parameter ID to read

        Returns:
            The data read from the vehicle
        """
        pass

    @abstractmethod
    def write_data(self, pid, data):
        """
        Write data to the vehicle

        Args:
            pid: The parameter ID to write
            data: The data to write

        Returns:
            bool: True if successful, False otherwise
        """
        pass

    @abstractmethod
    def get_protocol_info(self):
        """
        Get information about the protocol

        Returns:
            dict: Protocol information
        """
        pass

    @abstractmethod
    def request_security_access(self, level):
        """
        Request security access

        Args:
            level (SecurityLevel): The security level to request

        Returns:
            bool: True if access granted, False otherwise
        """
        pass

    def calculate_key(self, seed, level):
        """
        Calculate the key from the seed

        Args:
            seed: The seed received from the ECU
            level (SecurityLevel): The security level

        Returns:
            The calculated key
        """
        # Default implementation (should be overridden by subclasses)
        return seed ^ 0xFF

    def test_connection(self, connection):
        """
        Test if the connection uses this protocol

        Args:
            connection: The connection to test

        Returns:
            bool: True if the connection uses this protocol, False otherwise
        """
        # Default implementation (should be overridden by subclasses)
        return False

    def check_supported_pids(self):
        """
        Check which PIDs are supported by the vehicle

        Returns:
            list: List of supported PIDs
        """
        # Default implementation (should be overridden by subclasses)
        return []

    def enter_diagnostic_session(self, session_type):
        """
        Enter a diagnostic session

        Args:
            session_type: The session type to enter

        Returns:
            bool: True if successful, False otherwise
        """
        # Default implementation (should be overridden by subclasses)
        self.session_type = session_type
        return True

    def exit_diagnostic_session(self):
        """
        Exit the current diagnostic session

        Returns:
            bool: True if successful, False otherwise
        """
        # Default implementation (should be overridden by subclasses)
        self.session_type = None
        return True

    def reset_ecu(self, reset_type="soft"):
        """
        Reset the ECU

        Args:
            reset_type (str): The type of reset to perform

        Returns:
            bool: True if successful, False otherwise
        """
        # Default implementation (should be overridden by subclasses)
        return False

    def get_vin(self):
        """
        Get the vehicle identification number

        Returns:
            str: The VIN
        """
        # Default implementation (should be overridden by subclasses)
        return None

    def get_ecu_info(self):
        """
        Get information about the ECU

        Returns:
            dict: ECU information
        """
        # Default implementation (should be overridden by subclasses)
        return {}

    def read_memory(self, address, size):
        """
        Read memory from the ECU

        Args:
            address: The memory address to read
            size: The number of bytes to read

        Returns:
            bytes: The memory data
        """
        # Default implementation (should be overridden by subclasses)
        return b''

    def write_memory(self, address, data):
        """
        Write memory to the ECU

        Args:
            address: The memory address to write
            data: The data to write

        Returns:
            bool: True if successful, False otherwise
        """
        # Default implementation (should be overridden by subclasses)
        return False

    def program_ecu(self, data, verify=True):
        """
        Program the ECU

        Args:
            data: The data to program
            verify (bool): Whether to verify the programming

        Returns:
            bool: True if successful, False otherwise
        """
        # Default implementation (should be overridden by subclasses)
        return False

    def verify_programming(self, data):
        """
        Verify ECU programming

        Args:
            data: The data to verify against

        Returns:
            bool: True if verification successful, False otherwise
        """
        # Default implementation (should be overridden by subclasses)
        return False

    def calculate_checksum(self, data):
        """
        Calculate checksum for data

        Args:
            data: The data to calculate checksum for

        Returns:
            The calculated checksum
        """
        # Default implementation (should be overridden by subclasses)
        return sum(data) & 0xFF

# Import protocol implementations
try:
    from protocols.standard.iso9141 import ISO9141Protocol
    from protocols.standard.iso14230 import ISO14230Protocol
    from protocols.standard.iso15765 import ISO15765Protocol
    from protocols.standard.j1850pwm import SAEJ1850PWMProtocol
    from protocols.standard.j1850vpw import SAEJ1850VPWProtocol

    from protocols.manufacturer.toyota import ToyotaProtocol, ToyotaVariant
    from protocols.manufacturer.honda import HondaProtocol, HondaVariant
    from protocols.manufacturer.ford import FordProtocol, FordVariant
    from protocols.manufacturer.gm import GMProtocol, GMVariant
    from protocols.manufacturer.bmw import BMWProtocol, BMWVariant
    from protocols.manufacturer.volkswagen import VolkswagenProtocol, VolkswagenVariant
    from protocols.manufacturer.hyundai import HyundaiProtocol, HyundaiVariant
    from protocols.manufacturer.mazda import MazdaProtocol, MazdaVariant
    from protocols.manufacturer.subaru import SubaruProtocol, SubaruVariant
except ImportError:
    logger.warning("Some protocol modules could not be imported. This is expected during initial setup.")

class ProtocolHandler:
    """Handles different OBD and manufacturer-specific protocols"""

    def __init__(self, interface=None):
        """
        Initialize the protocol handler

        Args:
            interface: The hardware interface to use
        """
        self.interface = interface
        self.logger = logging.getLogger("protocol.ProtocolHandler")

        # Standard OBD protocols
        self.standard_protocols = {}

        # Manufacturer-specific protocols
        self.manufacturer_protocols = {}

        # Manufacturer protocol mapping
        self.manufacturer_protocol_map = {
            # Toyota/Lexus
            'Toyota': ['Toyota ISO-TP', 'Toyota KWP', 'Toyota UDS'],
            'Lexus': ['Toyota ISO-TP', 'Toyota KWP', 'Toyota UDS'],

            # Honda/Acura
            'Honda': ['Honda Diag-H', 'Honda KWP', 'Honda UDS'],
            'Acura': ['Honda Diag-H', 'Honda KWP', 'Honda UDS'],

            # Ford/Lincoln
            'Ford': ['Ford UDS', 'Ford SCP', 'Ford UBP'],
            'Lincoln': ['Ford UDS', 'Ford SCP', 'Ford UBP'],

            # GM brands
            'GM': ['GM ALDL', 'GM Class 2', 'GM Global A', 'GM UDS'],
            'Chevrolet': ['GM ALDL', 'GM Class 2', 'GM Global A', 'GM UDS'],
            'Buick': ['GM ALDL', 'GM Class 2', 'GM Global A', 'GM UDS'],
            'Cadillac': ['GM ALDL', 'GM Class 2', 'GM Global A', 'GM UDS'],
            'GMC': ['GM ALDL', 'GM Class 2', 'GM Global A', 'GM UDS'],

            # BMW/Mini
            'BMW': ['BMW DS2', 'BMW KWP', 'BMW UDS'],
            'Mini': ['BMW DS2', 'BMW KWP', 'BMW UDS'],

            # Volkswagen Group
            'VW': ['VW KWP1281', 'VW KWP2000', 'VW UDS'],
            'Volkswagen': ['VW KWP1281', 'VW KWP2000', 'VW UDS'],
            'Audi': ['VW KWP2000', 'VW UDS'],
            'Porsche': ['VW KWP2000', 'VW UDS'],

            # Hyundai/Kia
            'Hyundai': ['Hyundai KWP', 'Hyundai CAN-TP', 'Hyundai UDS'],
            'Kia': ['Kia KWP', 'Kia CAN-TP', 'Kia UDS'],
            'Genesis': ['Hyundai UDS'],

            # Mazda
            'Mazda': ['Mazda KWP', 'Mazda CAN-TP', 'Mazda UDS'],

            # Subaru
            'Subaru': ['Subaru SSM1', 'Subaru SSM2', 'Subaru SSM3', 'Subaru SSM4'],

            # Nissan/Infiniti
            'Nissan': ['Toyota ISO-TP', 'Toyota UDS'],  # Many Nissan vehicles use Toyota-compatible protocols
            'Infiniti': ['Toyota ISO-TP', 'Toyota UDS'],

            # Mitsubishi
            'Mitsubishi': ['Toyota ISO-TP', 'Toyota UDS'],  # Many Mitsubishi vehicles use Toyota-compatible protocols

            # Suzuki
            'Suzuki': ['Toyota ISO-TP', 'Toyota UDS'],  # Many Suzuki vehicles use Toyota-compatible protocols

            # European manufacturers
            'Mercedes': ['Toyota UDS'],  # Modern Mercedes use UDS over CAN
            'Mercedes-Benz': ['Toyota UDS'],
            'Volvo': ['Toyota UDS'],  # Modern Volvo use UDS over CAN
            'Jaguar': ['Toyota UDS'],  # Modern Jaguar use UDS over CAN
            'Land Rover': ['Toyota UDS'],  # Modern Land Rover use UDS over CAN

            # FCA Group
            'Chrysler': ['Toyota UDS'],  # Modern Chrysler use UDS over CAN
            'Dodge': ['Toyota UDS'],
            'Jeep': ['Toyota UDS'],
            'RAM': ['Toyota UDS'],
            'Fiat': ['Toyota UDS'],
            'Alfa Romeo': ['Toyota UDS'],

            # Other manufacturers
            'Tesla': ['Toyota UDS']  # Tesla uses a variant of UDS over CAN
        }

        # Initialize protocols
        self._initialize_protocols()

    def _initialize_protocols(self):
        """Initialize protocol objects"""
        try:
            # Standard OBD protocols
            self.standard_protocols = {
                'ISO9141-2': ISO9141Protocol(self.interface),
                'ISO14230-4 (KWP2000)': ISO14230Protocol(self.interface),
                'ISO15765-4 (CAN)': ISO15765Protocol(self.interface),
                'SAE J1850 PWM': SAEJ1850PWMProtocol(self.interface),
                'SAE J1850 VPW': SAEJ1850VPWProtocol(self.interface)
            }

            # Manufacturer-specific protocols
            self.manufacturer_protocols = {
                # Toyota protocols
                'Toyota ISO-TP': ToyotaProtocol(self.interface, variant=ToyotaVariant.TOBD),
                'Toyota KWP': ToyotaProtocol(self.interface, variant=ToyotaVariant.KWP),
                'Toyota UDS': ToyotaProtocol(self.interface, variant=ToyotaVariant.UDS),

                # Honda protocols
                'Honda Diag-H': HondaProtocol(self.interface, variant=HondaVariant.DIAG_H),
                'Honda KWP': HondaProtocol(self.interface, variant=HondaVariant.KWP),
                'Honda UDS': HondaProtocol(self.interface, variant=HondaVariant.UDS),

                # Ford protocols
                'Ford UDS': FordProtocol(self.interface, variant=FordVariant.UDS),
                'Ford SCP': FordProtocol(self.interface, variant=FordVariant.SCP),
                'Ford UBP': FordProtocol(self.interface, variant=FordVariant.UBP),

                # GM protocols
                'GM ALDL': GMProtocol(self.interface, variant=GMVariant.ALDL),
                'GM Class 2': GMProtocol(self.interface, variant=GMVariant.CLASS2),
                'GM Global A': GMProtocol(self.interface, variant=GMVariant.GLOBAL_A),
                'GM UDS': GMProtocol(self.interface, variant=GMVariant.UDS),

                # BMW protocols
                'BMW DS2': BMWProtocol(self.interface, variant=BMWVariant.DS2),
                'BMW KWP': BMWProtocol(self.interface, variant=BMWVariant.KWP),
                'BMW UDS': BMWProtocol(self.interface, variant=BMWVariant.UDS),

                # Volkswagen protocols
                'VW KWP1281': VolkswagenProtocol(self.interface, variant=VolkswagenVariant.KWP1281),
                'VW KWP2000': VolkswagenProtocol(self.interface, variant=VolkswagenVariant.KWP2000),
                'VW UDS': VolkswagenProtocol(self.interface, variant=VolkswagenVariant.UDS),

                # Hyundai/Kia protocols
                'Hyundai KWP': HyundaiProtocol(self.interface, variant=HyundaiVariant.KWP),
                'Hyundai CAN-TP': HyundaiProtocol(self.interface, variant=HyundaiVariant.CAN_TP),
                'Hyundai UDS': HyundaiProtocol(self.interface, variant=HyundaiVariant.UDS),
                'Kia KWP': HyundaiProtocol(self.interface, variant=HyundaiVariant.KWP),
                'Kia CAN-TP': HyundaiProtocol(self.interface, variant=HyundaiVariant.CAN_TP),
                'Kia UDS': HyundaiProtocol(self.interface, variant=HyundaiVariant.UDS),

                # Mazda protocols
                'Mazda KWP': MazdaProtocol(self.interface, variant=MazdaVariant.KWP),
                'Mazda CAN-TP': MazdaProtocol(self.interface, variant=MazdaVariant.CAN_TP),
                'Mazda UDS': MazdaProtocol(self.interface, variant=MazdaVariant.UDS),

                # Subaru protocols
                'Subaru SSM1': SubaruProtocol(self.interface, variant=SubaruVariant.SSM1),
                'Subaru SSM2': SubaruProtocol(self.interface, variant=SubaruVariant.SSM2),
                'Subaru SSM3': SubaruProtocol(self.interface, variant=SubaruVariant.SSM3),
                'Subaru SSM4': SubaruProtocol(self.interface, variant=SubaruVariant.SSM4)
            }
        except NameError:
            self.logger.warning("Protocol classes not available. This is expected during initial setup.")

    def get_standard_protocol(self, protocol_name):
        """
        Get a standard OBD protocol by name

        Args:
            protocol_name (str): The name of the protocol

        Returns:
            BaseProtocol: The protocol object or None if not found
        """
        return self.standard_protocols.get(protocol_name)

    def get_manufacturer_protocol(self, protocol_name):
        """
        Get a manufacturer-specific protocol by name

        Args:
            protocol_name (str): The name of the protocol

        Returns:
            BaseProtocol: The protocol object or None if not found
        """
        return self.manufacturer_protocols.get(protocol_name)

    def get_manufacturer_protocols(self, manufacturer):
        """
        Get protocols for a specific manufacturer

        Args:
            manufacturer (str): The manufacturer name

        Returns:
            list: List of protocol names or empty list if not found
        """
        return self.manufacturer_protocol_map.get(manufacturer, [])

    def get_all_standard_protocols(self):
        """
        Get a list of all standard OBD protocols

        Returns:
            list: List of protocol names
        """
        return list(self.standard_protocols.keys())

    def get_all_manufacturer_protocols(self):
        """
        Get a list of all manufacturer-specific protocols

        Returns:
            list: List of protocol names
        """
        return list(self.manufacturer_protocols.keys())

    def get_all_manufacturers(self):
        """
        Get a list of all manufacturers with specific protocols

        Returns:
            list: List of manufacturer names
        """
        return list(self.manufacturer_protocol_map.keys())

    def detect_protocol(self, connection=None):
        """
        Detect the protocol used by the vehicle

        Args:
            connection: The connection to the vehicle (uses self.interface if None)

        Returns:
            str: The detected protocol name or None if not detected
        """
        connection = connection or self.interface

        if not connection:
            self.logger.error("No connection provided for protocol detection")
            return None

        # Try each standard protocol
        for protocol_name, protocol in self.standard_protocols.items():
            try:
                if protocol.test_connection(connection):
                    self.logger.info(f"Detected standard protocol: {protocol_name}")
                    return protocol_name
            except Exception as e:
                self.logger.warning(f"Error testing protocol {protocol_name}: {e}")

        # Try each manufacturer protocol
        for protocol_name, protocol in self.manufacturer_protocols.items():
            try:
                if protocol.test_connection(connection):
                    self.logger.info(f"Detected manufacturer protocol: {protocol_name}")
                    return protocol_name
            except Exception as e:
                self.logger.warning(f"Error testing protocol {protocol_name}: {e}")

        self.logger.warning("No protocol detected")
        return None

    def get_protocol_for_vehicle(self, make, model, year):
        """
        Get the appropriate protocol for a specific vehicle

        Args:
            make (str): The vehicle make
            model (str): The vehicle model
            year (int): The vehicle year

        Returns:
            BaseProtocol: The protocol object or None if not found
        """
        # Get protocols for the manufacturer
        protocol_names = self.get_manufacturer_protocols(make)

        if not protocol_names:
            self.logger.warning(f"No protocols found for manufacturer: {make}")
            return None

        # Try to find the most appropriate protocol based on year
        if year >= 2008:
            # Most vehicles after 2008 use UDS
            for protocol_name in protocol_names:
                if "UDS" in protocol_name:
                    return self.get_manufacturer_protocol(protocol_name)
        elif year >= 2000:
            # Most vehicles between 2000-2008 use KWP2000
            for protocol_name in protocol_names:
                if "KWP" in protocol_name:
                    return self.get_manufacturer_protocol(protocol_name)

        # Fall back to the first protocol in the list
        return self.get_manufacturer_protocol(protocol_names[0])

    def create_connection(self, protocol_name, interface=None):
        """
        Create a connection using the specified protocol

        Args:
            protocol_name (str): The name of the protocol
            interface: The hardware interface to use (uses self.interface if None)

        Returns:
            BaseProtocol: The connected protocol object or None if connection failed
        """
        interface = interface or self.interface

        if not interface:
            self.logger.error("No interface provided for connection")
            return None

        # Get the protocol
        protocol = self.get_standard_protocol(protocol_name) or self.get_manufacturer_protocol(protocol_name)

        if not protocol:
            self.logger.error(f"Protocol not found: {protocol_name}")
            return None

        # Set the interface
        protocol.interface = interface

        # Connect
        try:
            if protocol.connect():
                self.logger.info(f"Connected using protocol: {protocol_name}")
                return protocol
            else:
                self.logger.error(f"Failed to connect using protocol: {protocol_name}")
                return None
        except Exception as e:
            self.logger.error(f"Error connecting using protocol {protocol_name}: {e}")
            return None
