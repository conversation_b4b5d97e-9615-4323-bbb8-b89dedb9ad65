#!/usr/bin/env python3
"""
Diagnostic Session Manager
This module provides a manager for diagnostic sessions.
"""

import logging
import time
import threading
from enum import Enum, auto
import json
import os

from protocols.protocol_manager import ProtocolManager, ConnectionMethod
from protocols.protocol_handler import SecurityLevel
from database.dtc_db import DTCDatabase

logger = logging.getLogger("diagnostic.session")

class SessionState(Enum):
    """Diagnostic session states"""
    DISCONNECTED = auto()  # Not connected to vehicle
    CONNECTING = auto()    # Connecting to vehicle
    CONNECTED = auto()     # Connected to vehicle
    ACTIVE = auto()        # Active diagnostic session
    PROGRAMMING = auto()   # Programming session
    ERROR = auto()         # Error state

class DiagnosticSession:
    """Diagnostic session"""
    
    def __init__(self, interface=None):
        """
        Initialize the diagnostic session
        
        Args:
            interface: The hardware interface to use
        """
        self.interface = interface
        self.protocol_manager = ProtocolManager(interface)
        self.dtc_database = DTCDatabase()
        self.state = SessionState.DISCONNECTED
        self.session_data = {}
        self.session_log = []
        self.keep_alive_thread = None
        self.keep_alive_interval = 2.0  # seconds
        self.keep_alive_running = False
        self.last_error = None
        self.security_level = SecurityLevel.NONE
    
    def connect(self, method=ConnectionMethod.AUTO, protocol_name=None, make=None, model=None, year=None):
        """
        Connect to the vehicle
        
        Args:
            method (ConnectionMethod): The connection method
            protocol_name (str): The protocol name (for manual connection)
            make (str): The vehicle make (for manual connection)
            model (str): The vehicle model (for manual connection)
            year (int): The vehicle year (for manual connection)
            
        Returns:
            bool: True if connected, False otherwise
        """
        try:
            self.state = SessionState.CONNECTING
            self.log_event("Connecting to vehicle")
            
            # Connect to the vehicle
            if self.protocol_manager.connect(method, protocol_name, make, model, year):
                self.state = SessionState.CONNECTED
                self.log_event("Connected to vehicle")
                
                # Get vehicle information
                vehicle_info = self.protocol_manager.get_vehicle_info()
                self.session_data['vehicle_info'] = vehicle_info
                
                # Start keep-alive thread
                self._start_keep_alive()
                
                return True
            else:
                self.state = SessionState.ERROR
                self.last_error = "Failed to connect to vehicle"
                self.log_event(f"Connection error: {self.last_error}")
                return False
        except Exception as e:
            self.state = SessionState.ERROR
            self.last_error = str(e)
            self.log_event(f"Connection error: {self.last_error}")
            logger.error(f"Error connecting to vehicle: {e}")
            return False
    
    def disconnect(self):
        """
        Disconnect from the vehicle
        
        Returns:
            bool: True if disconnected, False otherwise
        """
        try:
            # Stop keep-alive thread
            self._stop_keep_alive()
            
            # Disconnect from the vehicle
            if self.protocol_manager.disconnect():
                self.state = SessionState.DISCONNECTED
                self.log_event("Disconnected from vehicle")
                return True
            else:
                self.state = SessionState.ERROR
                self.last_error = "Failed to disconnect from vehicle"
                self.log_event(f"Disconnection error: {self.last_error}")
                return False
        except Exception as e:
            self.state = SessionState.ERROR
            self.last_error = str(e)
            self.log_event(f"Disconnection error: {self.last_error}")
            logger.error(f"Error disconnecting from vehicle: {e}")
            return False
    
    def read_dtc(self):
        """
        Read diagnostic trouble codes
        
        Returns:
            list: List of DTCs with detailed information
        """
        try:
            if self.state not in [SessionState.CONNECTED, SessionState.ACTIVE]:
                self.last_error = "Not connected to vehicle"
                self.log_event(f"Error reading DTCs: {self.last_error}")
                return []
            
            self.log_event("Reading DTCs")
            
            # Read DTCs from the vehicle
            dtcs = self.protocol_manager.read_dtc()
            
            if not dtcs:
                self.log_event("No DTCs found")
                return []
            
            # Get detailed information for each DTC
            detailed_dtcs = []
            for dtc in dtcs:
                code = dtc.get('code')
                if code:
                    # Get DTC information from the database
                    dtc_info = self.dtc_database.get_dtc_info(code)
                    
                    if dtc_info:
                        # Combine vehicle DTC with database information
                        detailed_dtc = {**dtc, **dtc_info}
                        detailed_dtcs.append(detailed_dtc)
                    else:
                        # No detailed information available
                        detailed_dtcs.append(dtc)
            
            self.log_event(f"Found {len(detailed_dtcs)} DTCs")
            
            # Store DTCs in session data
            self.session_data['dtcs'] = detailed_dtcs
            
            return detailed_dtcs
        except Exception as e:
            self.last_error = str(e)
            self.log_event(f"Error reading DTCs: {self.last_error}")
            logger.error(f"Error reading DTCs: {e}")
            return []
    
    def clear_dtc(self):
        """
        Clear diagnostic trouble codes
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if self.state not in [SessionState.CONNECTED, SessionState.ACTIVE]:
                self.last_error = "Not connected to vehicle"
                self.log_event(f"Error clearing DTCs: {self.last_error}")
                return False
            
            self.log_event("Clearing DTCs")
            
            # Clear DTCs
            if self.protocol_manager.clear_dtc():
                self.log_event("DTCs cleared successfully")
                
                # Clear DTCs from session data
                self.session_data['dtcs'] = []
                
                return True
            else:
                self.last_error = "Failed to clear DTCs"
                self.log_event(f"Error clearing DTCs: {self.last_error}")
                return False
        except Exception as e:
            self.last_error = str(e)
            self.log_event(f"Error clearing DTCs: {self.last_error}")
            logger.error(f"Error clearing DTCs: {e}")
            return False
    
    def read_data(self, pid):
        """
        Read data from the vehicle
        
        Args:
            pid: The parameter ID to read
            
        Returns:
            The data read from the vehicle
        """
        try:
            if self.state not in [SessionState.CONNECTED, SessionState.ACTIVE]:
                self.last_error = "Not connected to vehicle"
                self.log_event(f"Error reading data: {self.last_error}")
                return None
            
            self.log_event(f"Reading data (PID: {pid})")
            
            # Read data from the vehicle
            data = self.protocol_manager.read_data(pid)
            
            if data is not None:
                self.log_event(f"Data read successfully (PID: {pid})")
                return data
            else:
                self.last_error = f"Failed to read data (PID: {pid})"
                self.log_event(f"Error reading data: {self.last_error}")
                return None
        except Exception as e:
            self.last_error = str(e)
            self.log_event(f"Error reading data: {self.last_error}")
            logger.error(f"Error reading data: {e}")
            return None
    
    def write_data(self, pid, data):
        """
        Write data to the vehicle
        
        Args:
            pid: The parameter ID to write
            data: The data to write
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if self.state not in [SessionState.CONNECTED, SessionState.ACTIVE, SessionState.PROGRAMMING]:
                self.last_error = "Not connected to vehicle"
                self.log_event(f"Error writing data: {self.last_error}")
                return False
            
            self.log_event(f"Writing data (PID: {pid})")
            
            # Write data to the vehicle
            if self.protocol_manager.write_data(pid, data):
                self.log_event(f"Data written successfully (PID: {pid})")
                return True
            else:
                self.last_error = f"Failed to write data (PID: {pid})"
                self.log_event(f"Error writing data: {self.last_error}")
                return False
        except Exception as e:
            self.last_error = str(e)
            self.log_event(f"Error writing data: {self.last_error}")
            logger.error(f"Error writing data: {e}")
            return False
    
    def request_security_access(self, level):
        """
        Request security access
        
        Args:
            level (SecurityLevel): The security level to request
            
        Returns:
            bool: True if access granted, False otherwise
        """
        try:
            if self.state not in [SessionState.CONNECTED, SessionState.ACTIVE]:
                self.last_error = "Not connected to vehicle"
                self.log_event(f"Error requesting security access: {self.last_error}")
                return False
            
            self.log_event(f"Requesting security access (Level: {level.name})")
            
            # Request security access
            if self.protocol_manager.request_security_access(level):
                self.security_level = level
                self.log_event(f"Security access granted (Level: {level.name})")
                return True
            else:
                self.last_error = f"Failed to get security access (Level: {level.name})"
                self.log_event(f"Error requesting security access: {self.last_error}")
                return False
        except Exception as e:
            self.last_error = str(e)
            self.log_event(f"Error requesting security access: {self.last_error}")
            logger.error(f"Error requesting security access: {e}")
            return False
    
    def enter_programming_mode(self):
        """
        Enter programming mode
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if self.state not in [SessionState.CONNECTED, SessionState.ACTIVE]:
                self.last_error = "Not connected to vehicle"
                self.log_event(f"Error entering programming mode: {self.last_error}")
                return False
            
            self.log_event("Entering programming mode")
            
            # Get the current protocol
            protocol = self.protocol_manager.current_protocol
            
            if not protocol:
                self.last_error = "No active protocol"
                self.log_event(f"Error entering programming mode: {self.last_error}")
                return False
            
            # Enter programming session
            if protocol.enter_diagnostic_session(protocol.session_programming):
                self.state = SessionState.PROGRAMMING
                self.log_event("Entered programming mode")
                return True
            else:
                self.last_error = "Failed to enter programming mode"
                self.log_event(f"Error entering programming mode: {self.last_error}")
                return False
        except Exception as e:
            self.last_error = str(e)
            self.log_event(f"Error entering programming mode: {self.last_error}")
            logger.error(f"Error entering programming mode: {e}")
            return False
    
    def exit_programming_mode(self):
        """
        Exit programming mode
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if self.state != SessionState.PROGRAMMING:
                self.last_error = "Not in programming mode"
                self.log_event(f"Error exiting programming mode: {self.last_error}")
                return False
            
            self.log_event("Exiting programming mode")
            
            # Get the current protocol
            protocol = self.protocol_manager.current_protocol
            
            if not protocol:
                self.last_error = "No active protocol"
                self.log_event(f"Error exiting programming mode: {self.last_error}")
                return False
            
            # Exit programming session
            if protocol.exit_diagnostic_session():
                self.state = SessionState.CONNECTED
                self.log_event("Exited programming mode")
                return True
            else:
                self.last_error = "Failed to exit programming mode"
                self.log_event(f"Error exiting programming mode: {self.last_error}")
                return False
        except Exception as e:
            self.last_error = str(e)
            self.log_event(f"Error exiting programming mode: {self.last_error}")
            logger.error(f"Error exiting programming mode: {e}")
            return False
    
    def get_session_info(self):
        """
        Get session information
        
        Returns:
            dict: Session information
        """
        try:
            # Get protocol information
            protocol_info = self.protocol_manager.get_protocol_info()
            
            # Get vehicle information
            vehicle_info = self.protocol_manager.get_vehicle_info()
            
            # Combine information
            session_info = {
                'state': self.state.name,
                'protocol': protocol_info,
                'vehicle': vehicle_info,
                'security_level': self.security_level.name,
                'last_error': self.last_error
            }
            
            return session_info
        except Exception as e:
            logger.error(f"Error getting session information: {e}")
            return {
                'state': self.state.name,
                'error': str(e)
            }
    
    def save_session(self, filename):
        """
        Save the session to a file
        
        Args:
            filename (str): The filename
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Create session data
            session_data = {
                'timestamp': time.time(),
                'date': time.strftime('%Y-%m-%d %H:%M:%S'),
                'state': self.state.name,
                'vehicle_info': self.session_data.get('vehicle_info', {}),
                'dtcs': self.session_data.get('dtcs', []),
                'protocol_info': self.protocol_manager.get_protocol_info(),
                'log': self.session_log
            }
            
            # Save to file
            with open(filename, 'w') as f:
                json.dump(session_data, f, indent=2)
            
            self.log_event(f"Session saved to {filename}")
            return True
        except Exception as e:
            self.last_error = str(e)
            self.log_event(f"Error saving session: {self.last_error}")
            logger.error(f"Error saving session: {e}")
            return False
    
    def load_session(self, filename):
        """
        Load a session from a file
        
        Args:
            filename (str): The filename
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if file exists
            if not os.path.exists(filename):
                self.last_error = f"File not found: {filename}"
                self.log_event(f"Error loading session: {self.last_error}")
                return False
            
            # Load from file
            with open(filename, 'r') as f:
                session_data = json.load(f)
            
            # Restore session data
            self.session_data = session_data
            self.session_log = session_data.get('log', [])
            
            self.log_event(f"Session loaded from {filename}")
            return True
        except Exception as e:
            self.last_error = str(e)
            self.log_event(f"Error loading session: {self.last_error}")
            logger.error(f"Error loading session: {e}")
            return False
    
    def log_event(self, message):
        """
        Log an event
        
        Args:
            message (str): The message
        """
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
        event = {
            'timestamp': timestamp,
            'message': message
        }
        self.session_log.append(event)
        logger.info(message)
    
    def _start_keep_alive(self):
        """Start the keep-alive thread"""
        if self.keep_alive_thread is None or not self.keep_alive_thread.is_alive():
            self.keep_alive_running = True
            self.keep_alive_thread = threading.Thread(target=self._keep_alive_worker)
            self.keep_alive_thread.daemon = True
            self.keep_alive_thread.start()
    
    def _stop_keep_alive(self):
        """Stop the keep-alive thread"""
        self.keep_alive_running = False
        if self.keep_alive_thread is not None and self.keep_alive_thread.is_alive():
            self.keep_alive_thread.join(timeout=1.0)
    
    def _keep_alive_worker(self):
        """Keep-alive worker thread"""
        while self.keep_alive_running:
            try:
                if self.state in [SessionState.CONNECTED, SessionState.ACTIVE, SessionState.PROGRAMMING]:
                    # Get the current protocol
                    protocol = self.protocol_manager.current_protocol
                    
                    if protocol:
                        # Send tester present command
                        protocol.send_command([0x3E, 0x00], response_required=False)
            except Exception as e:
                logger.error(f"Error in keep-alive thread: {e}")
            
            # Sleep for the keep-alive interval
            time.sleep(self.keep_alive_interval)
