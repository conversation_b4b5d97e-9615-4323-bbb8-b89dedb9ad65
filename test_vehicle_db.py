#!/usr/bin/env python3
"""
Test Vehicle Database
This script tests the vehicle database schema and adds a sample vehicle with ECU information.
"""

import os
import sys
from database.vehicle_db import VehicleDatabase, Make, Model, Vehicle, ECU

def main():
    """Main function"""
    print("Testing vehicle database...")
    
    # Initialize database
    db = VehicleDatabase()
    
    # Create a session
    session = db.Session()
    
    # Check if BMW exists
    bmw = session.query(Make).filter(Make.name == "BMW").first()
    
    if not bmw:
        # Create BMW
        bmw = Make(name="BMW")
        session.add(bmw)
        session.flush()
        print("Added BMW make")
    
    # Check if 3 Series exists
    series3 = session.query(Model).filter(
        Model.name == "3 Series",
        Model.make_id == bmw.id
    ).first()
    
    if not series3:
        # Create 3 Series
        series3 = Model(name="3 Series", make_id=bmw.id)
        session.add(series3)
        session.flush()
        print("Added 3 Series model")
    
    # Check if 2020 330i exists
    bmw330i = session.query(Vehicle).filter(
        Vehicle.model_id == series3.id,
        Vehicle.year == 2020,
        Vehicle.variant == "330i"
    ).first()
    
    if not bmw330i:
        # Create 2020 330i
        bmw330i = Vehicle(
            model_id=series3.id,
            year=2020,
            generation="G20",
            variant="330i",
            engine_type="Inline-4 Turbo",
            engine_displacement=2.0,
            fuel_type="Gasoline",
            transmission_type="Automatic",
            num_cylinders=4,
            body_style="Sedan",
            drive_type="RWD",
            obd_protocol="ISO15765-4 (CAN)",
            manufacturer_protocol="BMW UDS",
            obd_port_location="Under dashboard, driver's side, near center console",
            ecu_location="Engine compartment, driver's side, near firewall",
            security_level=3,
            special_notes="Requires BMW ISTA+ for programming",
            vin_pattern="^WBA[0-9A-Z]{11}$",
            engine_code="B48"
        )
        session.add(bmw330i)
        session.flush()
        print("Added 2020 BMW 330i")
        
        # Add ECUs
        dme = ECU(
            vehicle_id=bmw330i.id,
            name="Digital Motor Electronics (DME)",
            code="MEVD17.2",
            location="Engine compartment, driver's side, near firewall",
            protocol="ISO15765-4 (CAN)",
            address="0x12",
            security_type="Seed-Key",
            flash_method="BMW ISTA+",
            special_notes="B48 engine control module"
        )
        session.add(dme)
        
        cas = ECU(
            vehicle_id=bmw330i.id,
            name="Car Access System (CAS)",
            code="CAS4+",
            location="Under dashboard",
            protocol="ISO15765-4 (CAN)",
            address="0x3F",
            security_type="Seed-Key",
            flash_method="BMW ISTA+",
            special_notes="Immobilizer and key management"
        )
        session.add(cas)
        
        print("Added ECUs for 2020 BMW 330i")
    
    # Commit changes
    session.commit()
    
    # Test getting vehicle
    vehicle = db.get_vehicle("BMW", "3 Series", 2020, "330i")
    
    if vehicle:
        print("\nVehicle details:")
        print(f"Make: {vehicle['make']}")
        print(f"Model: {vehicle['model']}")
        print(f"Year: {vehicle['year']}")
        print(f"Generation: {vehicle['generation']}")
        print(f"Variant: {vehicle['variant']}")
        print(f"Engine: {vehicle['engine_type']} {vehicle['engine_displacement']}L {vehicle['num_cylinders']} cylinder")
        print(f"Fuel Type: {vehicle['fuel_type']}")
        print(f"Transmission: {vehicle['transmission_type']}")
        print(f"Body Style: {vehicle['body_style']}")
        print(f"Drive Type: {vehicle['drive_type']}")
        print(f"OBD Protocol: {vehicle['obd_protocol']}")
        print(f"Manufacturer Protocol: {vehicle['manufacturer_protocol']}")
        print(f"OBD Port Location: {vehicle['obd_port_location']}")
        print(f"ECU Location: {vehicle['ecu_location']}")
        print(f"Security Level: {vehicle['security_level']}")
        print(f"Special Notes: {vehicle['special_notes']}")
        print(f"VIN Pattern: {vehicle['vin_pattern']}")
        print(f"Engine Code: {vehicle['engine_code']}")
        
        print("\nECUs:")
        for ecu in vehicle['ecus']:
            print(f"  {ecu['name']} ({ecu['code']})")
            print(f"    Location: {ecu['location']}")
            print(f"    Protocol: {ecu['protocol']}")
            print(f"    Address: {ecu['address']}")
            print(f"    Security Type: {ecu['security_type']}")
            print(f"    Flash Method: {ecu['flash_method']}")
            print(f"    Special Notes: {ecu['special_notes']}")
    else:
        print("Vehicle not found")
    
    # Close session
    session.close()
    
    print("\nTest complete!")

if __name__ == "__main__":
    main()
