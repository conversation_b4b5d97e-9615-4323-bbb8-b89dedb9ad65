#!/usr/bin/env python3
"""
ECU Coding Module
This module provides functionality for ECU coding and adaptation.
"""

import logging
import time
import os
import json
import struct
from enum import Enum, auto
from typing import Dict, List, Optional, Any, Tuple, BinaryIO, Union

from protocols.protocol_handler import BaseProtocol, SecurityLevel, SecurityAccessStatus, ProtocolError, CommunicationError

logger = logging.getLogger("diagnostic.ecu_coding")

class CodingType(Enum):
    """Coding types"""
    BINARY = auto()
    TEXT = auto()
    JSON = auto()
    XML = auto()

class CodingError(Exception):
    """Coding error"""
    pass

class AdaptationChannel:
    """Adaptation channel"""
    
    def __init__(self, channel_id: int, name: str, description: str = "", min_value: int = 0, max_value: int = 0, 
                 default_value: int = 0, unit: str = "", data_type: str = "int"):
        """
        Initialize the adaptation channel
        
        Args:
            channel_id (int): The channel ID
            name (str): The channel name
            description (str): The channel description
            min_value (int): The minimum value
            max_value (int): The maximum value
            default_value (int): The default value
            unit (str): The unit
            data_type (str): The data type
        """
        self.channel_id = channel_id
        self.name = name
        self.description = description
        self.min_value = min_value
        self.max_value = max_value
        self.default_value = default_value
        self.unit = unit
        self.data_type = data_type
        self.value = default_value
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert to dictionary
        
        Returns:
            dict: Dictionary representation
        """
        return {
            'channel_id': self.channel_id,
            'name': self.name,
            'description': self.description,
            'min_value': self.min_value,
            'max_value': self.max_value,
            'default_value': self.default_value,
            'unit': self.unit,
            'data_type': self.data_type,
            'value': self.value
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AdaptationChannel':
        """
        Create from dictionary
        
        Args:
            data (dict): Dictionary representation
            
        Returns:
            AdaptationChannel: The adaptation channel
        """
        channel = cls(
            channel_id=data.get('channel_id', 0),
            name=data.get('name', ''),
            description=data.get('description', ''),
            min_value=data.get('min_value', 0),
            max_value=data.get('max_value', 0),
            default_value=data.get('default_value', 0),
            unit=data.get('unit', ''),
            data_type=data.get('data_type', 'int')
        )
        
        if 'value' in data:
            channel.value = data['value']
        
        return channel

class CodingData:
    """Coding data"""
    
    def __init__(self, module_id: int, coding_id: int, name: str, description: str = "", coding_type: CodingType = CodingType.BINARY):
        """
        Initialize the coding data
        
        Args:
            module_id (int): The module ID
            coding_id (int): The coding ID
            name (str): The coding name
            description (str): The coding description
            coding_type (CodingType): The coding type
        """
        self.module_id = module_id
        self.coding_id = coding_id
        self.name = name
        self.description = description
        self.coding_type = coding_type
        self.data = b''
        self.parsed_data: Dict[str, Any] = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert to dictionary
        
        Returns:
            dict: Dictionary representation
        """
        return {
            'module_id': self.module_id,
            'coding_id': self.coding_id,
            'name': self.name,
            'description': self.description,
            'coding_type': self.coding_type.name,
            'data': self.data.hex() if self.data else '',
            'parsed_data': self.parsed_data
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CodingData':
        """
        Create from dictionary
        
        Args:
            data (dict): Dictionary representation
            
        Returns:
            CodingData: The coding data
        """
        coding_type = CodingType.BINARY
        if 'coding_type' in data:
            try:
                coding_type = CodingType[data['coding_type']]
            except (KeyError, ValueError):
                pass
        
        coding = cls(
            module_id=data.get('module_id', 0),
            coding_id=data.get('coding_id', 0),
            name=data.get('name', ''),
            description=data.get('description', ''),
            coding_type=coding_type
        )
        
        if 'data' in data and data['data']:
            try:
                coding.data = bytes.fromhex(data['data'])
            except (ValueError, TypeError):
                pass
        
        if 'parsed_data' in data:
            coding.parsed_data = data['parsed_data']
        
        return coding

class ECUCoding:
    """ECU coding"""
    
    def __init__(self, protocol: BaseProtocol = None):
        """
        Initialize the ECU coding
        
        Args:
            protocol (BaseProtocol): The protocol to use
        """
        self.protocol = protocol
        self.security_level = SecurityLevel.LEVEL_1
        self.adaptation_channels: Dict[int, AdaptationChannel] = {}
        self.coding_data: Dict[int, CodingData] = {}
        
        # Common service IDs for coding
        self.service_diagnostic_session_control = 0x10
        self.service_ecu_reset = 0x11
        self.service_security_access = 0x27
        self.service_read_data_by_identifier = 0x22
        self.service_write_data_by_identifier = 0x2E
        self.service_input_output_control_by_identifier = 0x2F
        
        # Common diagnostic session types
        self.session_default = 0x01
        self.session_programming = 0x02
        self.session_extended = 0x03
    
    def set_protocol(self, protocol: BaseProtocol):
        """
        Set the protocol
        
        Args:
            protocol (BaseProtocol): The protocol to use
        """
        self.protocol = protocol
    
    def enter_extended_session(self) -> bool:
        """
        Enter extended diagnostic session
        
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.protocol:
            raise CodingError("No protocol set")
        
        try:
            # Enter extended session
            response = self.protocol.send_command([self.service_diagnostic_session_control, self.session_extended])
            
            if not response or response[0] != (self.service_diagnostic_session_control + 0x40):
                logger.error("Failed to enter extended session")
                return False
            
            # Wait for ECU to enter extended mode
            time.sleep(0.5)
            
            # Request security access
            if not self.request_security_access():
                logger.error("Failed to get security access")
                return False
            
            logger.info("Entered extended session")
            return True
        except Exception as e:
            logger.error(f"Error entering extended session: {e}")
            return False
    
    def request_security_access(self) -> bool:
        """
        Request security access
        
        Returns:
            bool: True if access granted, False otherwise
        """
        if not self.protocol:
            raise CodingError("No protocol set")
        
        try:
            # Request security access
            return self.protocol.request_security_access(self.security_level)
        except Exception as e:
            logger.error(f"Error requesting security access: {e}")
            return False
    
    def read_coding(self, module_id: int, coding_id: int) -> Optional[CodingData]:
        """
        Read coding data
        
        Args:
            module_id (int): The module ID
            coding_id (int): The coding ID
            
        Returns:
            CodingData: The coding data or None if failed
        """
        if not self.protocol:
            raise CodingError("No protocol set")
        
        try:
            # Enter extended session
            if not self.enter_extended_session():
                return None
            
            # Read data by identifier
            identifier = (coding_id << 8) | module_id
            response = self.protocol.send_command([self.service_read_data_by_identifier, (identifier >> 8) & 0xFF, identifier & 0xFF])
            
            if not response or response[0] != (self.service_read_data_by_identifier + 0x40):
                logger.error(f"Failed to read coding data for module {module_id:02X}, coding {coding_id:02X}")
                return None
            
            # Extract data
            data = bytes(response[3:])
            
            # Create coding data
            coding_data = CodingData(module_id, coding_id, f"Coding {module_id:02X}-{coding_id:02X}")
            coding_data.data = data
            
            # Parse data
            self.parse_coding_data(coding_data)
            
            # Store coding data
            self.coding_data[coding_id] = coding_data
            
            logger.info(f"Read coding data for module {module_id:02X}, coding {coding_id:02X}")
            return coding_data
        except Exception as e:
            logger.error(f"Error reading coding data: {e}")
            return None
    
    def write_coding(self, coding_data: CodingData) -> bool:
        """
        Write coding data
        
        Args:
            coding_data (CodingData): The coding data
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.protocol:
            raise CodingError("No protocol set")
        
        try:
            # Enter extended session
            if not self.enter_extended_session():
                return False
            
            # Write data by identifier
            identifier = (coding_data.coding_id << 8) | coding_data.module_id
            command = [self.service_write_data_by_identifier, (identifier >> 8) & 0xFF, identifier & 0xFF]
            command.extend(coding_data.data)
            
            response = self.protocol.send_command(command)
            
            if not response or response[0] != (self.service_write_data_by_identifier + 0x40):
                logger.error(f"Failed to write coding data for module {coding_data.module_id:02X}, coding {coding_data.coding_id:02X}")
                return False
            
            logger.info(f"Wrote coding data for module {coding_data.module_id:02X}, coding {coding_data.coding_id:02X}")
            return True
        except Exception as e:
            logger.error(f"Error writing coding data: {e}")
            return False
    
    def parse_coding_data(self, coding_data: CodingData) -> bool:
        """
        Parse coding data
        
        Args:
            coding_data (CodingData): The coding data
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Parse based on coding type
            if coding_data.coding_type == CodingType.BINARY:
                # For binary data, just store the hex representation
                coding_data.parsed_data = {'hex': coding_data.data.hex()}
            elif coding_data.coding_type == CodingType.TEXT:
                # For text data, decode as UTF-8
                try:
                    coding_data.parsed_data = {'text': coding_data.data.decode('utf-8')}
                except UnicodeDecodeError:
                    coding_data.parsed_data = {'hex': coding_data.data.hex()}
            elif coding_data.coding_type == CodingType.JSON:
                # For JSON data, parse as JSON
                try:
                    coding_data.parsed_data = json.loads(coding_data.data.decode('utf-8'))
                except (UnicodeDecodeError, json.JSONDecodeError):
                    coding_data.parsed_data = {'hex': coding_data.data.hex()}
            elif coding_data.coding_type == CodingType.XML:
                # For XML data, just store as text
                try:
                    coding_data.parsed_data = {'xml': coding_data.data.decode('utf-8')}
                except UnicodeDecodeError:
                    coding_data.parsed_data = {'hex': coding_data.data.hex()}
            
            return True
        except Exception as e:
            logger.error(f"Error parsing coding data: {e}")
            return False
    
    def read_adaptation_channel(self, module_id: int, channel_id: int) -> Optional[AdaptationChannel]:
        """
        Read adaptation channel
        
        Args:
            module_id (int): The module ID
            channel_id (int): The channel ID
            
        Returns:
            AdaptationChannel: The adaptation channel or None if failed
        """
        if not self.protocol:
            raise CodingError("No protocol set")
        
        try:
            # Enter extended session
            if not self.enter_extended_session():
                return None
            
            # Read data by identifier
            identifier = (channel_id << 8) | module_id
            response = self.protocol.send_command([self.service_read_data_by_identifier, (identifier >> 8) & 0xFF, identifier & 0xFF])
            
            if not response or response[0] != (self.service_read_data_by_identifier + 0x40):
                logger.error(f"Failed to read adaptation channel for module {module_id:02X}, channel {channel_id:02X}")
                return None
            
            # Extract data
            data = bytes(response[3:])
            
            # Create adaptation channel
            channel = AdaptationChannel(channel_id, f"Channel {channel_id:02X}")
            
            # Parse data
            if len(data) >= 2:
                channel.value = int.from_bytes(data, byteorder='big')
            
            # Store adaptation channel
            self.adaptation_channels[channel_id] = channel
            
            logger.info(f"Read adaptation channel for module {module_id:02X}, channel {channel_id:02X}")
            return channel
        except Exception as e:
            logger.error(f"Error reading adaptation channel: {e}")
            return None
    
    def write_adaptation_channel(self, module_id: int, channel: AdaptationChannel) -> bool:
        """
        Write adaptation channel
        
        Args:
            module_id (int): The module ID
            channel (AdaptationChannel): The adaptation channel
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.protocol:
            raise CodingError("No protocol set")
        
        try:
            # Enter extended session
            if not self.enter_extended_session():
                return False
            
            # Write data by identifier
            identifier = (channel.channel_id << 8) | module_id
            command = [self.service_write_data_by_identifier, (identifier >> 8) & 0xFF, identifier & 0xFF]
            
            # Add value
            if channel.data_type == "int":
                # Determine number of bytes needed
                if channel.max_value <= 0xFF:
                    command.append(channel.value & 0xFF)
                elif channel.max_value <= 0xFFFF:
                    command.append((channel.value >> 8) & 0xFF)
                    command.append(channel.value & 0xFF)
                elif channel.max_value <= 0xFFFFFF:
                    command.append((channel.value >> 16) & 0xFF)
                    command.append((channel.value >> 8) & 0xFF)
                    command.append(channel.value & 0xFF)
                else:
                    command.append((channel.value >> 24) & 0xFF)
                    command.append((channel.value >> 16) & 0xFF)
                    command.append((channel.value >> 8) & 0xFF)
                    command.append(channel.value & 0xFF)
            
            response = self.protocol.send_command(command)
            
            if not response or response[0] != (self.service_write_data_by_identifier + 0x40):
                logger.error(f"Failed to write adaptation channel for module {module_id:02X}, channel {channel.channel_id:02X}")
                return False
            
            logger.info(f"Wrote adaptation channel for module {module_id:02X}, channel {channel.channel_id:02X}")
            return True
        except Exception as e:
            logger.error(f"Error writing adaptation channel: {e}")
            return False
    
    def load_adaptation_channels(self, file_path: str) -> bool:
        """
        Load adaptation channels from file
        
        Args:
            file_path (str): Path to the file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if file exists
            if not os.path.exists(file_path):
                logger.error(f"File not found: {file_path}")
                return False
            
            # Load JSON file
            with open(file_path, 'r') as file:
                data = json.load(file)
            
            # Parse channels
            if isinstance(data, list):
                for channel_data in data:
                    channel = AdaptationChannel.from_dict(channel_data)
                    self.adaptation_channels[channel.channel_id] = channel
            elif isinstance(data, dict):
                for channel_id, channel_data in data.items():
                    try:
                        channel_id = int(channel_id)
                        channel = AdaptationChannel.from_dict(channel_data)
                        channel.channel_id = channel_id
                        self.adaptation_channels[channel_id] = channel
                    except ValueError:
                        pass
            
            logger.info(f"Loaded {len(self.adaptation_channels)} adaptation channels from {file_path}")
            return True
        except Exception as e:
            logger.error(f"Error loading adaptation channels: {e}")
            return False
    
    def save_adaptation_channels(self, file_path: str) -> bool:
        """
        Save adaptation channels to file
        
        Args:
            file_path (str): Path to the file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Convert channels to dictionaries
            channels = {}
            for channel_id, channel in self.adaptation_channels.items():
                channels[str(channel_id)] = channel.to_dict()
            
            # Save to JSON file
            with open(file_path, 'w') as file:
                json.dump(channels, file, indent=2)
            
            logger.info(f"Saved {len(self.adaptation_channels)} adaptation channels to {file_path}")
            return True
        except Exception as e:
            logger.error(f"Error saving adaptation channels: {e}")
            return False
    
    def load_coding_data(self, file_path: str) -> bool:
        """
        Load coding data from file
        
        Args:
            file_path (str): Path to the file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if file exists
            if not os.path.exists(file_path):
                logger.error(f"File not found: {file_path}")
                return False
            
            # Load JSON file
            with open(file_path, 'r') as file:
                data = json.load(file)
            
            # Parse coding data
            if isinstance(data, list):
                for coding_data in data:
                    coding = CodingData.from_dict(coding_data)
                    self.coding_data[coding.coding_id] = coding
            elif isinstance(data, dict):
                for coding_id, coding_data in data.items():
                    try:
                        coding_id = int(coding_id)
                        coding = CodingData.from_dict(coding_data)
                        coding.coding_id = coding_id
                        self.coding_data[coding_id] = coding
                    except ValueError:
                        pass
            
            logger.info(f"Loaded {len(self.coding_data)} coding data from {file_path}")
            return True
        except Exception as e:
            logger.error(f"Error loading coding data: {e}")
            return False
    
    def save_coding_data(self, file_path: str) -> bool:
        """
        Save coding data to file
        
        Args:
            file_path (str): Path to the file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Convert coding data to dictionaries
            coding_data = {}
            for coding_id, coding in self.coding_data.items():
                coding_data[str(coding_id)] = coding.to_dict()
            
            # Save to JSON file
            with open(file_path, 'w') as file:
                json.dump(coding_data, file, indent=2)
            
            logger.info(f"Saved {len(self.coding_data)} coding data to {file_path}")
            return True
        except Exception as e:
            logger.error(f"Error saving coding data: {e}")
            return False
