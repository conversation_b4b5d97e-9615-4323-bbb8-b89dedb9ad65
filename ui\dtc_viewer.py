#!/usr/bin/env python3
"""
DTC Viewer Module
This module provides a viewer for diagnostic trouble codes.
"""

import sys
import os
import logging
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
                            QComboBox, QLineEdit, QTextEdit, QGroupBox, QFormLayout, 
                            QTableWidget, QTableWidgetItem, QHeaderView, QSplitter,
                            QTabWidget, QCheckBox, QRadioButton, QButtonGroup, QMessageBox)
from PyQt5.QtCore import Qt, QSize, pyqtSignal
from PyQt5.QtGui import QIcon, QFont, QColor, QPixmap

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("ui.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("ui.dtc_viewer")

class DTCViewer(QWidget):
    """DTC viewer widget"""
    
    # Signals
    dtc_selected = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        """Initialize the DTC viewer widget"""
        super().__init__(parent)
        
        # Set up the layout
        self.layout = QVBoxLayout(self)
        
        # Create the search panel
        self.create_search_panel()
        
        # Create the DTC table
        self.create_dtc_table()
        
        # Create the details panel
        self.create_details_panel()
        
        # Initialize data
        self.dtc_codes = []
        self.filtered_codes = []
        self.selected_dtc = None
    
    def create_search_panel(self):
        """Create the search panel"""
        search_group = QGroupBox("Search DTCs")
        search_layout = QVBoxLayout()
        
        # Search bar
        search_bar_layout = QHBoxLayout()
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search by code or description...")
        self.search_input.textChanged.connect(self.filter_dtcs)
        
        self.search_button = QPushButton("Search")
        self.search_button.clicked.connect(self.filter_dtcs)
        
        self.clear_button = QPushButton("Clear")
        self.clear_button.clicked.connect(self.clear_search)
        
        search_bar_layout.addWidget(self.search_input)
        search_bar_layout.addWidget(self.search_button)
        search_bar_layout.addWidget(self.clear_button)
        
        search_layout.addLayout(search_bar_layout)
        
        # Filter options
        filter_layout = QHBoxLayout()
        
        # Category filter
        category_layout = QVBoxLayout()
        category_label = QLabel("Category:")
        self.category_combo = QComboBox()
        self.category_combo.addItem("All Categories")
        self.category_combo.currentIndexChanged.connect(self.filter_dtcs)
        
        category_layout.addWidget(category_label)
        category_layout.addWidget(self.category_combo)
        
        # Manufacturer filter
        manufacturer_layout = QVBoxLayout()
        manufacturer_label = QLabel("Manufacturer:")
        self.manufacturer_combo = QComboBox()
        self.manufacturer_combo.addItem("All Manufacturers")
        self.manufacturer_combo.currentIndexChanged.connect(self.filter_dtcs)
        
        manufacturer_layout.addWidget(manufacturer_label)
        manufacturer_layout.addWidget(self.manufacturer_combo)
        
        # System filter
        system_layout = QVBoxLayout()
        system_label = QLabel("System:")
        self.system_combo = QComboBox()
        self.system_combo.addItem("All Systems")
        self.system_combo.currentIndexChanged.connect(self.filter_dtcs)
        
        system_layout.addWidget(system_label)
        system_layout.addWidget(self.system_combo)
        
        # Severity filter
        severity_layout = QVBoxLayout()
        severity_label = QLabel("Severity:")
        self.severity_combo = QComboBox()
        self.severity_combo.addItems(["All Severities", "1 - Low", "2 - Medium", "3 - High", "4 - Critical"])
        self.severity_combo.currentIndexChanged.connect(self.filter_dtcs)
        
        severity_layout.addWidget(severity_label)
        severity_layout.addWidget(self.severity_combo)
        
        filter_layout.addLayout(category_layout)
        filter_layout.addLayout(manufacturer_layout)
        filter_layout.addLayout(system_layout)
        filter_layout.addLayout(severity_layout)
        
        search_layout.addLayout(filter_layout)
        
        search_group.setLayout(search_layout)
        self.layout.addWidget(search_group)
    
    def create_dtc_table(self):
        """Create the DTC table"""
        self.dtc_table = QTableWidget()
        self.dtc_table.setColumnCount(5)
        self.dtc_table.setHorizontalHeaderLabels(["Code", "Description", "Severity", "Manufacturer", "System"])
        self.dtc_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.dtc_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.dtc_table.setSelectionMode(QTableWidget.SingleSelection)
        self.dtc_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.dtc_table.setAlternatingRowColors(True)
        self.dtc_table.setSortingEnabled(True)
        self.dtc_table.setMinimumHeight(300)
        
        self.dtc_table.selectionModel().selectionChanged.connect(self.on_dtc_selected)
        
        self.layout.addWidget(self.dtc_table)
    
    def create_details_panel(self):
        """Create the details panel"""
        details_group = QGroupBox("DTC Details")
        details_layout = QVBoxLayout()
        
        # Create tabs
        self.details_tabs = QTabWidget()
        
        # Overview tab
        overview_widget = QWidget()
        overview_layout = QFormLayout(overview_widget)
        
        self.code_label = QLabel("N/A")
        self.description_label = QLabel("N/A")
        self.severity_label = QLabel("N/A")
        self.manufacturer_label = QLabel("N/A")
        self.system_label = QLabel("N/A")
        self.subsystem_label = QLabel("N/A")
        
        overview_layout.addRow("Code:", self.code_label)
        overview_layout.addRow("Description:", self.description_label)
        overview_layout.addRow("Severity:", self.severity_label)
        overview_layout.addRow("Manufacturer:", self.manufacturer_label)
        overview_layout.addRow("System:", self.system_label)
        overview_layout.addRow("Subsystem:", self.subsystem_label)
        
        self.details_tabs.addTab(overview_widget, "Overview")
        
        # Diagnostic tab
        diagnostic_widget = QWidget()
        diagnostic_layout = QVBoxLayout(diagnostic_widget)
        
        # Possible causes
        causes_group = QGroupBox("Possible Causes")
        causes_layout = QVBoxLayout()
        self.causes_text = QTextEdit()
        self.causes_text.setReadOnly(True)
        causes_layout.addWidget(self.causes_text)
        causes_group.setLayout(causes_layout)
        
        # Solutions
        solutions_group = QGroupBox("Solutions")
        solutions_layout = QVBoxLayout()
        self.solutions_text = QTextEdit()
        self.solutions_text.setReadOnly(True)
        solutions_layout.addWidget(self.solutions_text)
        solutions_group.setLayout(solutions_layout)
        
        diagnostic_layout.addWidget(causes_group)
        diagnostic_layout.addWidget(solutions_group)
        
        self.details_tabs.addTab(diagnostic_widget, "Diagnostic")
        
        # Technical tab
        technical_widget = QWidget()
        technical_layout = QFormLayout(technical_widget)
        
        self.symptoms_label = QLabel("N/A")
        self.symptoms_label.setWordWrap(True)
        
        self.testing_procedures_label = QLabel("N/A")
        self.testing_procedures_label.setWordWrap(True)
        
        self.component_location_label = QLabel("N/A")
        self.component_location_label.setWordWrap(True)
        
        self.repair_difficulty_label = QLabel("N/A")
        self.repair_cost_label = QLabel("N/A")
        self.repair_time_label = QLabel("N/A")
        self.tools_required_label = QLabel("N/A")
        self.tools_required_label.setWordWrap(True)
        
        technical_layout.addRow("Symptoms:", self.symptoms_label)
        technical_layout.addRow("Testing Procedures:", self.testing_procedures_label)
        technical_layout.addRow("Component Location:", self.component_location_label)
        technical_layout.addRow("Repair Difficulty:", self.repair_difficulty_label)
        technical_layout.addRow("Repair Cost:", self.repair_cost_label)
        technical_layout.addRow("Repair Time:", self.repair_time_label)
        technical_layout.addRow("Tools Required:", self.tools_required_label)
        
        self.details_tabs.addTab(technical_widget, "Technical")
        
        # Related tab
        related_widget = QWidget()
        related_layout = QVBoxLayout(related_widget)
        
        # Related codes
        related_codes_group = QGroupBox("Related Codes")
        related_codes_layout = QVBoxLayout()
        self.related_codes_text = QTextEdit()
        self.related_codes_text.setReadOnly(True)
        related_codes_layout.addWidget(self.related_codes_text)
        related_codes_group.setLayout(related_codes_layout)
        
        # Applicable models
        applicable_models_group = QGroupBox("Applicable Models")
        applicable_models_layout = QVBoxLayout()
        self.applicable_models_text = QTextEdit()
        self.applicable_models_text.setReadOnly(True)
        applicable_models_layout.addWidget(self.applicable_models_text)
        applicable_models_group.setLayout(applicable_models_layout)
        
        related_layout.addWidget(related_codes_group)
        related_layout.addWidget(applicable_models_group)
        
        self.details_tabs.addTab(related_widget, "Related")
        
        details_layout.addWidget(self.details_tabs)
        
        # Action buttons
        action_layout = QHBoxLayout()
        
        self.read_live_data_button = QPushButton("Read Live Data")
        self.read_live_data_button.clicked.connect(self.on_read_live_data)
        
        self.clear_dtc_button = QPushButton("Clear DTC")
        self.clear_dtc_button.clicked.connect(self.on_clear_dtc)
        
        self.freeze_frame_button = QPushButton("Freeze Frame Data")
        self.freeze_frame_button.clicked.connect(self.on_freeze_frame)
        
        action_layout.addWidget(self.read_live_data_button)
        action_layout.addWidget(self.clear_dtc_button)
        action_layout.addWidget(self.freeze_frame_button)
        
        details_layout.addLayout(action_layout)
        
        details_group.setLayout(details_layout)
        self.layout.addWidget(details_group)
    
    def set_dtc_codes(self, codes):
        """
        Set the DTC codes
        
        Args:
            codes (list): List of DTC codes
        """
        self.dtc_codes = codes
        self.filtered_codes = codes
        
        # Populate the table
        self.populate_table()
        
        # Populate the filter combos
        self.populate_filter_combos()
    
    def populate_table(self):
        """Populate the DTC table"""
        # Clear the table
        self.dtc_table.setRowCount(0)
        
        # Add rows
        self.dtc_table.setSortingEnabled(False)
        self.dtc_table.setRowCount(len(self.filtered_codes))
        
        for i, code in enumerate(self.filtered_codes):
            # Code
            code_item = QTableWidgetItem(code.get('code', 'N/A'))
            self.dtc_table.setItem(i, 0, code_item)
            
            # Description
            description_item = QTableWidgetItem(code.get('description', 'N/A'))
            self.dtc_table.setItem(i, 1, description_item)
            
            # Severity
            severity = code.get('severity', 0)
            severity_item = QTableWidgetItem(str(severity))
            severity_item.setData(Qt.DisplayRole, severity)  # For sorting
            
            # Set background color based on severity
            if severity == 4:
                severity_item.setBackground(QColor(255, 0, 0, 100))  # Red
            elif severity == 3:
                severity_item.setBackground(QColor(255, 165, 0, 100))  # Orange
            elif severity == 2:
                severity_item.setBackground(QColor(255, 255, 0, 100))  # Yellow
            elif severity == 1:
                severity_item.setBackground(QColor(0, 255, 0, 100))  # Green
            
            self.dtc_table.setItem(i, 2, severity_item)
            
            # Manufacturer
            manufacturer_item = QTableWidgetItem(code.get('manufacturer', 'Generic'))
            self.dtc_table.setItem(i, 3, manufacturer_item)
            
            # System
            system_item = QTableWidgetItem(code.get('system', 'N/A'))
            self.dtc_table.setItem(i, 4, system_item)
        
        self.dtc_table.setSortingEnabled(True)
    
    def populate_filter_combos(self):
        """Populate the filter combo boxes"""
        # Get unique values
        categories = set()
        manufacturers = set()
        systems = set()
        
        for code in self.dtc_codes:
            if 'category_prefix' in code:
                categories.add(code['category_prefix'])
            
            if 'manufacturer' in code:
                manufacturers.add(code['manufacturer'])
            
            if 'system' in code:
                systems.add(code['system'])
        
        # Populate category combo
        self.category_combo.clear()
        self.category_combo.addItem("All Categories")
        for category in sorted(categories):
            self.category_combo.addItem(category)
        
        # Populate manufacturer combo
        self.manufacturer_combo.clear()
        self.manufacturer_combo.addItem("All Manufacturers")
        for manufacturer in sorted(manufacturers):
            self.manufacturer_combo.addItem(manufacturer)
        
        # Populate system combo
        self.system_combo.clear()
        self.system_combo.addItem("All Systems")
        for system in sorted(systems):
            self.system_combo.addItem(system)
    
    def filter_dtcs(self):
        """Filter the DTC codes"""
        # Get filter values
        search_text = self.search_input.text().lower()
        category = self.category_combo.currentText()
        manufacturer = self.manufacturer_combo.currentText()
        system = self.system_combo.currentText()
        severity = self.severity_combo.currentIndex()
        
        # Filter codes
        self.filtered_codes = []
        
        for code in self.dtc_codes:
            # Check search text
            if search_text:
                code_text = code.get('code', '').lower()
                description = code.get('description', '').lower()
                
                if search_text not in code_text and search_text not in description:
                    continue
            
            # Check category
            if category != "All Categories" and code.get('category_prefix') != category:
                continue
            
            # Check manufacturer
            if manufacturer != "All Manufacturers" and code.get('manufacturer') != manufacturer:
                continue
            
            # Check system
            if system != "All Systems" and code.get('system') != system:
                continue
            
            # Check severity
            if severity > 0 and code.get('severity') != severity:
                continue
            
            # Add to filtered codes
            self.filtered_codes.append(code)
        
        # Update table
        self.populate_table()
    
    def clear_search(self):
        """Clear the search"""
        self.search_input.clear()
        self.category_combo.setCurrentIndex(0)
        self.manufacturer_combo.setCurrentIndex(0)
        self.system_combo.setCurrentIndex(0)
        self.severity_combo.setCurrentIndex(0)
        
        # Reset filtered codes
        self.filtered_codes = self.dtc_codes
        
        # Update table
        self.populate_table()
    
    def on_dtc_selected(self, selected, deselected):
        """
        Handle DTC selection
        
        Args:
            selected: The selected items
            deselected: The deselected items
        """
        # Get selected row
        indexes = selected.indexes()
        
        if not indexes:
            return
        
        # Get selected DTC
        row = indexes[0].row()
        code = self.dtc_table.item(row, 0).text()
        
        # Find DTC in filtered codes
        for dtc in self.filtered_codes:
            if dtc.get('code') == code:
                self.selected_dtc = dtc
                self.update_details()
                self.dtc_selected.emit(dtc)
                break
    
    def update_details(self):
        """Update the details panel"""
        if not self.selected_dtc:
            return
        
        # Update overview tab
        self.code_label.setText(self.selected_dtc.get('code', 'N/A'))
        self.description_label.setText(self.selected_dtc.get('description', 'N/A'))
        
        severity = self.selected_dtc.get('severity', 0)
        severity_text = f"{severity} - "
        if severity == 4:
            severity_text += "Critical"
        elif severity == 3:
            severity_text += "High"
        elif severity == 2:
            severity_text += "Medium"
        elif severity == 1:
            severity_text += "Low"
        else:
            severity_text = "N/A"
        
        self.severity_label.setText(severity_text)
        self.manufacturer_label.setText(self.selected_dtc.get('manufacturer', 'Generic'))
        self.system_label.setText(self.selected_dtc.get('system', 'N/A'))
        self.subsystem_label.setText(self.selected_dtc.get('subsystem', 'N/A'))
        
        # Update diagnostic tab
        self.causes_text.setText(self.selected_dtc.get('possible_causes', 'N/A'))
        self.solutions_text.setText(self.selected_dtc.get('solutions', 'N/A'))
        
        # Update technical tab
        self.symptoms_label.setText(self.selected_dtc.get('symptoms', 'N/A'))
        self.testing_procedures_label.setText(self.selected_dtc.get('testing_procedures', 'N/A'))
        self.component_location_label.setText(self.selected_dtc.get('component_location', 'N/A'))
        
        difficulty = self.selected_dtc.get('repair_difficulty', 0)
        difficulty_text = f"{difficulty} - "
        if difficulty == 5:
            difficulty_text += "Very Difficult"
        elif difficulty == 4:
            difficulty_text += "Difficult"
        elif difficulty == 3:
            difficulty_text += "Moderate"
        elif difficulty == 2:
            difficulty_text += "Easy"
        elif difficulty == 1:
            difficulty_text += "Very Easy"
        else:
            difficulty_text = "N/A"
        
        self.repair_difficulty_label.setText(difficulty_text)
        self.repair_cost_label.setText(self.selected_dtc.get('repair_cost', 'N/A'))
        self.repair_time_label.setText(self.selected_dtc.get('repair_time', 'N/A'))
        self.tools_required_label.setText(self.selected_dtc.get('tools_required', 'N/A'))
        
        # Update related tab
        self.related_codes_text.setText(self.selected_dtc.get('related_codes', 'N/A'))
        self.applicable_models_text.setText(self.selected_dtc.get('applicable_models', 'N/A'))
    
    def on_read_live_data(self):
        """Handle read live data button click"""
        if not self.selected_dtc:
            return
        
        # This would normally trigger a signal to read live data related to the DTC
        # For now, we'll just show a message
        QMessageBox.information(self, "Read Live Data", 
                               f"Reading live data related to {self.selected_dtc.get('code')}")
    
    def on_clear_dtc(self):
        """Handle clear DTC button click"""
        if not self.selected_dtc:
            return
        
        # This would normally trigger a signal to clear the DTC
        # For now, we'll just show a message
        QMessageBox.information(self, "Clear DTC", 
                               f"Clearing DTC {self.selected_dtc.get('code')}")
    
    def on_freeze_frame(self):
        """Handle freeze frame button click"""
        if not self.selected_dtc:
            return
        
        # This would normally trigger a signal to read freeze frame data
        # For now, we'll just show a message
        QMessageBox.information(self, "Freeze Frame Data", 
                               f"Reading freeze frame data for {self.selected_dtc.get('code')}")
