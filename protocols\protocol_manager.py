#!/usr/bin/env python3
"""
Protocol Manager
This module provides a manager for selecting and initializing protocols.
"""

import logging
import time
from enum import Enum, auto

from protocols.protocol_handler import ProtocolHandler, BaseProtocol, ProtocolType, SecurityLevel, SecurityAccessStatus, ProtocolError, CommunicationError

logger = logging.getLogger("protocol.manager")

class ConnectionMethod(Enum):
    """Connection methods"""
    AUTO = auto()  # Automatic detection
    MANUAL = auto()  # Manual selection

class ProtocolManager:
    """Protocol manager"""
    
    def __init__(self, interface=None):
        """
        Initialize the protocol manager
        
        Args:
            interface: The hardware interface to use
        """
        self.interface = interface
        self.protocol_handler = ProtocolHandler(interface)
        self.current_protocol = None
        self.current_protocol_name = None
        self.vehicle_info = {}
    
    def connect(self, method=ConnectionMethod.AUTO, protocol_name=None, make=None, model=None, year=None):
        """
        Connect to the vehicle
        
        Args:
            method (ConnectionMethod): The connection method
            protocol_name (str): The protocol name (for manual connection)
            make (str): The vehicle make (for manual connection)
            model (str): The vehicle model (for manual connection)
            year (int): The vehicle year (for manual connection)
            
        Returns:
            bool: True if connected, False otherwise
        """
        if not self.interface:
            logger.error("No interface provided")
            return False
        
        try:
            if method == ConnectionMethod.AUTO:
                # Automatic detection
                return self._connect_auto()
            elif method == ConnectionMethod.MANUAL:
                # Manual selection
                return self._connect_manual(protocol_name, make, model, year)
            else:
                logger.error(f"Invalid connection method: {method}")
                return False
        except Exception as e:
            logger.error(f"Error connecting to vehicle: {e}")
            return False
    
    def disconnect(self):
        """
        Disconnect from the vehicle
        
        Returns:
            bool: True if disconnected, False otherwise
        """
        try:
            if self.current_protocol:
                # Disconnect using the current protocol
                if self.current_protocol.disconnect():
                    self.current_protocol = None
                    self.current_protocol_name = None
                    logger.info("Disconnected from vehicle")
                    return True
                else:
                    logger.error("Failed to disconnect from vehicle")
                    return False
            
            return True
        except Exception as e:
            logger.error(f"Error disconnecting from vehicle: {e}")
            return False
    
    def _connect_auto(self):
        """
        Connect to the vehicle using automatic detection
        
        Returns:
            bool: True if connected, False otherwise
        """
        try:
            # Detect protocol
            protocol_name = self.protocol_handler.detect_protocol(self.interface)
            
            if not protocol_name:
                logger.error("Failed to detect protocol")
                return False
            
            # Create connection
            protocol = self.protocol_handler.create_connection(protocol_name, self.interface)
            
            if not protocol:
                logger.error(f"Failed to connect using protocol: {protocol_name}")
                return False
            
            self.current_protocol = protocol
            self.current_protocol_name = protocol_name
            
            # Get vehicle information
            self._get_vehicle_info()
            
            logger.info(f"Connected to vehicle using protocol: {protocol_name}")
            return True
        except Exception as e:
            logger.error(f"Error connecting to vehicle: {e}")
            return False
    
    def _connect_manual(self, protocol_name=None, make=None, model=None, year=None):
        """
        Connect to the vehicle using manual selection
        
        Args:
            protocol_name (str): The protocol name
            make (str): The vehicle make
            model (str): The vehicle model
            year (int): The vehicle year
            
        Returns:
            bool: True if connected, False otherwise
        """
        try:
            if protocol_name:
                # Connect using the specified protocol
                protocol = self.protocol_handler.create_connection(protocol_name, self.interface)
                
                if not protocol:
                    logger.error(f"Failed to connect using protocol: {protocol_name}")
                    return False
                
                self.current_protocol = protocol
                self.current_protocol_name = protocol_name
            elif make and year:
                # Connect using the appropriate protocol for the vehicle
                protocol = self.protocol_handler.get_protocol_for_vehicle(make, model, year)
                
                if not protocol:
                    logger.error(f"Failed to find protocol for vehicle: {make} {model} {year}")
                    return False
                
                # Set the interface
                protocol.interface = self.interface
                
                # Connect
                if not protocol.connect():
                    logger.error(f"Failed to connect using protocol for vehicle: {make} {model} {year}")
                    return False
                
                self.current_protocol = protocol
                self.current_protocol_name = protocol.get_protocol_info().get('name')
            else:
                logger.error("Insufficient information for manual connection")
                return False
            
            # Get vehicle information
            self._get_vehicle_info()
            
            logger.info(f"Connected to vehicle using protocol: {self.current_protocol_name}")
            return True
        except Exception as e:
            logger.error(f"Error connecting to vehicle: {e}")
            return False
    
    def _get_vehicle_info(self):
        """
        Get vehicle information
        
        Returns:
            dict: Vehicle information
        """
        try:
            if not self.current_protocol:
                return {}
            
            # Get VIN
            vin = self.current_protocol.get_vin()
            
            if vin:
                self.vehicle_info['vin'] = vin
                
                # Parse VIN
                self._parse_vin(vin)
            
            # Get ECU information
            ecu_info = self.current_protocol.get_ecu_info()
            
            if ecu_info:
                self.vehicle_info['ecu_info'] = ecu_info
            
            return self.vehicle_info
        except Exception as e:
            logger.error(f"Error getting vehicle information: {e}")
            return {}
    
    def _parse_vin(self, vin):
        """
        Parse VIN
        
        Args:
            vin (str): The VIN
            
        Returns:
            dict: Parsed VIN information
        """
        try:
            if not vin or len(vin) != 17:
                return {}
            
            # World Manufacturer Identifier (WMI)
            wmi = vin[:3]
            
            # Vehicle Descriptor Section (VDS)
            vds = vin[3:9]
            
            # Vehicle Identifier Section (VIS)
            vis = vin[9:]
            
            # Model year
            year_code = vin[9]
            year_map = {
                'A': 2010, 'B': 2011, 'C': 2012, 'D': 2013, 'E': 2014,
                'F': 2015, 'G': 2016, 'H': 2017, 'J': 2018, 'K': 2019,
                'L': 2020, 'M': 2021, 'N': 2022, 'P': 2023, 'R': 2024,
                'S': 2025, 'T': 2026, 'V': 2027, 'W': 2028, 'X': 2029,
                'Y': 2030, '1': 2001, '2': 2002, '3': 2003, '4': 2004,
                '5': 2005, '6': 2006, '7': 2007, '8': 2008, '9': 2009,
                '0': 2000
            }
            year = year_map.get(year_code, 0)
            
            # Manufacturer
            manufacturer_map = {
                'WBA': 'BMW',
                'WBS': 'BMW M',
                'WBX': 'BMW',
                '4US': 'BMW',
                'JHM': 'Honda',
                'JHG': 'Honda',
                '1HG': 'Honda',
                '2HG': 'Honda',
                '3HG': 'Honda',
                'SHH': 'Honda',
                'JHD': 'Acura',
                '19X': 'Acura',
                '2HK': 'Honda',
                'JN1': 'Nissan',
                'JN8': 'Nissan',
                '1N4': 'Nissan',
                '1N6': 'Nissan',
                '3N1': 'Nissan',
                '5N1': 'Nissan',
                'JF1': 'Subaru',
                'JF2': 'Subaru',
                '4S3': 'Subaru',
                '4S4': 'Subaru',
                'JA3': 'Mitsubishi',
                'JA4': 'Mitsubishi',
                'JMB': 'Mitsubishi',
                'KL5': 'Suzuki',
                'JS2': 'Suzuki',
                'KNA': 'Kia',
                'KND': 'Kia',
                '5XX': 'Kia',
                'KMH': 'Hyundai',
                'KMT': 'Hyundai',
                '5NP': 'Hyundai',
                'WDD': 'Mercedes-Benz',
                'WDB': 'Mercedes-Benz',
                '4JG': 'Mercedes-Benz',
                'WDC': 'Mercedes-Benz',
                'WMW': 'Mini',
                'WP0': 'Porsche',
                'WP1': 'Porsche',
                'WVW': 'Volkswagen',
                '3VW': 'Volkswagen',
                'WVG': 'Volkswagen',
                'WAU': 'Audi',
                'WA1': 'Audi',
                'YV1': 'Volvo',
                'YV4': 'Volvo',
                'SAL': 'Land Rover',
                'SAJ': 'Jaguar',
                '1FA': 'Ford',
                '1FB': 'Ford',
                '1FM': 'Ford',
                '1FT': 'Ford',
                '1ZV': 'Lincoln',
                '2LM': 'Lincoln',
                '5LM': 'Lincoln',
                '1G1': 'Chevrolet',
                '1GC': 'Chevrolet',
                '1GD': 'Chevrolet',
                '1G6': 'Cadillac',
                '1GT': 'GMC',
                '1G2': 'Pontiac',
                '1G3': 'Oldsmobile',
                '1G4': 'Buick',
                '1C3': 'Chrysler',
                '1C4': 'Chrysler',
                '1C6': 'Chrysler',
                '2C3': 'Chrysler',
                '2C4': 'Chrysler',
                '1D7': 'Dodge',
                '1D8': 'Dodge',
                '2D3': 'Dodge',
                '2D4': 'Dodge',
                '1J4': 'Jeep',
                '1J8': 'Jeep',
                '1C6': 'RAM',
                '3C6': 'RAM',
                '5YJ': 'Tesla',
                '7SA': 'Tesla'
            }
            
            manufacturer = manufacturer_map.get(wmi, 'Unknown')
            
            # Store information
            self.vehicle_info['wmi'] = wmi
            self.vehicle_info['vds'] = vds
            self.vehicle_info['vis'] = vis
            self.vehicle_info['year'] = year
            self.vehicle_info['manufacturer'] = manufacturer
            
            return self.vehicle_info
        except Exception as e:
            logger.error(f"Error parsing VIN: {e}")
            return {}
    
    def read_dtc(self):
        """
        Read diagnostic trouble codes
        
        Returns:
            list: List of DTCs
        """
        try:
            if not self.current_protocol:
                logger.error("Not connected to vehicle")
                return []
            
            return self.current_protocol.read_dtc()
        except Exception as e:
            logger.error(f"Error reading DTCs: {e}")
            return []
    
    def clear_dtc(self):
        """
        Clear diagnostic trouble codes
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.current_protocol:
                logger.error("Not connected to vehicle")
                return False
            
            return self.current_protocol.clear_dtc()
        except Exception as e:
            logger.error(f"Error clearing DTCs: {e}")
            return False
    
    def read_data(self, pid):
        """
        Read data from the vehicle
        
        Args:
            pid: The parameter ID to read
            
        Returns:
            The data read from the vehicle
        """
        try:
            if not self.current_protocol:
                logger.error("Not connected to vehicle")
                return None
            
            return self.current_protocol.read_data(pid)
        except Exception as e:
            logger.error(f"Error reading data: {e}")
            return None
    
    def write_data(self, pid, data):
        """
        Write data to the vehicle
        
        Args:
            pid: The parameter ID to write
            data: The data to write
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.current_protocol:
                logger.error("Not connected to vehicle")
                return False
            
            return self.current_protocol.write_data(pid, data)
        except Exception as e:
            logger.error(f"Error writing data: {e}")
            return False
    
    def get_protocol_info(self):
        """
        Get information about the current protocol
        
        Returns:
            dict: Protocol information
        """
        try:
            if not self.current_protocol:
                return {}
            
            return self.current_protocol.get_protocol_info()
        except Exception as e:
            logger.error(f"Error getting protocol information: {e}")
            return {}
    
    def request_security_access(self, level):
        """
        Request security access
        
        Args:
            level (SecurityLevel): The security level to request
            
        Returns:
            bool: True if access granted, False otherwise
        """
        try:
            if not self.current_protocol:
                logger.error("Not connected to vehicle")
                return False
            
            return self.current_protocol.request_security_access(level)
        except Exception as e:
            logger.error(f"Error requesting security access: {e}")
            return False
    
    def get_supported_protocols(self):
        """
        Get a list of supported protocols
        
        Returns:
            dict: Dictionary of supported protocols
        """
        try:
            return {
                'standard': self.protocol_handler.get_all_standard_protocols(),
                'manufacturer': self.protocol_handler.get_all_manufacturer_protocols(),
                'manufacturers': self.protocol_handler.get_all_manufacturers()
            }
        except Exception as e:
            logger.error(f"Error getting supported protocols: {e}")
            return {}
    
    def get_vehicle_info(self):
        """
        Get vehicle information
        
        Returns:
            dict: Vehicle information
        """
        return self.vehicle_info
