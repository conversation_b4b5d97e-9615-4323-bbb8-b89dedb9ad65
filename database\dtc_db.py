"""
DTC Database Module
Provides access to Diagnostic Trouble Codes database
"""

import os
import json
import sqlite3
import logging
import time
from sqlalchemy import create_engine, Column, Integer, String, Text, ForeignKey, Float, Boolean, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship, backref
from datetime import datetime

from database.dtc_scraper import DTCScraper

# Set up logging
logger = logging.getLogger("database.dtc_db")

Base = declarative_base()

class DTCCategory(Base):
    """DTC category model"""
    __tablename__ = 'dtc_categories'

    id = Column(Integer, primary_key=True)
    code_prefix = Column(String, nullable=False)
    name = Column(String, nullable=False)
    description = Column(Text)
    codes = relationship("DTCCode", back_populates="category", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<DTCCategory(code_prefix='{self.code_prefix}', name='{self.name}')>"

class DTCCode(Base):
    """DTC code model"""
    __tablename__ = 'dtc_codes'

    id = Column(Integer, primary_key=True)
    code = Column(String, nullable=False, unique=True)
    description = Column(Text, nullable=False)
    possible_causes = Column(Text)
    solutions = Column(Text)
    severity = Column(Integer)  # 1-5, with 5 being most severe
    category_id = Column(Integer, ForeignKey('dtc_categories.id'), nullable=False)
    category = relationship("DTCCategory", back_populates="codes")

    # Enhanced information
    symptoms = Column(Text)  # Observable symptoms when this code is present
    testing_procedures = Column(Text)  # Step-by-step testing procedures
    component_location = Column(Text)  # Physical location of the affected component
    wiring_diagram = Column(Text)  # Reference to wiring diagram (file path or URL)
    repair_difficulty = Column(Integer)  # 1-5, with 5 being most difficult
    repair_cost = Column(String)  # Estimated repair cost range (e.g., "$50-$100")
    repair_time = Column(String)  # Estimated repair time (e.g., "1-2 hours")
    tools_required = Column(Text)  # Tools required for diagnosis and repair
    related_codes = Column(Text)  # Comma-separated list of related DTC codes
    applicable_models = Column(Text)  # Models this code is commonly found on
    tsb_references = Column(Text)  # Technical Service Bulletin references

    # Manufacturer-specific information
    manufacturer = Column(String)  # Specific manufacturer if applicable
    model_years = Column(String)  # Applicable model years (e.g., "2010-2015")
    system = Column(String)  # Affected system (e.g., "Engine Control", "Transmission")
    subsystem = Column(String)  # Affected subsystem (e.g., "Fuel Injection", "Shift Control")

    # Additional resources
    video_url = Column(String)  # URL to a video explaining the diagnosis/repair
    image_url = Column(String)  # URL to an image showing the component/location

    def __repr__(self):
        return f"<DTCCode(code='{self.code}', description='{self.description[:30]}...')>"


class DTCSystem(Base):
    """Vehicle system model for organizing DTCs"""
    __tablename__ = 'dtc_systems'

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False)
    description = Column(Text)
    parent_id = Column(Integer, ForeignKey('dtc_systems.id'))
    children = relationship("DTCSystem",
                           backref=backref("parent", remote_side=[id]),
                           cascade="all, delete-orphan")

    def __repr__(self):
        return f"<DTCSystem(name='{self.name}')>"


class DTCManufacturer(Base):
    """Manufacturer model for organizing manufacturer-specific DTCs"""
    __tablename__ = 'dtc_manufacturers'

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False, unique=True)
    code_prefix = Column(String)  # Manufacturer-specific code prefix
    description = Column(Text)

    def __repr__(self):
        return f"<DTCManufacturer(name='{self.name}', code_prefix='{self.code_prefix}')>"

class DTCDatabase:
    """DTC database manager"""

    def __init__(self, db_path='database/dtc.db'):
        """
        Initialize the DTC database

        Args:
            db_path (str): Path to the SQLite database file
        """
        self.db_path = db_path
        self.engine = create_engine(f'sqlite:///{db_path}')
        self.Session = sessionmaker(bind=self.engine)

        # Create tables if they don't exist
        Base.metadata.create_all(self.engine)

        # Load sample data if database is empty
        self._load_sample_data_if_empty()

    def _load_sample_data_if_empty(self):
        """Load sample DTC data if the database is empty"""
        session = self.Session()

        # Check if database is empty
        if session.query(DTCCategory).count() == 0:
            # Create categories
            categories = [
                DTCCategory(code_prefix='P0', name='Powertrain Generic',
                           description='Generic (SAE) Powertrain Diagnostic Trouble Codes'),
                DTCCategory(code_prefix='P1', name='Powertrain Manufacturer Specific',
                           description='Manufacturer-Specific Powertrain Diagnostic Trouble Codes'),
                DTCCategory(code_prefix='P2', name='Powertrain Generic',
                           description='Generic (SAE) Powertrain Diagnostic Trouble Codes'),
                DTCCategory(code_prefix='P3', name='Powertrain Generic',
                           description='Generic (SAE) Powertrain Diagnostic Trouble Codes'),
                DTCCategory(code_prefix='B0', name='Body Generic',
                           description='Generic (SAE) Body Diagnostic Trouble Codes'),
                DTCCategory(code_prefix='B1', name='Body Manufacturer Specific',
                           description='Manufacturer-Specific Body Diagnostic Trouble Codes'),
                DTCCategory(code_prefix='C0', name='Chassis Generic',
                           description='Generic (SAE) Chassis Diagnostic Trouble Codes'),
                DTCCategory(code_prefix='C1', name='Chassis Manufacturer Specific',
                           description='Manufacturer-Specific Chassis Diagnostic Trouble Codes'),
                DTCCategory(code_prefix='U0', name='Network Generic',
                           description='Generic (SAE) Network Diagnostic Trouble Codes'),
                DTCCategory(code_prefix='U1', name='Network Manufacturer Specific',
                           description='Manufacturer-Specific Network Diagnostic Trouble Codes')
            ]

            # Add manufacturer-specific categories
            manufacturer_categories = [
                # Toyota
                DTCCategory(code_prefix='P1T', name='Toyota Powertrain',
                           description='Toyota-Specific Powertrain Diagnostic Trouble Codes'),
                DTCCategory(code_prefix='B1T', name='Toyota Body',
                           description='Toyota-Specific Body Diagnostic Trouble Codes'),
                DTCCategory(code_prefix='C1T', name='Toyota Chassis',
                           description='Toyota-Specific Chassis Diagnostic Trouble Codes'),
                DTCCategory(code_prefix='U1T', name='Toyota Network',
                           description='Toyota-Specific Network Diagnostic Trouble Codes'),

                # Honda
                DTCCategory(code_prefix='P1H', name='Honda Powertrain',
                           description='Honda-Specific Powertrain Diagnostic Trouble Codes'),
                DTCCategory(code_prefix='B1H', name='Honda Body',
                           description='Honda-Specific Body Diagnostic Trouble Codes'),
                DTCCategory(code_prefix='C1H', name='Honda Chassis',
                           description='Honda-Specific Chassis Diagnostic Trouble Codes'),
                DTCCategory(code_prefix='U1H', name='Honda Network',
                           description='Honda-Specific Network Diagnostic Trouble Codes'),

                # Ford
                DTCCategory(code_prefix='P1F', name='Ford Powertrain',
                           description='Ford-Specific Powertrain Diagnostic Trouble Codes'),
                DTCCategory(code_prefix='B1F', name='Ford Body',
                           description='Ford-Specific Body Diagnostic Trouble Codes'),
                DTCCategory(code_prefix='C1F', name='Ford Chassis',
                           description='Ford-Specific Chassis Diagnostic Trouble Codes'),
                DTCCategory(code_prefix='U1F', name='Ford Network',
                           description='Ford-Specific Network Diagnostic Trouble Codes'),

                # BMW
                DTCCategory(code_prefix='P1B', name='BMW Powertrain',
                           description='BMW-Specific Powertrain Diagnostic Trouble Codes'),
                DTCCategory(code_prefix='B1B', name='BMW Body',
                           description='BMW-Specific Body Diagnostic Trouble Codes'),
                DTCCategory(code_prefix='C1B', name='BMW Chassis',
                           description='BMW-Specific Chassis Diagnostic Trouble Codes'),
                DTCCategory(code_prefix='U1B', name='BMW Network',
                           description='BMW-Specific Network Diagnostic Trouble Codes'),

                # Mercedes
                DTCCategory(code_prefix='P1M', name='Mercedes Powertrain',
                           description='Mercedes-Specific Powertrain Diagnostic Trouble Codes'),
                DTCCategory(code_prefix='B1M', name='Mercedes Body',
                           description='Mercedes-Specific Body Diagnostic Trouble Codes'),
                DTCCategory(code_prefix='C1M', name='Mercedes Chassis',
                           description='Mercedes-Specific Chassis Diagnostic Trouble Codes'),
                DTCCategory(code_prefix='U1M', name='Mercedes Network',
                           description='Mercedes-Specific Network Diagnostic Trouble Codes')
            ]

            # Add all categories
            categories.extend(manufacturer_categories)
            for category in categories:
                session.add(category)

            session.commit()

            # Initialize manufacturers
            self._initialize_manufacturers(session)

            # Initialize systems
            self._initialize_systems(session)

            # Load initial sample codes
            self._load_initial_sample_codes(session)

            # Import additional codes from external files if available
            self._import_codes_from_files(session)

        session.close()

    def _initialize_manufacturers(self, session):
        """Initialize manufacturer data"""
        manufacturers = [
            # Asian manufacturers
            DTCManufacturer(name='Toyota', code_prefix='T',
                           description='Toyota Motor Corporation'),
            DTCManufacturer(name='Lexus', code_prefix='T',
                           description='Lexus, luxury division of Toyota'),
            DTCManufacturer(name='Honda', code_prefix='H',
                           description='Honda Motor Co., Ltd.'),
            DTCManufacturer(name='Acura', code_prefix='H',
                           description='Acura, luxury division of Honda'),
            DTCManufacturer(name='Nissan', code_prefix='N',
                           description='Nissan Motor Corporation'),
            DTCManufacturer(name='Infiniti', code_prefix='N',
                           description='Infiniti, luxury division of Nissan'),
            DTCManufacturer(name='Mazda', code_prefix='M',
                           description='Mazda Motor Corporation'),
            DTCManufacturer(name='Subaru', code_prefix='S',
                           description='Subaru Corporation'),
            DTCManufacturer(name='Mitsubishi', code_prefix='MM',
                           description='Mitsubishi Motors'),
            DTCManufacturer(name='Hyundai', code_prefix='HY',
                           description='Hyundai Motor Company'),
            DTCManufacturer(name='Kia', code_prefix='K',
                           description='Kia Corporation'),

            # European manufacturers
            DTCManufacturer(name='BMW', code_prefix='B',
                           description='Bayerische Motoren Werke AG'),
            DTCManufacturer(name='Mini', code_prefix='B',
                           description='Mini, owned by BMW'),
            DTCManufacturer(name='Mercedes-Benz', code_prefix='MB',
                           description='Mercedes-Benz Group'),
            DTCManufacturer(name='Volkswagen', code_prefix='VW',
                           description='Volkswagen Group'),
            DTCManufacturer(name='Audi', code_prefix='A',
                           description='Audi AG, part of Volkswagen Group'),
            DTCManufacturer(name='Porsche', code_prefix='P',
                           description='Porsche AG, part of Volkswagen Group'),
            DTCManufacturer(name='Volvo', code_prefix='V',
                           description='Volvo Cars'),
            DTCManufacturer(name='Jaguar', code_prefix='J',
                           description='Jaguar Land Rover'),
            DTCManufacturer(name='Land Rover', code_prefix='LR',
                           description='Jaguar Land Rover'),

            # American manufacturers
            DTCManufacturer(name='Ford', code_prefix='F',
                           description='Ford Motor Company'),
            DTCManufacturer(name='Lincoln', code_prefix='L',
                           description='Lincoln, luxury division of Ford'),
            DTCManufacturer(name='General Motors', code_prefix='GM',
                           description='General Motors Company'),
            DTCManufacturer(name='Chevrolet', code_prefix='C',
                           description='Chevrolet, division of General Motors'),
            DTCManufacturer(name='Buick', code_prefix='BU',
                           description='Buick, division of General Motors'),
            DTCManufacturer(name='Cadillac', code_prefix='CA',
                           description='Cadillac, division of General Motors'),
            DTCManufacturer(name='GMC', code_prefix='G',
                           description='GMC, division of General Motors'),
            DTCManufacturer(name='Chrysler', code_prefix='CH',
                           description='Chrysler, part of Stellantis'),
            DTCManufacturer(name='Dodge', code_prefix='D',
                           description='Dodge, part of Stellantis'),
            DTCManufacturer(name='Jeep', code_prefix='J',
                           description='Jeep, part of Stellantis'),
            DTCManufacturer(name='RAM', code_prefix='R',
                           description='RAM, part of Stellantis'),
            DTCManufacturer(name='Tesla', code_prefix='TE',
                           description='Tesla, Inc.')
        ]

        for manufacturer in manufacturers:
            session.add(manufacturer)

        session.commit()

    def _initialize_systems(self, session):
        """Initialize system data"""
        # Main systems
        engine_system = DTCSystem(name='Engine',
                                 description='Engine control and management system')
        transmission_system = DTCSystem(name='Transmission',
                                       description='Transmission control and management system')
        body_system = DTCSystem(name='Body',
                               description='Body electrical and electronic systems')
        chassis_system = DTCSystem(name='Chassis',
                                  description='Chassis control systems')
        network_system = DTCSystem(name='Network',
                                  description='Vehicle communication networks')
        hvac_system = DTCSystem(name='HVAC',
                               description='Heating, ventilation, and air conditioning')
        safety_system = DTCSystem(name='Safety',
                                 description='Safety systems')

        # Add main systems
        session.add_all([
            engine_system, transmission_system, body_system,
            chassis_system, network_system, hvac_system, safety_system
        ])
        session.flush()

        # Engine subsystems
        engine_subsystems = [
            DTCSystem(name='Fuel System',
                     description='Fuel delivery and control system',
                     parent_id=engine_system.id),
            DTCSystem(name='Air Intake',
                     description='Air intake and management system',
                     parent_id=engine_system.id),
            DTCSystem(name='Ignition',
                     description='Ignition control system',
                     parent_id=engine_system.id),
            DTCSystem(name='Emissions',
                     description='Emissions control system',
                     parent_id=engine_system.id),
            DTCSystem(name='Cooling',
                     description='Engine cooling system',
                     parent_id=engine_system.id),
            DTCSystem(name='Turbocharger/Supercharger',
                     description='Forced induction system',
                     parent_id=engine_system.id)
        ]

        # Transmission subsystems
        transmission_subsystems = [
            DTCSystem(name='Automatic Transmission',
                     description='Automatic transmission control',
                     parent_id=transmission_system.id),
            DTCSystem(name='Manual Transmission',
                     description='Manual transmission components',
                     parent_id=transmission_system.id),
            DTCSystem(name='Clutch',
                     description='Clutch control system',
                     parent_id=transmission_system.id),
            DTCSystem(name='Transfer Case',
                     description='Transfer case control system',
                     parent_id=transmission_system.id),
            DTCSystem(name='Differential',
                     description='Differential control system',
                     parent_id=transmission_system.id)
        ]

        # Chassis subsystems
        chassis_subsystems = [
            DTCSystem(name='ABS',
                     description='Anti-lock Braking System',
                     parent_id=chassis_system.id),
            DTCSystem(name='Traction Control',
                     description='Traction control system',
                     parent_id=chassis_system.id),
            DTCSystem(name='Stability Control',
                     description='Electronic stability control system',
                     parent_id=chassis_system.id),
            DTCSystem(name='Steering',
                     description='Power steering system',
                     parent_id=chassis_system.id),
            DTCSystem(name='Suspension',
                     description='Electronic suspension system',
                     parent_id=chassis_system.id)
        ]

        # Safety subsystems
        safety_subsystems = [
            DTCSystem(name='Airbags',
                     description='Airbag control system',
                     parent_id=safety_system.id),
            DTCSystem(name='Seat Belts',
                     description='Seat belt control system',
                     parent_id=safety_system.id),
            DTCSystem(name='Collision Avoidance',
                     description='Collision avoidance systems',
                     parent_id=safety_system.id),
            DTCSystem(name='Lane Assist',
                     description='Lane keeping and departure warning systems',
                     parent_id=safety_system.id),
            DTCSystem(name='Adaptive Cruise',
                     description='Adaptive cruise control system',
                     parent_id=safety_system.id)
        ]

        # Add all subsystems
        session.add_all(engine_subsystems)
        session.add_all(transmission_subsystems)
        session.add_all(chassis_subsystems)
        session.add_all(safety_subsystems)

        session.commit()

    def _load_initial_sample_codes(self, session):
        """Load initial sample DTC codes"""
        # Sample DTC codes
        sample_codes = [
            # Powertrain codes (P)
            {'code': 'P0100', 'description': 'Mass or Volume Air Flow Circuit Malfunction',
             'possible_causes': 'Faulty MAF sensor, wiring issues, air leaks',
             'solutions': 'Check wiring, clean/replace MAF sensor, check for air leaks',
             'severity': 3, 'category_prefix': 'P0'},
            {'code': 'P0101', 'description': 'Mass or Volume Air Flow Circuit Range/Performance Problem',
             'possible_causes': 'Dirty MAF sensor, air leaks, clogged air filter',
             'solutions': 'Clean MAF sensor, replace air filter, check for vacuum leaks',
             'severity': 2, 'category_prefix': 'P0'},
            {'code': 'P0102', 'description': 'Mass or Volume Air Flow Circuit Low Input',
             'possible_causes': 'Short to ground in MAF circuit, faulty MAF sensor',
             'solutions': 'Check wiring for shorts, replace MAF sensor if necessary',
             'severity': 3, 'category_prefix': 'P0'},
            {'code': 'P0171', 'description': 'System Too Lean (Bank 1)',
             'possible_causes': 'Vacuum leaks, faulty fuel injectors, bad MAF sensor, clogged fuel filter',
             'solutions': 'Check for vacuum leaks, test fuel pressure, clean/replace MAF sensor',
             'severity': 3, 'category_prefix': 'P0'},
            {'code': 'P0300', 'description': 'Random/Multiple Cylinder Misfire Detected',
             'possible_causes': 'Faulty spark plugs/wires, bad ignition coil, vacuum leak, low fuel pressure',
             'solutions': 'Check spark plugs and wires, test ignition coils, check fuel pressure',
             'severity': 4, 'category_prefix': 'P0'},
            {'code': 'P0420', 'description': 'Catalyst System Efficiency Below Threshold (Bank 1)',
             'possible_causes': 'Failed catalytic converter, exhaust leaks, bad O2 sensors',
             'solutions': 'Check for exhaust leaks, test O2 sensors, replace catalytic converter if necessary',
             'severity': 3, 'category_prefix': 'P0'},

            # Body codes (B)
            {'code': 'B0001', 'description': 'Driver Frontal Squib 1 Circuit Resistance Low',
             'possible_causes': 'Short in airbag circuit, damaged wiring',
             'solutions': 'Check wiring, replace airbag module if necessary',
             'severity': 5, 'category_prefix': 'B0'},
            {'code': 'B0100', 'description': 'Front Crash Sensor 1 Circuit Malfunction',
             'possible_causes': 'Damaged crash sensor, wiring issues',
             'solutions': 'Check wiring, replace crash sensor',
             'severity': 5, 'category_prefix': 'B0'},

            # Chassis codes (C)
            {'code': 'C0035', 'description': 'Left Front Wheel Speed Circuit Malfunction',
             'possible_causes': 'Faulty wheel speed sensor, damaged wiring, debris on sensor',
             'solutions': 'Clean sensor, check wiring, replace sensor if necessary',
             'severity': 3, 'category_prefix': 'C0'},
            {'code': 'C0040', 'description': 'Right Front Wheel Speed Circuit Malfunction',
             'possible_causes': 'Faulty wheel speed sensor, damaged wiring, debris on sensor',
             'solutions': 'Clean sensor, check wiring, replace sensor if necessary',
             'severity': 3, 'category_prefix': 'C0'},

            # Network codes (U)
            {'code': 'U0001', 'description': 'High Speed CAN Communication Bus',
             'possible_causes': 'CAN bus wiring issues, module failure',
             'solutions': 'Check CAN bus wiring, test modules on the network',
             'severity': 4, 'category_prefix': 'U0'},
            {'code': 'U0100', 'description': 'Lost Communication With ECM/PCM',
             'possible_causes': 'CAN bus wiring issues, ECM failure, power supply issues',
             'solutions': 'Check CAN bus wiring, test ECM power and ground',
             'severity': 4, 'category_prefix': 'U0'},

            # Toyota specific codes
            {'code': 'P1100', 'description': 'BARO Sensor Circuit',
             'possible_causes': 'Faulty BARO sensor, wiring issues',
             'solutions': 'Check wiring, replace BARO sensor if necessary',
             'severity': 3, 'category_prefix': 'P1T'},
            {'code': 'P1120', 'description': 'Accelerator Pedal Position Sensor Circuit',
             'possible_causes': 'Faulty accelerator pedal position sensor, wiring issues',
             'solutions': 'Check wiring, replace accelerator pedal position sensor if necessary',
             'severity': 3, 'category_prefix': 'P1T'},

            # Honda specific codes
            {'code': 'P1106', 'description': 'BARO Sensor Circuit Range/Performance',
             'possible_causes': 'Faulty BARO sensor, wiring issues',
             'solutions': 'Check wiring, replace BARO sensor if necessary',
             'severity': 3, 'category_prefix': 'P1H'},
            {'code': 'P1128', 'description': 'MAP Sensor Signal Higher Than Expected',
             'possible_causes': 'Faulty MAP sensor, wiring issues',
             'solutions': 'Check wiring, replace MAP sensor if necessary',
             'severity': 3, 'category_prefix': 'P1H'},

            # Ford specific codes
            {'code': 'P1131', 'description': 'Lack of HO2S-11 Switch - Sensor Indicates Lean',
             'possible_causes': 'Faulty O2 sensor, wiring issues, vacuum leaks',
             'solutions': 'Check for vacuum leaks, check wiring, replace O2 sensor if necessary',
             'severity': 3, 'category_prefix': 'P1F'},
            {'code': 'P1151', 'description': 'Lack of HO2S-21 Switch - Sensor Indicates Lean',
             'possible_causes': 'Faulty O2 sensor, wiring issues, vacuum leaks',
             'solutions': 'Check for vacuum leaks, check wiring, replace O2 sensor if necessary',
             'severity': 3, 'category_prefix': 'P1F'}
        ]

        # Add sample codes
        for code_data in sample_codes:
            category = session.query(DTCCategory).filter(
                DTCCategory.code_prefix == code_data['category_prefix']
            ).first()

            if category:
                code = DTCCode(
                    code=code_data['code'],
                    description=code_data['description'],
                    possible_causes=code_data['possible_causes'],
                    solutions=code_data['solutions'],
                    severity=code_data['severity'],
                    category_id=category.id
                )
                session.add(code)

        session.commit()

    def _import_codes_from_files(self, session):
        """Import DTC codes from external files"""
        import os
        import json

        # Check if the dtc_codes directory exists
        dtc_dir = os.path.join(os.path.dirname(self.db_path), 'dtc_codes')
        if not os.path.exists(dtc_dir):
            os.makedirs(dtc_dir)
            return

        # Import codes from JSON files
        for filename in os.listdir(dtc_dir):
            if filename.endswith('.json'):
                try:
                    with open(os.path.join(dtc_dir, filename), 'r') as f:
                        codes_data = json.load(f)

                        for code_data in codes_data:
                            # Find the category
                            category = session.query(DTCCategory).filter(
                                DTCCategory.code_prefix == code_data.get('category_prefix')
                            ).first()

                            if category:
                                # Check if code already exists
                                existing_code = session.query(DTCCode).filter(
                                    DTCCode.code == code_data.get('code')
                                ).first()

                                if not existing_code:
                                    code = DTCCode(
                                        code=code_data.get('code'),
                                        description=code_data.get('description', ''),
                                        possible_causes=code_data.get('possible_causes', ''),
                                        solutions=code_data.get('solutions', ''),
                                        severity=code_data.get('severity', 3),
                                        category_id=category.id
                                    )
                                    session.add(code)

                        session.commit()
                except Exception as e:
                    print(f"Error importing codes from {filename}: {e}")
                    session.rollback()

    def get_dtc_info(self, code):
        """
        Get information about a specific DTC code

        Args:
            code (str): The DTC code

        Returns:
            dict: Information about the DTC code or None if not found
        """
        session = self.Session()

        dtc = session.query(DTCCode).filter(DTCCode.code == code).first()

        if not dtc:
            session.close()
            return None

        # Convert to dictionary
        dtc_dict = {
            'code': dtc.code,
            'description': dtc.description,
            'possible_causes': dtc.possible_causes,
            'solutions': dtc.solutions,
            'severity': dtc.severity,
            'category': dtc.category.name,

            # Enhanced information
            'symptoms': dtc.symptoms,
            'testing_procedures': dtc.testing_procedures,
            'component_location': dtc.component_location,
            'wiring_diagram': dtc.wiring_diagram,
            'repair_difficulty': dtc.repair_difficulty,
            'repair_cost': dtc.repair_cost,
            'repair_time': dtc.repair_time,
            'tools_required': dtc.tools_required,
            'related_codes': dtc.related_codes,
            'applicable_models': dtc.applicable_models,
            'tsb_references': dtc.tsb_references,

            # Manufacturer-specific information
            'manufacturer': dtc.manufacturer,
            'model_years': dtc.model_years,
            'system': dtc.system,
            'subsystem': dtc.subsystem,

            # Additional resources
            'video_url': dtc.video_url,
            'image_url': dtc.image_url
        }

        session.close()
        return dtc_dict

    def search_dtc_codes(self, search_term, manufacturer=None, system=None, severity=None):
        """
        Search for DTC codes with advanced filtering

        Args:
            search_term (str): The search term
            manufacturer (str, optional): Filter by manufacturer
            system (str, optional): Filter by system
            severity (int, optional): Filter by minimum severity level

        Returns:
            list: List of matching DTC codes
        """
        session = self.Session()

        # Start with base query
        query = session.query(DTCCode)

        # Apply search term filter
        if search_term:
            query = query.filter(
                (DTCCode.code.like(f'%{search_term}%')) |
                (DTCCode.description.like(f'%{search_term}%')) |
                (DTCCode.possible_causes.like(f'%{search_term}%')) |
                (DTCCode.symptoms.like(f'%{search_term}%')) |
                (DTCCode.applicable_models.like(f'%{search_term}%'))
            )

        # Apply manufacturer filter
        if manufacturer:
            query = query.filter(DTCCode.manufacturer == manufacturer)

        # Apply system filter
        if system:
            query = query.filter(DTCCode.system == system)

        # Apply severity filter
        if severity:
            query = query.filter(DTCCode.severity >= severity)

        # Execute query
        dtcs = query.all()

        # Convert to list of dictionaries
        dtc_list = []
        for dtc in dtcs:
            dtc_list.append({
                'code': dtc.code,
                'description': dtc.description,
                'severity': dtc.severity,
                'category': dtc.category.name,
                'manufacturer': dtc.manufacturer,
                'system': dtc.system,
                'subsystem': dtc.subsystem,
                'applicable_models': dtc.applicable_models,
                'repair_difficulty': dtc.repair_difficulty,
                'repair_cost': dtc.repair_cost
            })

        session.close()
        return dtc_list

    def get_all_categories(self):
        """
        Get all DTC categories

        Returns:
            list: List of DTC categories
        """
        session = self.Session()

        categories = session.query(DTCCategory).all()

        # Convert to list of dictionaries
        category_list = []
        for category in categories:
            category_list.append({
                'code_prefix': category.code_prefix,
                'name': category.name,
                'description': category.description
            })

        session.close()
        return category_list

    def get_codes_by_category(self, category_prefix):
        """
        Get DTC codes by category

        Args:
            category_prefix (str): The category prefix (e.g., 'P0', 'B1')

        Returns:
            list: List of DTC codes in the category
        """
        session = self.Session()

        category = session.query(DTCCategory).filter(
            DTCCategory.code_prefix == category_prefix
        ).first()

        if not category:
            session.close()
            return []

        # Get codes in the category
        dtcs = session.query(DTCCode).filter(
            DTCCode.category_id == category.id
        ).all()

        # Convert to list of dictionaries
        dtc_list = []
        for dtc in dtcs:
            dtc_list.append({
                'code': dtc.code,
                'description': dtc.description,
                'severity': dtc.severity,
                'manufacturer': dtc.manufacturer,
                'system': dtc.system,
                'subsystem': dtc.subsystem
            })

        session.close()
        return dtc_list

    def get_all_categories(self):
        """
        Get all categories

        Returns:
            list: List of categories
        """
        session = self.Session()

        categories = session.query(DTCCategory).all()

        # Convert to list of dictionaries
        category_list = []
        for category in categories:
            category_list.append({
                'id': category.id,
                'code_prefix': category.code_prefix,
                'name': category.name,
                'description': category.description
            })

        session.close()
        return category_list

    def get_all_manufacturers(self):
        """
        Get all manufacturers

        Returns:
            list: List of manufacturers
        """
        session = self.Session()

        manufacturers = session.query(DTCManufacturer).all()

        # Convert to list of dictionaries
        manufacturer_list = []
        for manufacturer in manufacturers:
            manufacturer_list.append({
                'id': manufacturer.id,
                'name': manufacturer.name,
                'code_prefix': manufacturer.code_prefix,
                'description': manufacturer.description
            })

        session.close()
        return manufacturer_list

    def get_all_systems(self):
        """
        Get all systems

        Returns:
            list: List of systems
        """
        session = self.Session()

        systems = session.query(DTCSystem).all()

        # Convert to list of dictionaries
        system_list = []
        for system in systems:
            system_list.append({
                'id': system.id,
                'name': system.name,
                'description': system.description,
                'parent_id': system.parent_id
            })

        session.close()
        return system_list

    def get_codes_by_manufacturer(self, manufacturer_name):
        """
        Get DTC codes by manufacturer

        Args:
            manufacturer_name (str): The manufacturer name

        Returns:
            list: List of DTC codes for the manufacturer
        """
        session = self.Session()

        # Get codes for the manufacturer
        dtcs = session.query(DTCCode).filter(
            DTCCode.manufacturer == manufacturer_name
        ).all()

        # Convert to list of dictionaries
        dtc_list = []
        for dtc in dtcs:
            dtc_list.append({
                'code': dtc.code,
                'description': dtc.description,
                'severity': dtc.severity,
                'category': dtc.category.name,
                'system': dtc.system,
                'subsystem': dtc.subsystem
            })

        session.close()
        return dtc_list

    def get_codes_by_system(self, system_name):
        """
        Get DTC codes by system

        Args:
            system_name (str): The system name

        Returns:
            list: List of DTC codes for the system
        """
        session = self.Session()

        # Get codes for the system
        dtcs = session.query(DTCCode).filter(
            DTCCode.system == system_name
        ).all()

        # Convert to list of dictionaries
        dtc_list = []
        for dtc in dtcs:
            dtc_list.append({
                'code': dtc.code,
                'description': dtc.description,
                'severity': dtc.severity,
                'category': dtc.category.name,
                'manufacturer': dtc.manufacturer,
                'subsystem': dtc.subsystem
            })

        session.close()
        return dtc_list

    def update_dtc_database_from_web(self, scrape_all=False):
        """
        Update the DTC database with data from web sources

        Args:
            scrape_all (bool): Whether to scrape all sources (default: False)

        Returns:
            int: Number of codes added/updated
        """
        try:
            logger.info("Updating DTC database from web sources")

            # Create scraper
            scraper = DTCScraper(output_dir=os.path.join(os.path.dirname(self.db_path), 'dtc_codes'))

            # Scrape data
            if scrape_all:
                codes_data = scraper.scrape_all()
            else:
                # Just scrape OBD codes
                codes_data = scraper.scrape_obd_codes()

            # Import codes into database
            count = self._import_codes_from_dict(codes_data)

            logger.info(f"Added/updated {count} DTC codes")
            return count
        except Exception as e:
            logger.error(f"Error updating DTC database from web: {e}")
            return 0

    def _import_codes_from_dict(self, codes_data):
        """
        Import DTC codes from a dictionary

        Args:
            codes_data (dict): Dictionary of DTC codes

        Returns:
            int: Number of codes added/updated
        """
        session = self.Session()
        count = 0

        try:
            for code, data in codes_data.items():
                # Determine category
                category_prefix = code[0:2]  # P0, B1, etc.

                # Find the category
                category = session.query(DTCCategory).filter(
                    DTCCategory.code_prefix == category_prefix
                ).first()

                if not category:
                    # Try to find a manufacturer-specific category
                    if 'manufacturer' in data and data['manufacturer']:
                        manufacturer = data['manufacturer']
                        category = session.query(DTCCategory).filter(
                            DTCCategory.name.like(f"%{manufacturer}%")
                        ).first()

                if not category:
                    # Use a generic category
                    if code.startswith('P'):
                        category = session.query(DTCCategory).filter(
                            DTCCategory.code_prefix == 'P0'
                        ).first()
                    elif code.startswith('B'):
                        category = session.query(DTCCategory).filter(
                            DTCCategory.code_prefix == 'B0'
                        ).first()
                    elif code.startswith('C'):
                        category = session.query(DTCCategory).filter(
                            DTCCategory.code_prefix == 'C0'
                        ).first()
                    elif code.startswith('U'):
                        category = session.query(DTCCategory).filter(
                            DTCCategory.code_prefix == 'U0'
                        ).first()

                if not category:
                    # Skip if no category found
                    continue

                # Check if code already exists
                existing_code = session.query(DTCCode).filter(
                    DTCCode.code == code
                ).first()

                if existing_code:
                    # Update existing code
                    if 'description' in data and data['description']:
                        existing_code.description = data['description']

                    if 'possible_causes' in data and data['possible_causes']:
                        if isinstance(data['possible_causes'], list):
                            existing_code.possible_causes = '\n'.join(data['possible_causes'])
                        else:
                            existing_code.possible_causes = data['possible_causes']

                    if 'manufacturer' in data and data['manufacturer']:
                        existing_code.manufacturer = data['manufacturer']

                    if 'severity' in data and data['severity']:
                        existing_code.severity = data['severity']

                    if 'symptoms' in data and data['symptoms']:
                        existing_code.symptoms = data['symptoms']

                    if 'testing_procedures' in data and data['testing_procedures']:
                        existing_code.testing_procedures = data['testing_procedures']

                    if 'component_location' in data and data['component_location']:
                        existing_code.component_location = data['component_location']

                    if 'repair_difficulty' in data and data['repair_difficulty']:
                        existing_code.repair_difficulty = data['repair_difficulty']

                    if 'repair_cost' in data and data['repair_cost']:
                        existing_code.repair_cost = data['repair_cost']

                    if 'repair_time' in data and data['repair_time']:
                        existing_code.repair_time = data['repair_time']

                    if 'tools_required' in data and data['tools_required']:
                        existing_code.tools_required = data['tools_required']

                    if 'related_codes' in data and data['related_codes']:
                        existing_code.related_codes = data['related_codes']

                    if 'applicable_models' in data and data['applicable_models']:
                        existing_code.applicable_models = data['applicable_models']

                    if 'tsb_references' in data and data['tsb_references']:
                        existing_code.tsb_references = data['tsb_references']

                    if 'system' in data and data['system']:
                        existing_code.system = data['system']

                    if 'subsystem' in data and data['subsystem']:
                        existing_code.subsystem = data['subsystem']

                    if 'video_url' in data and data['video_url']:
                        existing_code.video_url = data['video_url']

                    if 'image_url' in data and data['image_url']:
                        existing_code.image_url = data['image_url']
                else:
                    # Create new code
                    new_code = DTCCode(
                        code=code,
                        description=data.get('description', ''),
                        category_id=category.id,
                        manufacturer=data.get('manufacturer', ''),
                        severity=data.get('severity', 3)
                    )

                    # Set possible causes
                    if 'possible_causes' in data and data['possible_causes']:
                        if isinstance(data['possible_causes'], list):
                            new_code.possible_causes = '\n'.join(data['possible_causes'])
                        else:
                            new_code.possible_causes = data['possible_causes']

                    # Set other fields
                    if 'symptoms' in data and data['symptoms']:
                        new_code.symptoms = data['symptoms']

                    if 'testing_procedures' in data and data['testing_procedures']:
                        new_code.testing_procedures = data['testing_procedures']

                    if 'component_location' in data and data['component_location']:
                        new_code.component_location = data['component_location']

                    if 'repair_difficulty' in data and data['repair_difficulty']:
                        new_code.repair_difficulty = data['repair_difficulty']

                    if 'repair_cost' in data and data['repair_cost']:
                        new_code.repair_cost = data['repair_cost']

                    if 'repair_time' in data and data['repair_time']:
                        new_code.repair_time = data['repair_time']

                    if 'tools_required' in data and data['tools_required']:
                        new_code.tools_required = data['tools_required']

                    if 'related_codes' in data and data['related_codes']:
                        new_code.related_codes = data['related_codes']

                    if 'applicable_models' in data and data['applicable_models']:
                        new_code.applicable_models = data['applicable_models']

                    if 'tsb_references' in data and data['tsb_references']:
                        new_code.tsb_references = data['tsb_references']

                    if 'system' in data and data['system']:
                        new_code.system = data['system']

                    if 'subsystem' in data and data['subsystem']:
                        new_code.subsystem = data['subsystem']

                    if 'video_url' in data and data['video_url']:
                        new_code.video_url = data['video_url']

                    if 'image_url' in data and data['image_url']:
                        new_code.image_url = data['image_url']

                    session.add(new_code)

                count += 1

            session.commit()
        except Exception as e:
            logger.error(f"Error importing codes: {e}")
            session.rollback()
        finally:
            session.close()

        return count

    def import_dtc_codes_from_file(self, file_path):
        """
        Import DTC codes from a JSON file

        Args:
            file_path (str): Path to the JSON file

        Returns:
            int: Number of codes added/updated
        """
        try:
            logger.info(f"Importing DTC codes from {file_path}")

            # Load codes from file
            with open(file_path, 'r') as f:
                codes_data = json.load(f)

            # Import codes into database
            count = self._import_codes_from_dict(codes_data)

            logger.info(f"Added/updated {count} DTC codes from {file_path}")
            return count
        except Exception as e:
            logger.error(f"Error importing DTC codes from file: {e}")
            return 0

    def export_dtc_codes_to_file(self, file_path):
        """
        Export DTC codes to a JSON file

        Args:
            file_path (str): Path to the JSON file

        Returns:
            int: Number of codes exported
        """
        try:
            logger.info(f"Exporting DTC codes to {file_path}")

            session = self.Session()

            # Get all codes
            dtcs = session.query(DTCCode).all()

            # Convert to dictionary
            codes_data = {}
            for dtc in dtcs:
                codes_data[dtc.code] = {
                    'code': dtc.code,
                    'description': dtc.description,
                    'possible_causes': dtc.possible_causes,
                    'solutions': dtc.solutions,
                    'severity': dtc.severity,
                    'category': dtc.category.name,
                    'manufacturer': dtc.manufacturer,
                    'symptoms': dtc.symptoms,
                    'testing_procedures': dtc.testing_procedures,
                    'component_location': dtc.component_location,
                    'wiring_diagram': dtc.wiring_diagram,
                    'repair_difficulty': dtc.repair_difficulty,
                    'repair_cost': dtc.repair_cost,
                    'repair_time': dtc.repair_time,
                    'tools_required': dtc.tools_required,
                    'related_codes': dtc.related_codes,
                    'applicable_models': dtc.applicable_models,
                    'tsb_references': dtc.tsb_references,
                    'system': dtc.system,
                    'subsystem': dtc.subsystem,
                    'video_url': dtc.video_url,
                    'image_url': dtc.image_url
                }

            session.close()

            # Save to file
            with open(file_path, 'w') as f:
                json.dump(codes_data, f, indent=2)

            logger.info(f"Exported {len(codes_data)} DTC codes to {file_path}")
            return len(codes_data)
        except Exception as e:
            logger.error(f"Error exporting DTC codes to file: {e}")
            return 0

    def get_dtc_statistics(self):
        """
        Get statistics about the DTC database

        Returns:
            dict: Statistics about the DTC database
        """
        try:
            session = self.Session()

            # Get counts
            total_codes = session.query(DTCCode).count()
            total_categories = session.query(DTCCategory).count()
            total_manufacturers = len(set([m.manufacturer for m in session.query(DTCCode.manufacturer).distinct() if m.manufacturer]))
            total_systems = len(set([s.system for s in session.query(DTCCode.system).distinct() if s.system]))

            # Get code counts by category
            category_counts = {}
            for category in session.query(DTCCategory).all():
                count = session.query(DTCCode).filter(DTCCode.category_id == category.id).count()
                category_counts[category.name] = count

            # Get code counts by manufacturer
            manufacturer_counts = {}
            for manufacturer in session.query(DTCCode.manufacturer).distinct():
                if manufacturer.manufacturer:
                    count = session.query(DTCCode).filter(DTCCode.manufacturer == manufacturer.manufacturer).count()
                    manufacturer_counts[manufacturer.manufacturer] = count

            # Get code counts by system
            system_counts = {}
            for system in session.query(DTCCode.system).distinct():
                if system.system:
                    count = session.query(DTCCode).filter(DTCCode.system == system.system).count()
                    system_counts[system.system] = count

            # Get code counts by severity
            severity_counts = {}
            for severity in range(1, 6):
                count = session.query(DTCCode).filter(DTCCode.severity == severity).count()
                severity_counts[severity] = count

            session.close()

            return {
                'total_codes': total_codes,
                'total_categories': total_categories,
                'total_manufacturers': total_manufacturers,
                'total_systems': total_systems,
                'category_counts': category_counts,
                'manufacturer_counts': manufacturer_counts,
                'system_counts': system_counts,
                'severity_counts': severity_counts
            }
        except Exception as e:
            logger.error(f"Error getting DTC statistics: {e}")
            return {}
