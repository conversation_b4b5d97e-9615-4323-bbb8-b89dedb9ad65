"""
Main Window Module
Implements the main application window
"""

import os
import sys
import time
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QTabWidget, QTableWidget, QTableWidgetItem, QHeaderView,
    QMessageBox, QGroupBox, QFormLayout, QLineEdit, QTextEdit, QSplitter,
    QAction, QToolBar, QStatusBar, QDialog, QDialogButtonBox, QCheckBox,
    QProgressBar, QFileDialog, QApplication, QTreeWidget, QTreeWidgetItem,
    QStackedWidget, QSpinBox
)
from PyQt5.QtCore import Qt, QTimer, pyqtSlot
from PyQt5.QtGui import QIcon, QFont, QPixmap

from ui.data_visualization import RealTimeGraph, DashboardWidget
from ui.dtc_viewer import DTCViewer

class MainWindow(QMainWindow):
    """Main application window"""

    def __init__(self, controller):
        """
        Initialize the main window

        Args:
            controller: The application controller
        """
        super().__init__()

        self.controller = controller
        self.connected = False
        self.current_vehicle = None
        self.live_data_timer = QTimer()
        self.live_data_timer.timeout.connect(self.update_live_data)

        self.init_ui()

    def init_ui(self):
        """Initialize the user interface"""
        # Set window properties
        self.setWindowTitle("Vehicle Diagnostics Tool")
        self.setMinimumSize(1024, 768)

        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Create main layout
        main_layout = QVBoxLayout(central_widget)

        # Create connection section
        connection_group = QGroupBox("Connection")
        connection_layout = QHBoxLayout()

        # Port selection
        self.port_combo = QComboBox()
        self.refresh_ports_button = QPushButton("Refresh")
        self.refresh_ports_button.clicked.connect(self.refresh_ports)

        # Connect button
        self.connect_button = QPushButton("Connect")
        self.connect_button.clicked.connect(self.toggle_connection)

        # Protocol selection
        self.protocol_combo = QComboBox()
        self.protocol_combo.addItem("Auto-detect")
        for protocol in self.controller.protocol_handler.get_all_standard_protocols():
            self.protocol_combo.addItem(protocol)

        # Add widgets to connection layout
        connection_layout.addWidget(QLabel("Port:"))
        connection_layout.addWidget(self.port_combo)
        connection_layout.addWidget(self.refresh_ports_button)
        connection_layout.addWidget(QLabel("Protocol:"))
        connection_layout.addWidget(self.protocol_combo)
        connection_layout.addWidget(self.connect_button)
        connection_layout.addStretch()

        connection_group.setLayout(connection_layout)
        main_layout.addWidget(connection_group)

        # Create vehicle selection section
        vehicle_group = QGroupBox("Vehicle Selection")
        vehicle_layout = QHBoxLayout()

        # Make selection
        self.make_combo = QComboBox()
        self.make_combo.currentTextChanged.connect(self.on_make_changed)

        # Model selection
        self.model_combo = QComboBox()
        self.model_combo.currentTextChanged.connect(self.on_model_changed)

        # Year selection
        self.year_combo = QComboBox()

        # Set vehicle button
        self.set_vehicle_button = QPushButton("Set Vehicle")
        self.set_vehicle_button.clicked.connect(self.set_vehicle)

        # Add widgets to vehicle layout
        vehicle_layout.addWidget(QLabel("Make:"))
        vehicle_layout.addWidget(self.make_combo)
        vehicle_layout.addWidget(QLabel("Model:"))
        vehicle_layout.addWidget(self.model_combo)
        vehicle_layout.addWidget(QLabel("Year:"))
        vehicle_layout.addWidget(self.year_combo)
        vehicle_layout.addWidget(self.set_vehicle_button)
        vehicle_layout.addStretch()

        vehicle_group.setLayout(vehicle_layout)
        main_layout.addWidget(vehicle_group)

        # Create tab widget for different functions
        self.tab_widget = QTabWidget()

        # Create tabs
        self.create_vehicle_info_tab()
        self.create_dtc_tab()
        self.create_live_data_tab()
        self.create_programming_tab()
        self.create_dtc_database_tab()
        self.create_data_visualization_tab()
        self.create_settings_tab()

        main_layout.addWidget(self.tab_widget)

        # Create status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Not connected")

        # Initialize ports
        self.refresh_ports()

        # Initialize vehicle makes
        self.refresh_makes()

    def create_vehicle_info_tab(self):
        """Create the vehicle information tab"""
        vehicle_info_tab = QWidget()
        layout = QVBoxLayout(vehicle_info_tab)

        # Vehicle information table
        self.vehicle_info_table = QTableWidget(0, 2)
        self.vehicle_info_table.setHorizontalHeaderLabels(["Property", "Value"])
        self.vehicle_info_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.vehicle_info_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)

        layout.addWidget(self.vehicle_info_table)

        self.tab_widget.addTab(vehicle_info_tab, "Vehicle Info")

    def create_dtc_tab(self):
        """Create the DTC (Diagnostic Trouble Codes) tab"""
        # Create the DTC viewer
        self.dtc_viewer = DTCViewer()

        # Connect signals
        self.dtc_viewer.dtc_selected.connect(self.on_dtc_selected)

        # Add buttons for DTC operations
        button_layout = QHBoxLayout()

        self.read_dtc_button = QPushButton("Read DTCs")
        self.read_dtc_button.clicked.connect(self.read_dtc_codes)

        self.clear_dtc_button = QPushButton("Clear DTCs")
        self.clear_dtc_button.clicked.connect(self.clear_dtc_codes)

        button_layout.addWidget(self.read_dtc_button)
        button_layout.addWidget(self.clear_dtc_button)
        button_layout.addStretch()

        # Create a container widget
        dtc_tab = QWidget()
        layout = QVBoxLayout(dtc_tab)
        layout.addLayout(button_layout)
        layout.addWidget(self.dtc_viewer)

        self.tab_widget.addTab(dtc_tab, "Diagnostic Codes")

    def create_live_data_tab(self):
        """Create the live data tab"""
        live_data_tab = QWidget()
        layout = QVBoxLayout(live_data_tab)

        # Controls for live data
        controls_layout = QHBoxLayout()

        self.pid_combo = QComboBox()
        self.pid_combo.setEditable(True)

        # Add common PIDs
        common_pids = [
            "ENGINE_LOAD", "COOLANT_TEMP", "SHORT_FUEL_TRIM_1", "LONG_FUEL_TRIM_1",
            "INTAKE_PRESSURE", "RPM", "SPEED", "TIMING_ADVANCE", "INTAKE_TEMP",
            "MAF", "THROTTLE_POS", "O2_SENSORS", "FUEL_LEVEL", "DISTANCE_W_MIL",
            "CONTROL_MODULE_VOLTAGE", "AMBIANT_AIR_TEMP", "FUEL_TYPE", "FUEL_RATE"
        ]

        for pid in common_pids:
            self.pid_combo.addItem(pid)

        self.add_pid_button = QPushButton("Add")
        self.add_pid_button.clicked.connect(self.add_live_data_pid)

        self.start_live_data_button = QPushButton("Start")
        self.start_live_data_button.clicked.connect(self.toggle_live_data)

        controls_layout.addWidget(QLabel("PID:"))
        controls_layout.addWidget(self.pid_combo)
        controls_layout.addWidget(self.add_pid_button)
        controls_layout.addWidget(self.start_live_data_button)
        controls_layout.addStretch()

        layout.addLayout(controls_layout)

        # Live data table
        self.live_data_table = QTableWidget(0, 4)
        self.live_data_table.setHorizontalHeaderLabels(["PID", "Value", "Unit", "Time"])
        self.live_data_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.live_data_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.live_data_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.live_data_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)

        layout.addWidget(self.live_data_table)

        self.tab_widget.addTab(live_data_tab, "Live Data")

    def create_programming_tab(self):
        """Create the programming tab"""
        programming_tab = QWidget()
        layout = QVBoxLayout(programming_tab)

        # Programming options
        options_group = QGroupBox("Programming Options")
        options_layout = QFormLayout()

        self.ecu_combo = QComboBox()
        self.ecu_combo.addItems(["Engine Control Module (ECM)", "Transmission Control Module (TCM)",
                                "Body Control Module (BCM)", "Airbag Control Module (ACM)"])

        self.file_path_edit = QLineEdit()
        self.browse_button = QPushButton("Browse")
        self.browse_button.clicked.connect(self.browse_file)

        file_layout = QHBoxLayout()
        file_layout.addWidget(self.file_path_edit)
        file_layout.addWidget(self.browse_button)

        options_layout.addRow("ECU:", self.ecu_combo)
        options_layout.addRow("File:", file_layout)

        options_group.setLayout(options_layout)
        layout.addWidget(options_group)

        # Programming controls
        controls_layout = QHBoxLayout()

        self.read_ecu_button = QPushButton("Read ECU")
        self.read_ecu_button.clicked.connect(self.read_ecu)

        self.write_ecu_button = QPushButton("Write ECU")
        self.write_ecu_button.clicked.connect(self.write_ecu)

        self.verify_ecu_button = QPushButton("Verify")
        self.verify_ecu_button.clicked.connect(self.verify_ecu)

        controls_layout.addWidget(self.read_ecu_button)
        controls_layout.addWidget(self.write_ecu_button)
        controls_layout.addWidget(self.verify_ecu_button)
        controls_layout.addStretch()

        layout.addLayout(controls_layout)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)

        layout.addWidget(self.progress_bar)

        # Log area
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)

        layout.addWidget(self.log_text)

        self.tab_widget.addTab(programming_tab, "ECU Programming")

    def create_dtc_database_tab(self):
        """Create the DTC database tab"""
        dtc_db_tab = QWidget()
        layout = QVBoxLayout(dtc_db_tab)

        # Search section
        search_group = QGroupBox("Search DTC Codes")
        search_layout = QHBoxLayout()

        self.dtc_search_edit = QLineEdit()
        self.dtc_search_edit.setPlaceholderText("Enter code or description...")

        self.dtc_search_button = QPushButton("Search")
        self.dtc_search_button.clicked.connect(self.search_dtc)

        search_layout.addWidget(self.dtc_search_edit)
        search_layout.addWidget(self.dtc_search_button)

        search_group.setLayout(search_layout)
        layout.addWidget(search_group)

        # Categories and codes section
        codes_layout = QHBoxLayout()

        # Categories tree
        categories_group = QGroupBox("Categories")
        categories_layout = QVBoxLayout()

        self.categories_tree = QTreeWidget()
        self.categories_tree.setHeaderLabels(["DTC Categories"])
        self.categories_tree.itemClicked.connect(self.on_category_selected)

        categories_layout.addWidget(self.categories_tree)
        categories_group.setLayout(categories_layout)
        codes_layout.addWidget(categories_group)

        # Codes table
        codes_group = QGroupBox("DTC Codes")
        codes_layout_inner = QVBoxLayout()

        self.dtc_codes_table = QTableWidget(0, 3)
        self.dtc_codes_table.setHorizontalHeaderLabels(["Code", "Description", "Severity"])
        self.dtc_codes_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.dtc_codes_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.dtc_codes_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.dtc_codes_table.itemClicked.connect(self.on_dtc_code_selected)

        codes_layout_inner.addWidget(self.dtc_codes_table)
        codes_group.setLayout(codes_layout_inner)
        codes_layout.addWidget(codes_group)

        layout.addLayout(codes_layout)

        # DTC details section
        details_group = QGroupBox("DTC Details")
        details_layout = QFormLayout()

        self.dtc_code_label = QLabel("")
        self.dtc_description_text = QTextEdit()
        self.dtc_description_text.setReadOnly(True)
        self.dtc_causes_text = QTextEdit()
        self.dtc_causes_text.setReadOnly(True)
        self.dtc_solutions_text = QTextEdit()
        self.dtc_solutions_text.setReadOnly(True)

        details_layout.addRow("Code:", self.dtc_code_label)
        details_layout.addRow("Description:", self.dtc_description_text)
        details_layout.addRow("Possible Causes:", self.dtc_causes_text)
        details_layout.addRow("Solutions:", self.dtc_solutions_text)

        details_group.setLayout(details_layout)
        layout.addWidget(details_group)

        self.tab_widget.addTab(dtc_db_tab, "DTC Database")

        # Load categories
        self.load_dtc_categories()

    def refresh_ports(self):
        """Refresh the list of available ports"""
        self.port_combo.clear()

        ports = self.controller.get_available_ports()
        for port in ports:
            self.port_combo.addItem(f"{port['port']} - {port['description']}", port['port'])

    def refresh_makes(self):
        """Refresh the list of vehicle makes"""
        self.make_combo.clear()

        makes = self.controller.get_vehicle_makes()
        for make in makes:
            self.make_combo.addItem(make)

    def on_make_changed(self, make):
        """
        Handle make selection change

        Args:
            make (str): The selected make
        """
        self.model_combo.clear()

        if not make:
            return

        models = self.controller.get_vehicle_models(make)
        for model in models:
            self.model_combo.addItem(model)

    def on_model_changed(self, model):
        """
        Handle model selection change

        Args:
            model (str): The selected model
        """
        self.year_combo.clear()

        if not model or not self.make_combo.currentText():
            return

        years = self.controller.vehicle_db.get_years(self.make_combo.currentText(), model)
        for year in years:
            self.year_combo.addItem(str(year))

    def toggle_connection(self):
        """Toggle the connection to the vehicle"""
        if self.connected:
            self.controller.disconnect()
            self.connected = False
            self.connect_button.setText("Connect")
            self.status_bar.showMessage("Disconnected")
            self.live_data_timer.stop()
        else:
            port = self.port_combo.currentData()
            if not port:
                QMessageBox.warning(self, "Connection Error", "Please select a port")
                return

            protocol = None
            if self.protocol_combo.currentIndex() > 0:
                protocol = self.protocol_combo.currentText()

            self.status_bar.showMessage("Connecting...")
            self.connected = self.controller.connect_to_vehicle(port, protocol)

            if self.connected:
                self.connect_button.setText("Disconnect")
                self.status_bar.showMessage(f"Connected to {self.controller.obd_interface.device_description}")
            else:
                QMessageBox.warning(self, "Connection Error", "Failed to connect to the vehicle")
                self.status_bar.showMessage("Connection failed")

    def set_vehicle(self):
        """Set the current vehicle"""
        make = self.make_combo.currentText()
        model = self.model_combo.currentText()
        year_text = self.year_combo.currentText()

        if not make or not model or not year_text:
            QMessageBox.warning(self, "Vehicle Selection", "Please select make, model, and year")
            return

        try:
            year = int(year_text)
        except ValueError:
            QMessageBox.warning(self, "Vehicle Selection", "Invalid year")
            return

        success = self.controller.set_current_vehicle(make, model, year)

        if success:
            self.current_vehicle = self.controller.current_vehicle
            self.update_vehicle_info()
            QMessageBox.information(self, "Vehicle Selection", f"Vehicle set to {make} {model} {year}")
        else:
            QMessageBox.warning(self, "Vehicle Selection", "Failed to set vehicle")

    def update_vehicle_info(self):
        """Update the vehicle information display"""
        if not self.current_vehicle:
            return

        # Clear the table
        self.vehicle_info_table.setRowCount(0)

        # Add vehicle information
        for i, (key, value) in enumerate(self.current_vehicle.items()):
            self.vehicle_info_table.insertRow(i)
            self.vehicle_info_table.setItem(i, 0, QTableWidgetItem(key.replace('_', ' ').title()))
            self.vehicle_info_table.setItem(i, 1, QTableWidgetItem(str(value)))

    def on_dtc_selected(self, dtc):
        """
        Handle DTC selection

        Args:
            dtc (dict): The selected DTC
        """
        # Display detailed information about the selected DTC
        if not dtc:
            return

        # Log the selected DTC
        self.status_bar.showMessage(f"Selected DTC: {dtc.get('code')} - {dtc.get('description')}")

        # Here you could perform additional actions when a DTC is selected
        # For example, read freeze frame data or related sensor values

    def read_dtc_codes(self):
        """Read DTC codes from the vehicle"""
        if not self.connected:
            QMessageBox.warning(self, "Not Connected", "Please connect to a vehicle first")
            return

        # Read DTC codes
        dtc_codes = self.controller.read_dtc_codes()

        # Update the DTC viewer
        self.dtc_viewer.set_dtc_codes(dtc_codes)

        # Show message
        self.status_bar.showMessage(f"Found {len(dtc_codes)} DTCs")

        if not dtc_codes:
            QMessageBox.information(self, "DTC Codes", "No DTC codes found")
            return

        # Add DTC codes to the table
        for i, dtc in enumerate(dtc_codes):
            self.dtc_table.insertRow(i)
            self.dtc_table.setItem(i, 0, QTableWidgetItem(dtc["code"]))
            self.dtc_table.setItem(i, 1, QTableWidgetItem(dtc["description"]))

    def clear_dtc_codes(self):
        """Clear DTC codes from the vehicle"""
        if not self.connected:
            QMessageBox.warning(self, "Not Connected", "Please connect to a vehicle first")
            return

        # Confirm with the user
        reply = QMessageBox.question(
            self, "Clear DTC Codes",
            "Are you sure you want to clear all DTC codes?",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # Clear DTC codes
        success = self.controller.clear_dtc_codes()

        if success:
            QMessageBox.information(self, "DTC Codes", "DTC codes cleared successfully")
            # Clear the DTC viewer
            self.dtc_viewer.set_dtc_codes([])
            # Update status
            self.status_bar.showMessage("DTC codes cleared successfully")
        else:
            QMessageBox.warning(self, "DTC Codes", "Failed to clear DTC codes")

    def add_live_data_pid(self):
        """Add a PID to the live data table"""
        pid = self.pid_combo.currentText()

        if not pid:
            return

        # Check if the PID is already in the table
        for i in range(self.live_data_table.rowCount()):
            if self.live_data_table.item(i, 0).text() == pid:
                return

        # Add the PID to the table
        row = self.live_data_table.rowCount()
        self.live_data_table.insertRow(row)
        self.live_data_table.setItem(row, 0, QTableWidgetItem(pid))
        self.live_data_table.setItem(row, 1, QTableWidgetItem(""))
        self.live_data_table.setItem(row, 2, QTableWidgetItem(""))
        self.live_data_table.setItem(row, 3, QTableWidgetItem(""))

    def toggle_live_data(self):
        """Toggle live data collection"""
        if not self.connected:
            QMessageBox.warning(self, "Not Connected", "Please connect to a vehicle first")
            return

        if self.live_data_timer.isActive():
            self.live_data_timer.stop()
            self.start_live_data_button.setText("Start")
        else:
            self.live_data_timer.start(1000)  # Update every second
            self.start_live_data_button.setText("Stop")

    def update_live_data(self):
        """Update live data values"""
        if not self.connected:
            self.live_data_timer.stop()
            self.start_live_data_button.setText("Start")
            return

        # Update each PID in the table
        for i in range(self.live_data_table.rowCount()):
            pid = self.live_data_table.item(i, 0).text()

            # Query the vehicle
            response = self.controller.get_live_data(pid)

            if response:
                self.live_data_table.setItem(i, 1, QTableWidgetItem(str(response["value"])))
                self.live_data_table.setItem(i, 2, QTableWidgetItem(response["unit"]))
                self.live_data_table.setItem(i, 3, QTableWidgetItem(f"{response['time']:.2f} s"))
            else:
                self.live_data_table.setItem(i, 1, QTableWidgetItem("N/A"))
                self.live_data_table.setItem(i, 2, QTableWidgetItem(""))
                self.live_data_table.setItem(i, 3, QTableWidgetItem(""))

    def browse_file(self):
        """Browse for a file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select File", "", "Binary Files (*.bin);;All Files (*)"
        )

        if file_path:
            self.file_path_edit.setText(file_path)

    def read_ecu(self):
        """Read ECU data"""
        if not self.connected:
            QMessageBox.warning(self, "Not Connected", "Please connect to a vehicle first")
            return

        ecu = self.ecu_combo.currentText()

        # This would normally read the ECU data
        # For now, just show a message
        self.log_text.append(f"Reading {ecu}...")

        # Simulate progress
        for i in range(101):
            self.progress_bar.setValue(i)
            QApplication.processEvents()
            time.sleep(0.05)

        self.log_text.append(f"Read {ecu} completed")

        # Ask for save location
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save ECU Data", "", "Binary Files (*.bin);;All Files (*)"
        )

        if file_path:
            self.log_text.append(f"Saved ECU data to {file_path}")

    def write_ecu(self):
        """Write ECU data"""
        if not self.connected:
            QMessageBox.warning(self, "Not Connected", "Please connect to a vehicle first")
            return

        ecu = self.ecu_combo.currentText()
        file_path = self.file_path_edit.text()

        if not file_path:
            QMessageBox.warning(self, "File Required", "Please select a file to write")
            return

        # Confirm with the user
        reply = QMessageBox.question(
            self, "Write ECU",
            f"Are you sure you want to write to the {ecu}?\n\nThis operation can damage your vehicle if the file is incorrect.",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # This would normally write the ECU data
        # For now, just show a message
        self.log_text.append(f"Writing {ecu}...")

        # Simulate progress
        for i in range(101):
            self.progress_bar.setValue(i)
            QApplication.processEvents()
            time.sleep(0.05)

        self.log_text.append(f"Write {ecu} completed")

    def verify_ecu(self):
        """Verify ECU data"""
        if not self.connected:
            QMessageBox.warning(self, "Not Connected", "Please connect to a vehicle first")
            return

        ecu = self.ecu_combo.currentText()
        file_path = self.file_path_edit.text()

        if not file_path:
            QMessageBox.warning(self, "File Required", "Please select a file to verify")
            return

        # This would normally verify the ECU data
        # For now, just show a message
        self.log_text.append(f"Verifying {ecu}...")

        # Simulate progress
        for i in range(101):
            self.progress_bar.setValue(i)
            QApplication.processEvents()
            time.sleep(0.05)

        self.log_text.append(f"Verify {ecu} completed - Checksum OK")

    def load_dtc_categories(self):
        """Load DTC categories into the tree widget"""
        self.categories_tree.clear()

        # Get categories from the database
        categories = self.controller.get_dtc_categories()

        # Create tree items
        powertrain_item = QTreeWidgetItem(["Powertrain Codes (P)"])
        body_item = QTreeWidgetItem(["Body Codes (B)"])
        chassis_item = QTreeWidgetItem(["Chassis Codes (C)"])
        network_item = QTreeWidgetItem(["Network Codes (U)"])

        self.categories_tree.addTopLevelItem(powertrain_item)
        self.categories_tree.addTopLevelItem(body_item)
        self.categories_tree.addTopLevelItem(chassis_item)
        self.categories_tree.addTopLevelItem(network_item)

        # Add categories to the tree
        for category in categories:
            prefix = category["code_prefix"]
            name = category["name"]

            category_item = QTreeWidgetItem([f"{prefix} - {name}"])
            category_item.setData(0, Qt.UserRole, prefix)

            if prefix.startswith("P"):
                powertrain_item.addChild(category_item)
            elif prefix.startswith("B"):
                body_item.addChild(category_item)
            elif prefix.startswith("C"):
                chassis_item.addChild(category_item)
            elif prefix.startswith("U"):
                network_item.addChild(category_item)

        # Expand all items
        self.categories_tree.expandAll()

    def on_category_selected(self, item, column):
        """
        Handle category selection

        Args:
            item (QTreeWidgetItem): The selected item
            column (int): The selected column
        """
        # Check if this is a category item (has data)
        prefix = item.data(0, Qt.UserRole)
        if not prefix:
            return

        # Get codes for the selected category
        codes = self.controller.get_dtc_codes_by_category(prefix)

        # Clear the table
        self.dtc_codes_table.setRowCount(0)

        # Add codes to the table
        for i, code in enumerate(codes):
            self.dtc_codes_table.insertRow(i)
            self.dtc_codes_table.setItem(i, 0, QTableWidgetItem(code["code"]))
            self.dtc_codes_table.setItem(i, 1, QTableWidgetItem(code["description"]))

            severity = code.get("severity", 0)
            severity_item = QTableWidgetItem(str(severity))
            if severity >= 4:
                severity_item.setBackground(Qt.red)
            elif severity >= 3:
                severity_item.setBackground(Qt.yellow)
            else:
                severity_item.setBackground(Qt.green)

            self.dtc_codes_table.setItem(i, 2, severity_item)

    def on_dtc_code_selected(self, item):
        """
        Handle DTC code selection

        Args:
            item (QTableWidgetItem): The selected item
        """
        row = item.row()
        code = self.dtc_codes_table.item(row, 0).text()

        # Get detailed information about the code
        dtc_info = self.controller.dtc_db.get_dtc_info(code)

        if dtc_info:
            self.dtc_code_label.setText(dtc_info["code"])
            self.dtc_description_text.setText(dtc_info["description"])
            self.dtc_causes_text.setText(dtc_info.get("possible_causes", ""))
            self.dtc_solutions_text.setText(dtc_info.get("solutions", ""))
        else:
            self.dtc_code_label.setText(code)
            self.dtc_description_text.setText(self.dtc_codes_table.item(row, 1).text())
            self.dtc_causes_text.setText("")
            self.dtc_solutions_text.setText("")

    def search_dtc(self):
        """Search for DTC codes"""
        search_term = self.dtc_search_edit.text()

        if not search_term:
            return

        # Search for codes
        codes = self.controller.search_dtc_codes(search_term)

        # Clear the table
        self.dtc_codes_table.setRowCount(0)

        # Add codes to the table
        for i, code in enumerate(codes):
            self.dtc_codes_table.insertRow(i)
            self.dtc_codes_table.setItem(i, 0, QTableWidgetItem(code["code"]))
            self.dtc_codes_table.setItem(i, 1, QTableWidgetItem(code["description"]))

            severity = code.get("severity", 0)
            severity_item = QTableWidgetItem(str(severity))
            if severity >= 4:
                severity_item.setBackground(Qt.red)
            elif severity >= 3:
                severity_item.setBackground(Qt.yellow)
            else:
                severity_item.setBackground(Qt.green)

            self.dtc_codes_table.setItem(i, 2, severity_item)

    def create_data_visualization_tab(self):
        """Create the data visualization tab"""
        data_viz_tab = QWidget()
        layout = QVBoxLayout(data_viz_tab)

        # Create tab selector
        viz_type_layout = QHBoxLayout()

        self.viz_type_combo = QComboBox()
        self.viz_type_combo.addItems(["Real-time Graph", "Dashboard"])
        self.viz_type_combo.currentIndexChanged.connect(self.switch_visualization)

        viz_type_layout.addWidget(QLabel("Visualization Type:"))
        viz_type_layout.addWidget(self.viz_type_combo)
        viz_type_layout.addStretch()

        layout.addLayout(viz_type_layout)

        # Create stacked widget for different visualizations
        self.viz_stack = QStackedWidget()

        # Create real-time graph
        self.real_time_graph = RealTimeGraph()
        self.viz_stack.addWidget(self.real_time_graph)

        # Create dashboard
        self.dashboard = DashboardWidget()
        self.viz_stack.addWidget(self.dashboard)

        # Add default gauges to dashboard
        self.dashboard.add_gauge("rpm", "Engine RPM", 0, 8000, "RPM", "#d62728")
        self.dashboard.add_gauge("speed", "Vehicle Speed", 0, 240, "km/h", "#1f77b4")
        self.dashboard.add_gauge("temp", "Coolant Temp", 0, 150, "°C", "#ff7f0e")
        self.dashboard.add_gauge("load", "Engine Load", 0, 100, "%", "#2ca02c")

        layout.addWidget(self.viz_stack)

        self.tab_widget.addTab(data_viz_tab, "Data Visualization")

        # Set up data sources for real-time graph
        self.setup_data_sources()

    def setup_data_sources(self):
        """Set up data sources for visualization"""
        # Common PIDs
        data_sources = {
            "ENGINE_LOAD": "Engine Load (%)",
            "COOLANT_TEMP": "Coolant Temperature (°C)",
            "SHORT_FUEL_TRIM_1": "Short Term Fuel Trim - Bank 1 (%)",
            "LONG_FUEL_TRIM_1": "Long Term Fuel Trim - Bank 1 (%)",
            "INTAKE_PRESSURE": "Intake Manifold Pressure (kPa)",
            "RPM": "Engine RPM",
            "SPEED": "Vehicle Speed (km/h)",
            "TIMING_ADVANCE": "Timing Advance (°)",
            "INTAKE_TEMP": "Intake Air Temperature (°C)",
            "MAF": "Mass Air Flow (g/s)",
            "THROTTLE_POS": "Throttle Position (%)",
            "O2_SENSORS": "O2 Sensors Present",
            "FUEL_LEVEL": "Fuel Level (%)",
            "DISTANCE_W_MIL": "Distance with MIL on (km)",
            "CONTROL_MODULE_VOLTAGE": "Control Module Voltage (V)",
            "AMBIANT_AIR_TEMP": "Ambient Air Temperature (°C)",
            "FUEL_TYPE": "Fuel Type",
            "FUEL_RATE": "Fuel Rate (L/h)"
        }

        self.real_time_graph.set_data_sources(data_sources)

    def switch_visualization(self, index):
        """
        Switch between visualization types

        Args:
            index (int): The index of the selected visualization
        """
        self.viz_stack.setCurrentIndex(index)

        # If switching to real-time graph, stop the live data timer
        if index == 0 and self.live_data_timer.isActive():
            self.live_data_timer.stop()
            self.start_live_data_button.setText("Start")

    def update_live_data(self):
        """Update live data values"""
        if not self.connected:
            self.live_data_timer.stop()
            self.start_live_data_button.setText("Start")
            return

        # Update each PID in the table
        for i in range(self.live_data_table.rowCount()):
            pid = self.live_data_table.item(i, 0).text()

            # Query the vehicle
            response = self.controller.get_live_data(pid)

            if response:
                value = response["value"]
                self.live_data_table.setItem(i, 1, QTableWidgetItem(str(value)))
                self.live_data_table.setItem(i, 2, QTableWidgetItem(response["unit"]))
                self.live_data_table.setItem(i, 3, QTableWidgetItem(f"{response['time']:.2f} s"))

                # Update real-time graph if it's active
                if self.viz_type_combo.currentIndex() == 0:
                    self.real_time_graph.add_data_point(pid, float(value))

                # Update dashboard if it's active
                if self.viz_type_combo.currentIndex() == 1:
                    if pid == "RPM":
                        self.dashboard.set_gauge_value("rpm", float(value))
                    elif pid == "SPEED":
                        self.dashboard.set_gauge_value("speed", float(value))
                    elif pid == "COOLANT_TEMP":
                        self.dashboard.set_gauge_value("temp", float(value))
                    elif pid == "ENGINE_LOAD":
                        self.dashboard.set_gauge_value("load", float(value))
            else:
                self.live_data_table.setItem(i, 1, QTableWidgetItem("N/A"))
                self.live_data_table.setItem(i, 2, QTableWidgetItem(""))
                self.live_data_table.setItem(i, 3, QTableWidgetItem(""))

    def create_settings_tab(self):
        """Create the settings tab"""
        settings_tab = QWidget()
        layout = QVBoxLayout(settings_tab)

        # Create tabs for different settings
        settings_tabs = QTabWidget()

        # General settings
        general_widget = QWidget()
        general_layout = QFormLayout(general_widget)

        # Interface settings
        self.auto_connect_check = QCheckBox("Auto-connect on startup")
        self.auto_detect_protocol_check = QCheckBox("Auto-detect protocol")
        self.auto_detect_protocol_check.setChecked(True)

        # Logging settings
        self.enable_logging_check = QCheckBox("Enable logging")
        self.enable_logging_check.setChecked(True)

        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"])
        self.log_level_combo.setCurrentIndex(1)  # INFO

        self.log_path_edit = QLineEdit("logs/app.log")
        self.log_path_button = QPushButton("Browse")
        self.log_path_button.clicked.connect(self.browse_log_path)

        log_path_layout = QHBoxLayout()
        log_path_layout.addWidget(self.log_path_edit)
        log_path_layout.addWidget(self.log_path_button)

        # Add widgets to general layout
        general_layout.addRow("Auto-connect on startup:", self.auto_connect_check)
        general_layout.addRow("Auto-detect protocol:", self.auto_detect_protocol_check)
        general_layout.addRow("Enable logging:", self.enable_logging_check)
        general_layout.addRow("Log level:", self.log_level_combo)
        general_layout.addRow("Log file:", log_path_layout)

        # Add general tab
        settings_tabs.addTab(general_widget, "General")

        # Interface settings
        interface_widget = QWidget()
        interface_layout = QFormLayout(interface_widget)

        # Serial settings
        self.serial_baudrate_combo = QComboBox()
        self.serial_baudrate_combo.addItems(["9600", "19200", "38400", "57600", "115200", "230400", "500000"])
        self.serial_baudrate_combo.setCurrentIndex(4)  # 115200

        self.serial_timeout_spin = QSpinBox()
        self.serial_timeout_spin.setRange(1, 60)
        self.serial_timeout_spin.setValue(5)
        self.serial_timeout_spin.setSuffix(" seconds")

        # CAN settings
        self.can_baudrate_combo = QComboBox()
        self.can_baudrate_combo.addItems(["125000", "250000", "500000", "1000000"])
        self.can_baudrate_combo.setCurrentIndex(2)  # 500000

        self.can_extended_id_check = QCheckBox("Use extended IDs")

        # Add widgets to interface layout
        interface_layout.addRow("Serial baudrate:", self.serial_baudrate_combo)
        interface_layout.addRow("Serial timeout:", self.serial_timeout_spin)
        interface_layout.addRow("CAN baudrate:", self.can_baudrate_combo)
        interface_layout.addRow("CAN extended IDs:", self.can_extended_id_check)

        # Add interface tab
        settings_tabs.addTab(interface_widget, "Interface")

        # Display settings
        display_widget = QWidget()
        display_layout = QFormLayout(display_widget)

        # Units
        self.units_combo = QComboBox()
        self.units_combo.addItems(["Metric", "Imperial"])
        self.units_combo.setCurrentIndex(0)  # Metric

        # Theme
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["System", "Light", "Dark"])
        self.theme_combo.setCurrentIndex(0)  # System

        # Font size
        self.font_size_combo = QComboBox()
        self.font_size_combo.addItems(["Small", "Medium", "Large"])
        self.font_size_combo.setCurrentIndex(1)  # Medium

        # Add widgets to display layout
        display_layout.addRow("Units:", self.units_combo)
        display_layout.addRow("Theme:", self.theme_combo)
        display_layout.addRow("Font size:", self.font_size_combo)

        # Add display tab
        settings_tabs.addTab(display_widget, "Display")

        # Advanced settings
        advanced_widget = QWidget()
        advanced_layout = QFormLayout(advanced_widget)

        # Debug mode
        self.debug_mode_check = QCheckBox("Enable debug mode")

        # Protocol timeout
        self.protocol_timeout_spin = QSpinBox()
        self.protocol_timeout_spin.setRange(1, 60)
        self.protocol_timeout_spin.setValue(10)
        self.protocol_timeout_spin.setSuffix(" seconds")

        # Response timeout
        self.response_timeout_spin = QSpinBox()
        self.response_timeout_spin.setRange(1, 60)
        self.response_timeout_spin.setValue(5)
        self.response_timeout_spin.setSuffix(" seconds")

        # Add widgets to advanced layout
        advanced_layout.addRow("Debug mode:", self.debug_mode_check)
        advanced_layout.addRow("Protocol timeout:", self.protocol_timeout_spin)
        advanced_layout.addRow("Response timeout:", self.response_timeout_spin)

        # Add advanced tab
        settings_tabs.addTab(advanced_widget, "Advanced")

        # Add tabs to layout
        layout.addWidget(settings_tabs)

        # Add buttons
        button_layout = QHBoxLayout()

        self.save_settings_button = QPushButton("Save Settings")
        self.save_settings_button.clicked.connect(self.save_settings)

        self.reset_settings_button = QPushButton("Reset to Defaults")
        self.reset_settings_button.clicked.connect(self.reset_settings)

        button_layout.addWidget(self.save_settings_button)
        button_layout.addWidget(self.reset_settings_button)
        button_layout.addStretch()

        layout.addLayout(button_layout)

        self.tab_widget.addTab(settings_tab, "Settings")

    def browse_log_path(self):
        """Browse for a log file path"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Select Log File", self.log_path_edit.text(), "Log Files (*.log);;All Files (*)"
        )

        if file_path:
            self.log_path_edit.setText(file_path)

    def save_settings(self):
        """Save settings"""
        # This would normally save settings to a configuration file
        # For now, we'll just show a message
        QMessageBox.information(self, "Settings", "Settings saved successfully")

        # Apply settings
        self.apply_settings()

    def reset_settings(self):
        """Reset settings to defaults"""
        # Confirm with the user
        reply = QMessageBox.question(
            self, "Reset Settings",
            "Are you sure you want to reset all settings to defaults?",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # Reset settings
        self.auto_connect_check.setChecked(False)
        self.auto_detect_protocol_check.setChecked(True)
        self.enable_logging_check.setChecked(True)
        self.log_level_combo.setCurrentIndex(1)  # INFO
        self.log_path_edit.setText("logs/app.log")

        self.serial_baudrate_combo.setCurrentIndex(4)  # 115200
        self.serial_timeout_spin.setValue(5)
        self.can_baudrate_combo.setCurrentIndex(2)  # 500000
        self.can_extended_id_check.setChecked(False)

        self.units_combo.setCurrentIndex(0)  # Metric
        self.theme_combo.setCurrentIndex(0)  # System
        self.font_size_combo.setCurrentIndex(1)  # Medium

        self.debug_mode_check.setChecked(False)
        self.protocol_timeout_spin.setValue(10)
        self.response_timeout_spin.setValue(5)

        # Show message
        QMessageBox.information(self, "Settings", "Settings reset to defaults")

        # Apply settings
        self.apply_settings()

    def apply_settings(self):
        """Apply settings"""
        # This would normally apply settings to the application
        # For now, we'll just update the status bar
        self.status_bar.showMessage("Settings applied")
