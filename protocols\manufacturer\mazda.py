#!/usr/bin/env python3
"""
Mazda Protocol Handler
This module provides the protocol handler for Mazda-specific protocols.
"""

import logging
import time
import struct
from enum import Enum, auto
from typing import Dict, List, Optional, Any, Tuple, Union

from protocols.protocol_handler import BaseProtocol, ProtocolType, SecurityLevel, SecurityAccessStatus, ProtocolError, CommunicationError
from protocols.standard.iso15765 import ISO15765Protocol, CANFrameType
from protocols.standard.iso14230 import ISO14230Protocol

logger = logging.getLogger("protocol.Mazda")

class MazdaVariant(Enum):
    """Mazda protocol variants"""
    KWP = "KWP"        # KWP2000 protocol (older vehicles)
    CAN_TP = "CAN-TP"  # CAN Transport Protocol (mid-age)
    UDS = "UDS"        # UDS over CAN (newer vehicles)

class MazdaProtocol(BaseProtocol):
    """Mazda protocol handler"""
    
    def __init__(self, interface=None, variant=MazdaVariant.UDS, baudrate=500000):
        """
        Initialize the Mazda protocol handler
        
        Args:
            interface: The hardware interface to use
            variant (MazdaVariant): The protocol variant (default: UDS)
            baudrate (int): The CAN baudrate (default: 500000)
        """
        super().__init__(interface)
        
        if isinstance(variant, str):
            try:
                self.variant = MazdaVariant(variant)
            except ValueError:
                self.variant = MazdaVariant.UDS
                logger.warning(f"Invalid Mazda protocol variant: {variant}, using UDS")
        else:
            self.variant = variant
        
        self.baudrate = baudrate
        
        # Initialize the appropriate protocol handler based on the variant
        if self.variant == MazdaVariant.UDS:
            # UDS over ISO15765 (CAN)
            self.protocol = ISO15765Protocol(
                interface=interface,
                baudrate=baudrate,
                frame_type=CANFrameType.STANDARD,
                tx_id=0x7E0,  # Mazda-specific diagnostic CAN ID
                rx_id=0x7E8   # Mazda-specific response CAN ID
            )
        elif self.variant == MazdaVariant.CAN_TP:
            # CAN-TP protocol
            self.protocol = ISO15765Protocol(
                interface=interface,
                baudrate=baudrate,
                frame_type=CANFrameType.STANDARD,
                tx_id=0x7E0,
                rx_id=0x7E8
            )
        elif self.variant == MazdaVariant.KWP:
            # KWP2000 over K-Line
            self.protocol = ISO14230Protocol(
                interface=interface,
                baudrate=10400,  # Mazda K-Line typically uses 10.4 kbps
                tx_id=0xF1,
                rx_id=0x10
            )
        
        # Mazda-specific PIDs
        self.pid_vin = 0xF190
        self.pid_ecu_info = 0xF18A
        self.pid_software_version = 0xF189
        self.pid_calibration_id = 0xF187
        self.pid_immobilizer_status = 0xF18C
        self.pid_odometer = 0xF15B
        self.pid_key_programming = 0xF1A2
        self.pid_skyactiv_data = 0xF1A3
        
        # Mazda-specific ECUs
        self.ecu_engine = 0x01
        self.ecu_transmission = 0x02
        self.ecu_abs = 0x03
        self.ecu_airbag = 0x04
        self.ecu_instrument_cluster = 0x05
        self.ecu_body_control = 0x06
        self.ecu_climate_control = 0x07
        self.ecu_immobilizer = 0x08
        self.ecu_infotainment = 0x09
        
        # Mazda-specific security access seeds/keys
        self.security_algorithms = {
            SecurityLevel.LEVEL_1: self._calculate_key_level_1,
            SecurityLevel.LEVEL_2: self._calculate_key_level_2,
            SecurityLevel.LEVEL_3: self._calculate_key_level_3,
            SecurityLevel.LEVEL_4: self._calculate_key_level_4,
            SecurityLevel.LEVEL_5: self._calculate_key_level_5
        }
    
    def connect(self):
        """
        Connect to the vehicle
        
        Returns:
            bool: True if connected, False otherwise
        """
        try:
            # Connect using the underlying protocol
            if self.protocol.connect():
                self.connected = True
                logger.info(f"Connected to Mazda vehicle using {self.variant.value} protocol")
                
                # Enter diagnostic session for Mazda
                if self.variant == MazdaVariant.UDS:
                    self.protocol.enter_diagnostic_session(self.protocol.session_extended_diagnostic)
                
                return True
            else:
                logger.error("Failed to connect to Mazda vehicle")
                return False
        except Exception as e:
            logger.error(f"Error connecting to Mazda vehicle: {e}")
            return False
    
    def disconnect(self):
        """
        Disconnect from the vehicle
        
        Returns:
            bool: True if disconnected, False otherwise
        """
        try:
            # Disconnect using the underlying protocol
            if self.protocol.disconnect():
                self.connected = False
                logger.info("Disconnected from Mazda vehicle")
                return True
            else:
                logger.error("Failed to disconnect from Mazda vehicle")
                return False
        except Exception as e:
            logger.error(f"Error disconnecting from Mazda vehicle: {e}")
            return False
    
    def send_command(self, command, response_required=True):
        """
        Send a command to the vehicle
        
        Args:
            command: The command to send
            response_required (bool): Whether a response is required
            
        Returns:
            The response from the vehicle
        """
        try:
            # Send command using the underlying protocol
            return self.protocol.send_command(command, response_required)
        except Exception as e:
            logger.error(f"Error sending command to Mazda vehicle: {e}")
            raise CommunicationError(f"Error sending command to Mazda vehicle: {e}")
    
    def read_dtc(self):
        """
        Read diagnostic trouble codes
        
        Returns:
            list: List of DTCs
        """
        try:
            # Read DTCs using the underlying protocol
            dtcs = self.protocol.read_dtc()
            
            # Add Mazda-specific information to DTCs
            for dtc in dtcs:
                # Add Mazda-specific DTC information if available
                # This would typically come from a Mazda-specific DTC database
                if dtc.get('code', '').startswith('P1'):
                    dtc['manufacturer'] = 'Mazda'
                
                # Add additional Mazda-specific information
                if dtc.get('code') == 'P1345':
                    dtc['description'] = 'VVT System Malfunction'
                    dtc['possible_causes'] = 'VVT solenoid valve malfunction, Oil pressure low, VVT controller damaged'
                elif dtc.get('code') == 'P0126':
                    dtc['description'] = 'Insufficient Coolant Temperature for Stable Operation'
                    dtc['possible_causes'] = 'Thermostat stuck open, Coolant temperature sensor malfunction'
                elif dtc.get('code') == 'P0300':
                    dtc['description'] = 'Random/Multiple Cylinder Misfire Detected'
                    dtc['possible_causes'] = 'Ignition system problem, Fuel system problem, Engine mechanical problem'
            
            return dtcs
        except Exception as e:
            logger.error(f"Error reading DTCs from Mazda vehicle: {e}")
            return []
    
    def clear_dtc(self):
        """
        Clear diagnostic trouble codes
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Clear DTCs using the underlying protocol
            return self.protocol.clear_dtc()
        except Exception as e:
            logger.error(f"Error clearing DTCs from Mazda vehicle: {e}")
            return False
    
    def read_data(self, pid):
        """
        Read data from the vehicle
        
        Args:
            pid: The parameter ID to read
            
        Returns:
            The data read from the vehicle
        """
        try:
            # Read data using the underlying protocol
            data = self.protocol.read_data(pid)
            
            # Process Mazda-specific data
            if pid == self.pid_vin:
                # VIN is already processed by the underlying protocol
                pass
            elif pid == self.pid_ecu_info:
                # Process ECU information
                if data and len(data) >= 8:
                    return {
                        'part_number': ''.join(chr(b) for b in data[:8]),
                        'calibration_id': ''.join(chr(b) for b in data[8:16]) if len(data) >= 16 else ''
                    }
            elif pid == self.pid_software_version:
                # Process software version
                if data and len(data) >= 4:
                    return {
                        'version': ''.join(chr(b) for b in data[:4])
                    }
            elif pid == self.pid_calibration_id:
                # Process calibration ID
                if data and len(data) >= 8:
                    return {
                        'calibration_id': ''.join(chr(b) for b in data[:8])
                    }
            elif pid == self.pid_immobilizer_status:
                # Process immobilizer status
                if data and len(data) >= 1:
                    status = data[0]
                    return {
                        'status': status,
                        'description': self._get_immobilizer_status_description(status)
                    }
            elif pid == self.pid_odometer:
                # Process odometer reading
                if data and len(data) >= 3:
                    # Mazda typically stores odometer as 3 bytes in km
                    odometer_km = (data[0] << 16) | (data[1] << 8) | data[2]
                    return {
                        'odometer_km': odometer_km,
                        'odometer_miles': round(odometer_km * 0.621371, 1)  # Convert to miles
                    }
            elif pid == self.pid_skyactiv_data:
                # Process SkyActiv engine data
                if data and len(data) >= 8:
                    return {
                        'compression_ratio': data[0] / 10.0,
                        'i_stop_status': data[1],
                        'i_eloop_status': data[2],
                        'cylinder_deactivation': data[3] != 0,
                        'engine_efficiency': data[4],
                        'fuel_economy_mode': data[5] != 0
                    }
            
            return data
        except Exception as e:
            logger.error(f"Error reading data from Mazda vehicle: {e}")
            return None
    
    def _get_immobilizer_status_description(self, status):
        """
        Get immobilizer status description
        
        Args:
            status (int): The status code
            
        Returns:
            str: The status description
        """
        status_descriptions = {
            0x00: "Immobilizer disabled",
            0x01: "Immobilizer enabled, vehicle secured",
            0x02: "Immobilizer enabled, key recognized",
            0x03: "Immobilizer learning mode",
            0x04: "Immobilizer malfunction",
            0x05: "Incorrect key detected",
            0x06: "No key detected",
            0x07: "Too many incorrect attempts"
        }
        
        return status_descriptions.get(status, "Unknown status")
    
    def write_data(self, pid, data):
        """
        Write data to the vehicle
        
        Args:
            pid: The parameter ID to write
            data: The data to write
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Write data using the underlying protocol
            return self.protocol.write_data(pid, data)
        except Exception as e:
            logger.error(f"Error writing data to Mazda vehicle: {e}")
            return False
    
    def get_protocol_info(self):
        """
        Get information about the protocol
        
        Returns:
            dict: Protocol information
        """
        protocol_info = self.protocol.get_protocol_info()
        protocol_info.update({
            'name': f'Mazda {self.variant.value}',
            'type': ProtocolType.MANUFACTURER,
            'manufacturer': 'Mazda'
        })
        return protocol_info
    
    def request_security_access(self, level):
        """
        Request security access
        
        Args:
            level (SecurityLevel): The security level to request
            
        Returns:
            bool: True if access granted, False otherwise
        """
        try:
            # Override the key calculation method
            original_calculate_key = self.protocol.calculate_key
            self.protocol.calculate_key = self.calculate_key
            
            # Request security access using the underlying protocol
            result = self.protocol.request_security_access(level)
            
            # Restore the original key calculation method
            self.protocol.calculate_key = original_calculate_key
            
            return result
        except Exception as e:
            logger.error(f"Error requesting security access from Mazda vehicle: {e}")
            return False
    
    def calculate_key(self, seed, level):
        """
        Calculate the key from the seed
        
        Args:
            seed: The seed received from the ECU
            level (SecurityLevel): The security level
            
        Returns:
            The calculated key
        """
        # Use the appropriate algorithm for the security level
        algorithm = self.security_algorithms.get(level, self._calculate_key_default)
        return algorithm(seed)
    
    def _calculate_key_default(self, seed):
        """Default key calculation algorithm"""
        # Simple XOR with a fixed value
        return [b ^ 0x3F for b in seed]
    
    def _calculate_key_level_1(self, seed):
        """Level 1 key calculation algorithm"""
        # Mazda-specific algorithm for level 1
        key = []
        for b in seed:
            key.append((b + 0x23) & 0xFF)
        return key
    
    def _calculate_key_level_2(self, seed):
        """Level 2 key calculation algorithm"""
        # Mazda-specific algorithm for level 2
        key = []
        for b in seed:
            key.append((b ^ 0x45) & 0xFF)
        return key
    
    def _calculate_key_level_3(self, seed):
        """Level 3 key calculation algorithm"""
        # Mazda-specific algorithm for level 3
        key = []
        for b in seed:
            key.append(((b << 2) | (b >> 6)) & 0xFF)
        return key
    
    def _calculate_key_level_4(self, seed):
        """Level 4 key calculation algorithm"""
        # Mazda-specific algorithm for level 4
        key = []
        for b in seed:
            key.append(((b + 0x34) ^ 0x67) & 0xFF)
        return key
    
    def _calculate_key_level_5(self, seed):
        """Level 5 key calculation algorithm"""
        # Mazda-specific algorithm for level 5
        key = []
        prev = 0x23
        for b in seed:
            k = (b ^ prev) & 0xFF
            key.append(k)
            prev = b
        return key
    
    def get_vin(self):
        """
        Get the vehicle identification number
        
        Returns:
            str: The VIN
        """
        try:
            # Get VIN using the underlying protocol
            vin = self.protocol.get_vin()
            
            # Mazda-specific VIN processing
            if vin:
                # Validate Mazda VIN
                if len(vin) == 17 and vin[0] in 'J,1,3,4':
                    return vin
            
            return vin
        except Exception as e:
            logger.error(f"Error getting VIN from Mazda vehicle: {e}")
            return None
    
    def get_ecu_info(self):
        """
        Get information about the ECU
        
        Returns:
            dict: ECU information
        """
        try:
            # Get ECU information using Mazda-specific PID
            ecu_info = self.read_data(self.pid_ecu_info)
            
            if not ecu_info:
                # Try to get basic ECU information
                return self.protocol.get_ecu_info()
            
            return ecu_info
        except Exception as e:
            logger.error(f"Error getting ECU information from Mazda vehicle: {e}")
            return {}
    
    def test_connection(self, connection):
        """
        Test if the connection uses Mazda protocol
        
        Args:
            connection: The connection to test
            
        Returns:
            bool: True if the connection uses Mazda protocol, False otherwise
        """
        try:
            # Test connection using the underlying protocol
            if not self.protocol.test_connection(connection):
                return False
            
            # Try to read a Mazda-specific PID
            self.protocol.interface = connection
            data = self.protocol.read_data(self.pid_ecu_info)
            
            if data:
                return True
            
            return False
        except Exception as e:
            logger.debug(f"Error testing Mazda connection: {e}")
            return False
    
    def program_key(self, key_id):
        """
        Program a key
        
        Args:
            key_id (int): The key ID
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Request security access
            if not self.request_security_access(SecurityLevel.LEVEL_3):
                logger.error("Failed to get security access for key programming")
                return False
            
            # Send key programming command
            command = [0x31, 0x01, self.pid_key_programming & 0xFF, key_id & 0xFF]
            response = self.send_command(command)
            
            # Check response
            if not response or response[0] != 0x71:
                logger.error("Failed to program key")
                return False
            
            logger.info(f"Successfully programmed key with ID {key_id}")
            return True
        except Exception as e:
            logger.error(f"Error programming key: {e}")
            return False
    
    def read_skyactiv_data(self):
        """
        Read SkyActiv engine data
        
        Returns:
            dict: SkyActiv engine data
        """
        try:
            # Read SkyActiv data using Mazda-specific PID
            return self.read_data(self.pid_skyactiv_data)
        except Exception as e:
            logger.error(f"Error reading SkyActiv data: {e}")
            return None
