#!/usr/bin/env python3
"""
Vehicle Diagnostics Application
Main entry point for the application
"""

import sys
from PyQt5.QtWidgets import QApplication
from ui.main_window import MainWindow
from core.app_controller import AppController

def main():
    """Main entry point for the application"""
    app = QApplication(sys.argv)
    app.setApplicationName("Vehicle Diagnostics Tool")
    
    # Initialize the application controller
    controller = AppController()
    
    # Initialize and show the main window
    main_window = MainWindow(controller)
    main_window.show()
    
    # Start the application event loop
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
