"""
OBD Interface Module
Provides interface to OBD hardware adapters (ELM327 and others)
"""

import time
import serial
import serial.tools.list_ports
import obd
from obd import OBDStatus
from hardware.ecu_programmer import ECUProgrammer, ProgrammingMode, ProgrammingStatus

class OBDInterface:
    """Interface to OBD hardware adapters"""

    def __init__(self, protocol_handler):
        """
        Initialize the OBD interface

        Args:
            protocol_handler: The protocol handler to use
        """
        self.protocol_handler = protocol_handler
        self.connection = None
        self.protocol = None
        self.elm_version = None
        self.device_description = None
        self.ecu_programmer = ECUProgrammer(self)

    def get_available_ports(self):
        """
        Get a list of available serial ports

        Returns:
            list: List of available serial ports
        """
        ports = []
        for port in serial.tools.list_ports.comports():
            ports.append({
                'port': port.device,
                'description': port.description,
                'hwid': port.hwid
            })
        return ports

    def connect(self, port, protocol=None):
        """
        Connect to an OBD adapter

        Args:
            port (str): The serial port to connect to
            protocol (str, optional): The protocol to use. If None, auto-detect.

        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            # Disconnect if already connected
            if self.connection is not None:
                self.disconnect()

            # Connect to the OBD adapter
            connection_kwargs = {"fast": False}
            if protocol:
                protocol_id = self.protocol_handler.get_standard_protocol_id(protocol)
                if protocol_id:
                    connection_kwargs["protocol"] = protocol_id

            self.connection = obd.OBD(port, **connection_kwargs)

            # Check if connection was successful
            if self.connection.status() != OBDStatus.CAR_CONNECTED:
                self.disconnect()
                return False

            # Get ELM version and protocol
            self.elm_version = self._get_elm_version()
            self.protocol = self._get_current_protocol()
            self.device_description = f"Connected to {self.elm_version} on {port} using {self.protocol}"

            return True
        except Exception as e:
            print(f"Error connecting to OBD adapter: {e}")
            self.disconnect()
            return False

    def disconnect(self):
        """Disconnect from the OBD adapter"""
        if self.connection is not None:
            self.connection.close()
            self.connection = None
            self.protocol = None
            self.elm_version = None
            self.device_description = None

    def _get_elm_version(self):
        """
        Get the ELM version

        Returns:
            str: The ELM version
        """
        if self.connection is None:
            return None

        response = self.connection.query(obd.commands.ELM_VERSION)
        if response.is_null():
            return "Unknown"
        return response.value

    def _get_current_protocol(self):
        """
        Get the current protocol

        Returns:
            str: The current protocol
        """
        if self.connection is None:
            return None

        return self.connection.protocol_name()

    def read_dtc_codes(self):
        """
        Read Diagnostic Trouble Codes from the vehicle

        Returns:
            list: List of DTC codes
        """
        if self.connection is None:
            return []

        response = self.connection.query(obd.commands.GET_DTC)
        if response.is_null():
            return []

        return [{"code": code, "description": self._get_dtc_description(code)} for code in response.value]

    def clear_dtc_codes(self):
        """
        Clear Diagnostic Trouble Codes from the vehicle

        Returns:
            bool: True if successful, False otherwise
        """
        if self.connection is None:
            return False

        response = self.connection.query(obd.commands.CLEAR_DTC)
        return not response.is_null()

    def query(self, pid):
        """
        Query the vehicle for a specific Parameter ID

        Args:
            pid (str): The Parameter ID to query

        Returns:
            dict: The response data
        """
        if self.connection is None:
            return None

        # Check if the PID is a standard OBD command
        command = None
        for cmd in obd.commands.modes[1]:
            if cmd.name == pid:
                command = cmd
                break

        if command is None:
            # Try to create a custom command
            try:
                command = obd.OBDCommand(pid, "Custom PID", pid, 1, self._decode_raw)
            except:
                return None

        response = self.connection.query(command)
        if response.is_null():
            return None

        return {
            "value": response.value,
            "unit": response.unit,
            "time": response.time
        }

    def _decode_raw(self, messages):
        """
        Decode raw messages

        Args:
            messages: The raw messages

        Returns:
            str: The decoded messages
        """
        result = ""
        for message in messages:
            result += f"{message.raw()}\n"
        return result

    def _get_dtc_description(self, code):
        """
        Get the description for a DTC code

        Args:
            code (str): The DTC code

        Returns:
            str: The description
        """
        # This would normally query a database of DTC codes
        # For now, return a generic description based on the code type

        if not code or len(code) != 5:
            return "Unknown code"

        code_type = code[0]
        system = code[1]

        type_descriptions = {
            "P": "Powertrain",
            "B": "Body",
            "C": "Chassis",
            "U": "Network"
        }

        system_descriptions = {
            "0": "Generic (SAE)",
            "1": "Manufacturer Specific",
            "2": "Generic (SAE)",
            "3": "Generic (SAE) & Manufacturer Specific"
        }

        type_desc = type_descriptions.get(code_type, "Unknown")
        system_desc = system_descriptions.get(system, "Unknown")

        return f"{type_desc} - {system_desc} - Refer to service manual for specific details"

    def read_ecu(self, ecu, file_path=None, callback=None):
        """
        Read data from an ECU

        Args:
            ecu (str): The ECU to read from
            file_path (str, optional): The file path to save the data to
            callback (function, optional): Callback function for progress updates

        Returns:
            bool: True if read successfully, False otherwise
        """
        return self.ecu_programmer.start_programming(ecu, ProgrammingMode.READ, file_path, callback)

    def write_ecu(self, ecu, file_path, callback=None):
        """
        Write data to an ECU

        Args:
            ecu (str): The ECU to write to
            file_path (str): The file path to read the data from
            callback (function, optional): Callback function for progress updates

        Returns:
            bool: True if written successfully, False otherwise
        """
        return self.ecu_programmer.start_programming(ecu, ProgrammingMode.WRITE, file_path, callback)

    def verify_ecu(self, ecu, file_path, callback=None):
        """
        Verify ECU data against a file

        Args:
            ecu (str): The ECU to verify
            file_path (str): The file path to compare against
            callback (function, optional): Callback function for progress updates

        Returns:
            bool: True if verified successfully, False otherwise
        """
        return self.ecu_programmer.start_programming(ecu, ProgrammingMode.VERIFY, file_path, callback)

    def get_programming_status(self):
        """
        Get the current programming status

        Returns:
            dict: The current status
        """
        return self.ecu_programmer.get_status()

    def cancel_programming(self):
        """
        Cancel the current programming operation

        Returns:
            bool: True if cancelled successfully, False otherwise
        """
        return self.ecu_programmer.cancel_programming()
