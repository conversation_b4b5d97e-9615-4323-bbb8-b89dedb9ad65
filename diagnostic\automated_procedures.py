#!/usr/bin/env python3
"""
Automated Diagnostic Procedures Module
This module provides functionality for automated diagnostic procedures.
"""

import logging
import time
import os
import json
import threading
from enum import Enum, auto
from typing import Dict, List, Optional, Any, Tuple, Union, Callable

logger = logging.getLogger("diagnostic.automated_procedures")

class ProcedureStatus(Enum):
    """Procedure status"""
    IDLE = auto()
    RUNNING = auto()
    PAUSED = auto()
    COMPLETED = auto()
    FAILED = auto()

class StepStatus(Enum):
    """Step status"""
    PENDING = auto()
    RUNNING = auto()
    COMPLETED = auto()
    FAILED = auto()
    SKIPPED = auto()

class StepType(Enum):
    """Step type"""
    READ_DATA = auto()
    WRITE_DATA = auto()
    READ_DTC = auto()
    CLEAR_DTC = auto()
    WAIT = auto()
    USER_INPUT = auto()
    CONDITION = auto()
    GOTO = auto()
    END = auto()

class DiagnosticStep:
    """Diagnostic step"""
    
    def __init__(self, step_id: str, step_type: StepType, name: str, description: str = ""):
        """
        Initialize the diagnostic step
        
        Args:
            step_id (str): The step ID
            step_type (StepType): The step type
            name (str): The step name
            description (str): The step description
        """
        self.step_id = step_id
        self.step_type = step_type
        self.name = name
        self.description = description
        self.status = StepStatus.PENDING
        self.parameters: Dict[str, Any] = {}
        self.result: Any = None
        self.next_step: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert to dictionary
        
        Returns:
            dict: Dictionary representation
        """
        return {
            'step_id': self.step_id,
            'step_type': self.step_type.name,
            'name': self.name,
            'description': self.description,
            'status': self.status.name,
            'parameters': self.parameters,
            'result': self.result,
            'next_step': self.next_step
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DiagnosticStep':
        """
        Create from dictionary
        
        Args:
            data (dict): Dictionary representation
            
        Returns:
            DiagnosticStep: The diagnostic step
        """
        step_type = StepType.READ_DATA
        if 'step_type' in data:
            try:
                step_type = StepType[data['step_type']]
            except (KeyError, ValueError):
                pass
        
        step = cls(
            step_id=data.get('step_id', ''),
            step_type=step_type,
            name=data.get('name', ''),
            description=data.get('description', '')
        )
        
        if 'status' in data:
            try:
                step.status = StepStatus[data['status']]
            except (KeyError, ValueError):
                pass
        
        if 'parameters' in data:
            step.parameters = data['parameters']
        
        if 'result' in data:
            step.result = data['result']
        
        if 'next_step' in data:
            step.next_step = data['next_step']
        
        return step

class DiagnosticProcedure:
    """Diagnostic procedure"""
    
    def __init__(self, procedure_id: str, name: str, description: str = ""):
        """
        Initialize the diagnostic procedure
        
        Args:
            procedure_id (str): The procedure ID
            name (str): The procedure name
            description (str): The procedure description
        """
        self.procedure_id = procedure_id
        self.name = name
        self.description = description
        self.status = ProcedureStatus.IDLE
        self.steps: Dict[str, DiagnosticStep] = {}
        self.current_step: Optional[str] = None
        self.start_step: Optional[str] = None
        self.result: Dict[str, Any] = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert to dictionary
        
        Returns:
            dict: Dictionary representation
        """
        steps_dict = {}
        for step_id, step in self.steps.items():
            steps_dict[step_id] = step.to_dict()
        
        return {
            'procedure_id': self.procedure_id,
            'name': self.name,
            'description': self.description,
            'status': self.status.name,
            'steps': steps_dict,
            'current_step': self.current_step,
            'start_step': self.start_step,
            'result': self.result
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DiagnosticProcedure':
        """
        Create from dictionary
        
        Args:
            data (dict): Dictionary representation
            
        Returns:
            DiagnosticProcedure: The diagnostic procedure
        """
        procedure = cls(
            procedure_id=data.get('procedure_id', ''),
            name=data.get('name', ''),
            description=data.get('description', '')
        )
        
        if 'status' in data:
            try:
                procedure.status = ProcedureStatus[data['status']]
            except (KeyError, ValueError):
                pass
        
        if 'steps' in data:
            for step_id, step_data in data['steps'].items():
                procedure.steps[step_id] = DiagnosticStep.from_dict(step_data)
        
        if 'current_step' in data:
            procedure.current_step = data['current_step']
        
        if 'start_step' in data:
            procedure.start_step = data['start_step']
        
        if 'result' in data:
            procedure.result = data['result']
        
        return procedure
    
    def add_step(self, step: DiagnosticStep) -> bool:
        """
        Add a step to the procedure
        
        Args:
            step (DiagnosticStep): The step to add
            
        Returns:
            bool: True if added, False otherwise
        """
        if step.step_id in self.steps:
            logger.warning(f"Step with ID {step.step_id} already exists")
            return False
        
        self.steps[step.step_id] = step
        
        # Set as start step if first step
        if not self.start_step:
            self.start_step = step.step_id
        
        return True
    
    def remove_step(self, step_id: str) -> bool:
        """
        Remove a step from the procedure
        
        Args:
            step_id (str): The step ID
            
        Returns:
            bool: True if removed, False otherwise
        """
        if step_id not in self.steps:
            logger.warning(f"Step with ID {step_id} does not exist")
            return False
        
        # Remove step
        del self.steps[step_id]
        
        # Update start step if needed
        if self.start_step == step_id:
            if self.steps:
                self.start_step = next(iter(self.steps))
            else:
                self.start_step = None
        
        # Update next steps
        for step in self.steps.values():
            if step.next_step == step_id:
                step.next_step = None
        
        return True
    
    def get_step(self, step_id: str) -> Optional[DiagnosticStep]:
        """
        Get a step
        
        Args:
            step_id (str): The step ID
            
        Returns:
            DiagnosticStep: The step or None if not found
        """
        return self.steps.get(step_id)
    
    def reset(self):
        """Reset the procedure"""
        self.status = ProcedureStatus.IDLE
        self.current_step = None
        self.result = {}
        
        # Reset all steps
        for step in self.steps.values():
            step.status = StepStatus.PENDING
            step.result = None

class AutomatedDiagnostics:
    """Automated diagnostics"""
    
    def __init__(self, controller=None):
        """
        Initialize the automated diagnostics
        
        Args:
            controller: The controller to use
        """
        self.controller = controller
        self.procedures: Dict[str, DiagnosticProcedure] = {}
        self.active_procedure: Optional[DiagnosticProcedure] = None
        self.thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
        self.pause_event = threading.Event()
        self.step_callback: Optional[Callable[[DiagnosticStep], None]] = None
        self.procedure_callback: Optional[Callable[[DiagnosticProcedure], None]] = None
        self.user_input_callback: Optional[Callable[[DiagnosticStep], Any]] = None
    
    def set_controller(self, controller):
        """
        Set the controller
        
        Args:
            controller: The controller to use
        """
        self.controller = controller
    
    def set_step_callback(self, callback):
        """
        Set the step callback
        
        Args:
            callback: The callback function (takes a step)
        """
        self.step_callback = callback
    
    def set_procedure_callback(self, callback):
        """
        Set the procedure callback
        
        Args:
            callback: The callback function (takes a procedure)
        """
        self.procedure_callback = callback
    
    def set_user_input_callback(self, callback):
        """
        Set the user input callback
        
        Args:
            callback: The callback function (takes a step and returns a result)
        """
        self.user_input_callback = callback
    
    def add_procedure(self, procedure: DiagnosticProcedure) -> bool:
        """
        Add a procedure
        
        Args:
            procedure (DiagnosticProcedure): The procedure to add
            
        Returns:
            bool: True if added, False otherwise
        """
        if procedure.procedure_id in self.procedures:
            logger.warning(f"Procedure with ID {procedure.procedure_id} already exists")
            return False
        
        self.procedures[procedure.procedure_id] = procedure
        return True
    
    def remove_procedure(self, procedure_id: str) -> bool:
        """
        Remove a procedure
        
        Args:
            procedure_id (str): The procedure ID
            
        Returns:
            bool: True if removed, False otherwise
        """
        if procedure_id not in self.procedures:
            logger.warning(f"Procedure with ID {procedure_id} does not exist")
            return False
        
        # Check if active
        if self.active_procedure and self.active_procedure.procedure_id == procedure_id:
            logger.warning(f"Cannot remove active procedure {procedure_id}")
            return False
        
        # Remove procedure
        del self.procedures[procedure_id]
        return True
    
    def get_procedure(self, procedure_id: str) -> Optional[DiagnosticProcedure]:
        """
        Get a procedure
        
        Args:
            procedure_id (str): The procedure ID
            
        Returns:
            DiagnosticProcedure: The procedure or None if not found
        """
        return self.procedures.get(procedure_id)
    
    def start_procedure(self, procedure_id: str) -> bool:
        """
        Start a procedure
        
        Args:
            procedure_id (str): The procedure ID
            
        Returns:
            bool: True if started, False otherwise
        """
        # Check if already running
        if self.active_procedure and self.active_procedure.status == ProcedureStatus.RUNNING:
            logger.warning("A procedure is already running")
            return False
        
        # Get procedure
        procedure = self.get_procedure(procedure_id)
        if not procedure:
            logger.warning(f"Procedure with ID {procedure_id} does not exist")
            return False
        
        # Reset procedure
        procedure.reset()
        
        # Set as active procedure
        self.active_procedure = procedure
        
        # Set status
        procedure.status = ProcedureStatus.RUNNING
        
        # Set current step
        procedure.current_step = procedure.start_step
        
        # Clear events
        self.stop_event.clear()
        self.pause_event.clear()
        
        # Start thread
        self.thread = threading.Thread(target=self._run_procedure)
        self.thread.daemon = True
        self.thread.start()
        
        logger.info(f"Started procedure: {procedure.name}")
        return True
    
    def stop_procedure(self) -> bool:
        """
        Stop the active procedure
        
        Returns:
            bool: True if stopped, False otherwise
        """
        if not self.active_procedure or self.active_procedure.status != ProcedureStatus.RUNNING:
            logger.warning("No procedure is running")
            return False
        
        # Set stop event
        self.stop_event.set()
        
        # Resume if paused
        self.pause_event.set()
        
        # Wait for thread to finish
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
        
        logger.info(f"Stopped procedure: {self.active_procedure.name}")
        return True
    
    def pause_procedure(self) -> bool:
        """
        Pause the active procedure
        
        Returns:
            bool: True if paused, False otherwise
        """
        if not self.active_procedure or self.active_procedure.status != ProcedureStatus.RUNNING:
            logger.warning("No procedure is running")
            return False
        
        # Clear pause event
        self.pause_event.clear()
        
        # Set status
        self.active_procedure.status = ProcedureStatus.PAUSED
        
        logger.info(f"Paused procedure: {self.active_procedure.name}")
        return True
    
    def resume_procedure(self) -> bool:
        """
        Resume the active procedure
        
        Returns:
            bool: True if resumed, False otherwise
        """
        if not self.active_procedure or self.active_procedure.status != ProcedureStatus.PAUSED:
            logger.warning("No procedure is paused")
            return False
        
        # Set pause event
        self.pause_event.set()
        
        # Set status
        self.active_procedure.status = ProcedureStatus.RUNNING
        
        logger.info(f"Resumed procedure: {self.active_procedure.name}")
        return True
    
    def _run_procedure(self):
        """Run the active procedure"""
        if not self.active_procedure:
            return
        
        procedure = self.active_procedure
        
        try:
            # Run until completed or stopped
            while procedure.status == ProcedureStatus.RUNNING or procedure.status == ProcedureStatus.PAUSED:
                # Check if stopped
                if self.stop_event.is_set():
                    procedure.status = ProcedureStatus.IDLE
                    break
                
                # Check if paused
                if procedure.status == ProcedureStatus.PAUSED:
                    self.pause_event.wait()
                    continue
                
                # Get current step
                if not procedure.current_step or procedure.current_step not in procedure.steps:
                    procedure.status = ProcedureStatus.COMPLETED
                    break
                
                step = procedure.steps[procedure.current_step]
                
                # Set step status
                step.status = StepStatus.RUNNING
                
                # Call step callback
                if self.step_callback:
                    self.step_callback(step)
                
                # Execute step
                success = self._execute_step(step)
                
                # Set step status
                if success:
                    step.status = StepStatus.COMPLETED
                else:
                    step.status = StepStatus.FAILED
                    procedure.status = ProcedureStatus.FAILED
                    break
                
                # Call step callback
                if self.step_callback:
                    self.step_callback(step)
                
                # Get next step
                if step.next_step:
                    procedure.current_step = step.next_step
                else:
                    # End of procedure
                    procedure.status = ProcedureStatus.COMPLETED
                    break
            
            # Call procedure callback
            if self.procedure_callback:
                self.procedure_callback(procedure)
            
            logger.info(f"Procedure completed: {procedure.name} (status: {procedure.status.name})")
        except Exception as e:
            logger.error(f"Error running procedure: {e}")
            procedure.status = ProcedureStatus.FAILED
            
            # Call procedure callback
            if self.procedure_callback:
                self.procedure_callback(procedure)
    
    def _execute_step(self, step: DiagnosticStep) -> bool:
        """
        Execute a step
        
        Args:
            step (DiagnosticStep): The step to execute
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Execute based on step type
            if step.step_type == StepType.READ_DATA:
                return self._execute_read_data(step)
            elif step.step_type == StepType.WRITE_DATA:
                return self._execute_write_data(step)
            elif step.step_type == StepType.READ_DTC:
                return self._execute_read_dtc(step)
            elif step.step_type == StepType.CLEAR_DTC:
                return self._execute_clear_dtc(step)
            elif step.step_type == StepType.WAIT:
                return self._execute_wait(step)
            elif step.step_type == StepType.USER_INPUT:
                return self._execute_user_input(step)
            elif step.step_type == StepType.CONDITION:
                return self._execute_condition(step)
            elif step.step_type == StepType.GOTO:
                return self._execute_goto(step)
            elif step.step_type == StepType.END:
                return self._execute_end(step)
            else:
                logger.error(f"Unknown step type: {step.step_type}")
                return False
        except Exception as e:
            logger.error(f"Error executing step: {e}")
            return False
    
    def _execute_read_data(self, step: DiagnosticStep) -> bool:
        """
        Execute a read data step
        
        Args:
            step (DiagnosticStep): The step to execute
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.controller:
            logger.error("No controller set")
            return False
        
        # Get parameters
        pid = step.parameters.get('pid')
        ecu_id = step.parameters.get('ecu_id')
        
        if not pid:
            logger.error("No PID specified")
            return False
        
        # Read data
        data = self.controller.get_live_data(pid, ecu_id)
        
        # Store result
        step.result = data
        
        # Store in procedure result
        if self.active_procedure:
            self.active_procedure.result[f"data_{pid}"] = data
        
        return data is not None
    
    def _execute_write_data(self, step: DiagnosticStep) -> bool:
        """
        Execute a write data step
        
        Args:
            step (DiagnosticStep): The step to execute
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.controller:
            logger.error("No controller set")
            return False
        
        # Get parameters
        pid = step.parameters.get('pid')
        data = step.parameters.get('data')
        ecu_id = step.parameters.get('ecu_id')
        
        if not pid:
            logger.error("No PID specified")
            return False
        
        if data is None:
            logger.error("No data specified")
            return False
        
        # Write data
        # This would normally call a method on the controller
        # For now, we'll just return True
        step.result = True
        return True
    
    def _execute_read_dtc(self, step: DiagnosticStep) -> bool:
        """
        Execute a read DTC step
        
        Args:
            step (DiagnosticStep): The step to execute
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.controller:
            logger.error("No controller set")
            return False
        
        # Get parameters
        ecu_id = step.parameters.get('ecu_id')
        
        # Read DTCs
        dtcs = self.controller.read_dtc_codes(ecu_id)
        
        # Store result
        step.result = dtcs
        
        # Store in procedure result
        if self.active_procedure:
            self.active_procedure.result['dtcs'] = dtcs
        
        return True
    
    def _execute_clear_dtc(self, step: DiagnosticStep) -> bool:
        """
        Execute a clear DTC step
        
        Args:
            step (DiagnosticStep): The step to execute
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.controller:
            logger.error("No controller set")
            return False
        
        # Get parameters
        ecu_id = step.parameters.get('ecu_id')
        
        # Clear DTCs
        success = self.controller.clear_dtc_codes(ecu_id)
        
        # Store result
        step.result = success
        
        return success
    
    def _execute_wait(self, step: DiagnosticStep) -> bool:
        """
        Execute a wait step
        
        Args:
            step (DiagnosticStep): The step to execute
            
        Returns:
            bool: True if successful, False otherwise
        """
        # Get parameters
        seconds = step.parameters.get('seconds', 1)
        
        # Wait
        time.sleep(seconds)
        
        # Store result
        step.result = True
        
        return True
    
    def _execute_user_input(self, step: DiagnosticStep) -> bool:
        """
        Execute a user input step
        
        Args:
            step (DiagnosticStep): The step to execute
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.user_input_callback:
            logger.error("No user input callback set")
            return False
        
        # Get user input
        result = self.user_input_callback(step)
        
        # Store result
        step.result = result
        
        # Store in procedure result
        if self.active_procedure:
            self.active_procedure.result[f"input_{step.step_id}"] = result
        
        return result is not None
    
    def _execute_condition(self, step: DiagnosticStep) -> bool:
        """
        Execute a condition step
        
        Args:
            step (DiagnosticStep): The step to execute
            
        Returns:
            bool: True if successful, False otherwise
        """
        # Get parameters
        condition = step.parameters.get('condition')
        true_step = step.parameters.get('true_step')
        false_step = step.parameters.get('false_step')
        
        if not condition:
            logger.error("No condition specified")
            return False
        
        # Evaluate condition
        result = False
        
        try:
            # Get values from procedure result
            if self.active_procedure:
                # Replace placeholders with values
                for key, value in self.active_procedure.result.items():
                    placeholder = f"${key}$"
                    if placeholder in condition:
                        if isinstance(value, dict) and 'value' in value:
                            condition = condition.replace(placeholder, str(value['value']))
                        else:
                            condition = condition.replace(placeholder, str(value))
            
            # Evaluate condition
            result = eval(condition)
        except Exception as e:
            logger.error(f"Error evaluating condition: {e}")
            return False
        
        # Store result
        step.result = result
        
        # Set next step
        if result and true_step:
            step.next_step = true_step
        elif not result and false_step:
            step.next_step = false_step
        
        return True
    
    def _execute_goto(self, step: DiagnosticStep) -> bool:
        """
        Execute a goto step
        
        Args:
            step (DiagnosticStep): The step to execute
            
        Returns:
            bool: True if successful, False otherwise
        """
        # Get parameters
        target_step = step.parameters.get('target_step')
        
        if not target_step:
            logger.error("No target step specified")
            return False
        
        # Set next step
        step.next_step = target_step
        
        # Store result
        step.result = True
        
        return True
    
    def _execute_end(self, step: DiagnosticStep) -> bool:
        """
        Execute an end step
        
        Args:
            step (DiagnosticStep): The step to execute
            
        Returns:
            bool: True if successful, False otherwise
        """
        # Set next step to None
        step.next_step = None
        
        # Store result
        step.result = True
        
        return True
    
    def load_procedures(self, file_path: str) -> bool:
        """
        Load procedures from file
        
        Args:
            file_path (str): Path to the file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if file exists
            if not os.path.exists(file_path):
                logger.error(f"File not found: {file_path}")
                return False
            
            # Load JSON file
            with open(file_path, 'r') as file:
                data = json.load(file)
            
            # Parse procedures
            if isinstance(data, list):
                for procedure_data in data:
                    procedure = DiagnosticProcedure.from_dict(procedure_data)
                    self.add_procedure(procedure)
            elif isinstance(data, dict):
                for procedure_id, procedure_data in data.items():
                    procedure = DiagnosticProcedure.from_dict(procedure_data)
                    procedure.procedure_id = procedure_id
                    self.add_procedure(procedure)
            
            logger.info(f"Loaded {len(self.procedures)} procedures from {file_path}")
            return True
        except Exception as e:
            logger.error(f"Error loading procedures: {e}")
            return False
    
    def save_procedures(self, file_path: str) -> bool:
        """
        Save procedures to file
        
        Args:
            file_path (str): Path to the file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Convert procedures to dictionaries
            procedures = {}
            for procedure_id, procedure in self.procedures.items():
                procedures[procedure_id] = procedure.to_dict()
            
            # Save to JSON file
            with open(file_path, 'w') as file:
                json.dump(procedures, file, indent=2)
            
            logger.info(f"Saved {len(self.procedures)} procedures to {file_path}")
            return True
        except Exception as e:
            logger.error(f"Error saving procedures: {e}")
            return False
