"""
Standard OBD Protocols Module
Implements standard OBD communication protocols
"""

class BaseProtocol:
    """Base class for all protocols"""
    
    def __init__(self, name, baud_rate, header_bytes, data_bytes):
        """
        Initialize the protocol
        
        Args:
            name (str): Protocol name
            baud_rate (int): Communication baud rate
            header_bytes (int): Number of header bytes
            data_bytes (int): Maximum number of data bytes
        """
        self.name = name
        self.baud_rate = baud_rate
        self.header_bytes = header_bytes
        self.data_bytes = data_bytes
    
    def format_request(self, command):
        """
        Format a request according to the protocol
        
        Args:
            command (bytes): The command bytes
            
        Returns:
            bytes: The formatted request
        """
        raise NotImplementedError("Subclasses must implement this method")
    
    def parse_response(self, response):
        """
        Parse a response according to the protocol
        
        Args:
            response (bytes): The response bytes
            
        Returns:
            dict: The parsed response
        """
        raise NotImplementedError("Subclasses must implement this method")
    
    def test_connection(self, connection):
        """
        Test if the connection uses this protocol
        
        Args:
            connection: The connection to test
            
        Returns:
            bool: True if the connection uses this protocol, False otherwise
        """
        raise NotImplementedError("Subclasses must implement this method")


class ISO9141Protocol(BaseProtocol):
    """ISO 9141-2 Protocol implementation"""
    
    def __init__(self):
        """Initialize the ISO 9141-2 protocol"""
        super().__init__("ISO 9141-2", 10400, 3, 7)
    
    def format_request(self, command):
        """Format a request according to ISO 9141-2"""
        # Header: 0x68 0x6A 0xF1
        header = bytes([0x68, 0x6A, 0xF1])
        
        # Calculate checksum (sum of all bytes modulo 0xFF + 1)
        checksum = (sum(header) + sum(command)) % 256
        
        # Format: header + command + checksum
        return header + command + bytes([checksum])
    
    def parse_response(self, response):
        """Parse a response according to ISO 9141-2"""
        if len(response) < 4:  # At least header byte + 1 data byte + checksum + 0x03 (end)
            return None
        
        # Check if the response ends with 0x03
        if response[-1] != 0x03:
            return None
        
        # Extract data (excluding header, checksum, and end byte)
        data = response[1:-2]
        
        # Verify checksum
        expected_checksum = sum(response[:-2]) % 256
        actual_checksum = response[-2]
        
        if expected_checksum != actual_checksum:
            return None
        
        return {"data": data}
    
    def test_connection(self, connection):
        """Test if the connection uses ISO 9141-2"""
        # Send a test message (Mode 1 PID 0 - Supported PIDs)
        test_command = bytes([0x01, 0x00])
        formatted_request = self.format_request(test_command)
        
        try:
            connection.send(formatted_request)
            response = connection.receive(timeout=1.0)
            
            # Try to parse the response
            parsed = self.parse_response(response)
            
            # If we got a valid response, this protocol works
            return parsed is not None
        except Exception:
            return False


class ISO14230Protocol(BaseProtocol):
    """ISO 14230-4 (KWP2000) Protocol implementation"""
    
    def __init__(self):
        """Initialize the ISO 14230-4 protocol"""
        super().__init__("ISO 14230-4 (KWP2000)", 10400, 4, 7)
    
    def format_request(self, command):
        """Format a request according to ISO 14230-4"""
        # Format byte (0x80 for physical addressing)
        format_byte = 0x80
        
        # Target address (0x33 for ECU)
        target = 0x33
        
        # Source address (0xF1 for tester)
        source = 0xF1
        
        # Length byte
        length = len(command)
        
        # Header
        header = bytes([format_byte, target, source, length])
        
        # Calculate checksum
        checksum = (sum(header) + sum(command)) % 256
        
        # Format: header + command + checksum
        return header + command + bytes([checksum])
    
    def parse_response(self, response):
        """Parse a response according to ISO 14230-4"""
        if len(response) < 5:  # At least format + target + source + length + checksum
            return None
        
        # Extract length byte
        length = response[3]
        
        # Check if the response length matches the expected length
        if len(response) != length + 5:  # header (4) + data (length) + checksum (1)
            return None
        
        # Extract data
        data = response[4:-1]
        
        # Verify checksum
        expected_checksum = sum(response[:-1]) % 256
        actual_checksum = response[-1]
        
        if expected_checksum != actual_checksum:
            return None
        
        return {"data": data}
    
    def test_connection(self, connection):
        """Test if the connection uses ISO 14230-4"""
        # Send a test message (Mode 1 PID 0 - Supported PIDs)
        test_command = bytes([0x01, 0x00])
        formatted_request = self.format_request(test_command)
        
        try:
            connection.send(formatted_request)
            response = connection.receive(timeout=1.0)
            
            # Try to parse the response
            parsed = self.parse_response(response)
            
            # If we got a valid response, this protocol works
            return parsed is not None
        except Exception:
            return False


class ISO15765Protocol(BaseProtocol):
    """ISO 15765-4 (CAN) Protocol implementation"""
    
    def __init__(self):
        """Initialize the ISO 15765-4 protocol"""
        super().__init__("ISO 15765-4 (CAN)", 500000, 4, 8)
    
    def format_request(self, command):
        """Format a request according to ISO 15765-4"""
        # CAN ID (0x7DF for functional addressing)
        can_id = bytes([0x7D, 0xF0])
        
        # Length byte
        length = len(command)
        
        # Format: CAN ID + length + command + padding (if needed)
        request = can_id + bytes([length]) + command
        
        # Pad to 8 bytes if needed
        if len(request) < 8:
            request += bytes([0] * (8 - len(request)))
        
        return request
    
    def parse_response(self, response):
        """Parse a response according to ISO 15765-4"""
        if len(response) < 4:  # At least CAN ID (2) + length (1) + 1 data byte
            return None
        
        # Extract CAN ID
        can_id = response[:2]
        
        # Extract length byte
        length = response[2]
        
        # Extract data
        data = response[3:3+length]
        
        return {"can_id": can_id, "data": data}
    
    def test_connection(self, connection):
        """Test if the connection uses ISO 15765-4"""
        # Send a test message (Mode 1 PID 0 - Supported PIDs)
        test_command = bytes([0x01, 0x00])
        formatted_request = self.format_request(test_command)
        
        try:
            connection.send(formatted_request)
            response = connection.receive(timeout=1.0)
            
            # Try to parse the response
            parsed = self.parse_response(response)
            
            # If we got a valid response, this protocol works
            return parsed is not None
        except Exception:
            return False


class SAEJ1850PWMProtocol(BaseProtocol):
    """SAE J1850 PWM Protocol implementation"""
    
    def __init__(self):
        """Initialize the SAE J1850 PWM protocol"""
        super().__init__("SAE J1850 PWM", 41600, 3, 7)
    
    def format_request(self, command):
        """Format a request according to SAE J1850 PWM"""
        # Header: 0x61 0x6A 0xF1
        header = bytes([0x61, 0x6A, 0xF1])
        
        # Calculate checksum (XOR of all bytes)
        checksum = 0
        for b in header + command:
            checksum ^= b
        
        # Format: header + command + checksum
        return header + command + bytes([checksum])
    
    def parse_response(self, response):
        """Parse a response according to SAE J1850 PWM"""
        if len(response) < 4:  # At least header byte + 1 data byte + checksum + 0x03 (end)
            return None
        
        # Check if the response ends with 0x03
        if response[-1] != 0x03:
            return None
        
        # Extract data (excluding header, checksum, and end byte)
        data = response[1:-2]
        
        # Verify checksum (XOR of all bytes except checksum and end byte)
        expected_checksum = 0
        for b in response[:-2]:
            expected_checksum ^= b
        
        actual_checksum = response[-2]
        
        if expected_checksum != actual_checksum:
            return None
        
        return {"data": data}
    
    def test_connection(self, connection):
        """Test if the connection uses SAE J1850 PWM"""
        # Send a test message (Mode 1 PID 0 - Supported PIDs)
        test_command = bytes([0x01, 0x00])
        formatted_request = self.format_request(test_command)
        
        try:
            connection.send(formatted_request)
            response = connection.receive(timeout=1.0)
            
            # Try to parse the response
            parsed = self.parse_response(response)
            
            # If we got a valid response, this protocol works
            return parsed is not None
        except Exception:
            return False


class SAEJ1850VPWProtocol(BaseProtocol):
    """SAE J1850 VPW Protocol implementation"""
    
    def __init__(self):
        """Initialize the SAE J1850 VPW protocol"""
        super().__init__("SAE J1850 VPW", 10400, 3, 7)
    
    def format_request(self, command):
        """Format a request according to SAE J1850 VPW"""
        # Header: 0x68 0x6A 0xF1
        header = bytes([0x68, 0x6A, 0xF1])
        
        # Calculate checksum (XOR of all bytes)
        checksum = 0
        for b in header + command:
            checksum ^= b
        
        # Format: header + command + checksum
        return header + command + bytes([checksum])
    
    def parse_response(self, response):
        """Parse a response according to SAE J1850 VPW"""
        if len(response) < 4:  # At least header byte + 1 data byte + checksum + 0x03 (end)
            return None
        
        # Check if the response ends with 0x03
        if response[-1] != 0x03:
            return None
        
        # Extract data (excluding header, checksum, and end byte)
        data = response[1:-2]
        
        # Verify checksum (XOR of all bytes except checksum and end byte)
        expected_checksum = 0
        for b in response[:-2]:
            expected_checksum ^= b
        
        actual_checksum = response[-2]
        
        if expected_checksum != actual_checksum:
            return None
        
        return {"data": data}
    
    def test_connection(self, connection):
        """Test if the connection uses SAE J1850 VPW"""
        # Send a test message (Mode 1 PID 0 - Supported PIDs)
        test_command = bytes([0x01, 0x00])
        formatted_request = self.format_request(test_command)
        
        try:
            connection.send(formatted_request)
            response = connection.receive(timeout=1.0)
            
            # Try to parse the response
            parsed = self.parse_response(response)
            
            # If we got a valid response, this protocol works
            return parsed is not None
        except Exception:
            return False
