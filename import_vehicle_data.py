#!/usr/bin/env python3
"""
Import Vehicle Data Script
This script imports vehicle data from JSON files into the vehicle database.
"""

import os
import sys
import logging
import argparse
from database.vehicle_db import VehicleDatabase
from database.vehicle_data_importer import VehicleDataImporter

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("vehicle_import.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("import_vehicle_data")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Import vehicle data into the database")
    parser.add_argument("--db", default="database/vehicles.db", help="Path to the database file")
    parser.add_argument("--dir", default="database/vehicle_data", help="Path to the data directory")
    parser.add_argument("--file", help="Path to a specific JSON file to import")
    parser.add_argument("--scrape", action="store_true", help="Scrape vehicle data from online sources")
    parser.add_argument("--make", help="Vehicle make (for scraping)")
    parser.add_argument("--model", help="Vehicle model (for scraping)")
    parser.add_argument("--start-year", type=int, help="Start year (for scraping)")
    parser.add_argument("--end-year", type=int, help="End year (for scraping)")
    
    args = parser.parse_args()
    
    # Initialize database
    vehicle_db = VehicleDatabase(args.db)
    
    # Initialize importer
    importer = VehicleDataImporter(vehicle_db, args.dir)
    
    if args.scrape:
        if not args.make:
            logger.error("Make is required for scraping")
            return 1
        
        year_range = None
        if args.start_year and args.end_year:
            year_range = (args.start_year, args.end_year)
        
        # Scrape data
        data = importer.scrape_car_data(args.make, args.model, year_range)
        
        # Count vehicles
        count = 0
        for make_name, models in data.items():
            for model_name, vehicles in models.items():
                count += len(vehicles)
        
        logger.info(f"Scraped and imported {count} vehicles")
    elif args.file:
        # Import data from a specific file
        count = importer.import_from_json(args.file)
        logger.info(f"Imported {count} vehicles from {args.file}")
    else:
        # Get list of JSON files
        json_files = [f for f in os.listdir(args.dir) if f.endswith('.json')]
        
        if not json_files:
            logger.warning(f"No JSON files found in {args.dir}")
            return 0
        
        # Import data from each file
        total_count = 0
        for file_name in json_files:
            file_path = os.path.join(args.dir, file_name)
            count = importer.import_from_json(file_path)
            total_count += count
            logger.info(f"Imported {count} vehicles from {file_name}")
        
        logger.info(f"Total vehicles imported: {total_count}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
