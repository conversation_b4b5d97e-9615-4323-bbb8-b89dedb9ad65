#!/usr/bin/env python3
"""
Enhanced Vehicle Data Import Script
This script imports comprehensive vehicle data from JSON files and adds it to the database.
"""

import os
import json
import sys
import argparse
import logging

# Add parent directory to path to import database modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from vehicle_db import VehicleDatabase, Make, Model, Vehicle, ECU

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("vehicle_import.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("vehicle_import")

def import_vehicle_data(db, file_path, region=None):
    """
    Import vehicle data from a JSON file

    Args:
        db (VehicleDatabase): The vehicle database
        file_path (str): Path to the JSON file
        region (str, optional): The region (asian, european, american)

    Returns:
        tuple: (makes_imported, models_imported, vehicles_imported, ecus_imported)
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        session = db.Session()
        makes_imported = 0
        models_imported = 0
        vehicles_imported = 0
        ecus_imported = 0

        # Process each make in the file
        for make_name, make_data in data.items():
            print(f"Processing make: {make_name}")
            logger.info(f"Processing make: {make_name}")

            # Check if make exists
            make = session.query(Make).filter(Make.name == make_name).first()

            if not make:
                # Create new make
                make = Make(name=make_name)
                session.add(make)
                session.flush()
                makes_imported += 1
                logger.info(f"Added new make: {make_name}")

            # Process models for this make
            for model_data in make_data:
                model_name = model_data.get("model")
                if not model_name:
                    logger.warning(f"Skipping model with no name for make {make_name}")
                    continue

                logger.info(f"Processing model: {model_name}")

                # Check if model exists
                model = session.query(Model).filter(
                    Model.name == model_name,
                    Model.make_id == make.id
                ).first()

                if not model:
                    # Create new model
                    model = Model(name=model_name, make_id=make.id)
                    session.add(model)
                    session.flush()
                    models_imported += 1
                    logger.info(f"Added new model: {model_name}")

                # Process variants and generations
                variants = model_data.get("variants", [model_name])
                generations = model_data.get("years", {})

                # Get other vehicle data
                engine_types = model_data.get("engine_types", ["Unknown"])
                fuel_types = model_data.get("fuel_types", ["Unknown"])
                transmission_types = model_data.get("transmission_types", ["Unknown"])
                body_styles = model_data.get("body_styles", ["Unknown"])
                drive_types = model_data.get("drive_types", ["Unknown"])
                obd_protocols = model_data.get("obd_protocols", ["ISO15765-4 (CAN)"])
                manufacturer_protocols = model_data.get("manufacturer_protocols", [])

                # OBD port and ECU locations
                obd_port_location = model_data.get("obd_port_location", "Under dashboard, driver's side")

                # Process each generation
                for generation, years in generations.items():
                    logger.info(f"Processing generation: {generation} ({min(years)}-{max(years)})")

                    # Process each variant
                    for variant in variants:
                        # Process each year
                        for year in years:
                            # Check if vehicle already exists
                            existing_vehicle = session.query(Vehicle).filter(
                                Vehicle.model_id == model.id,
                                Vehicle.year == year,
                                Vehicle.variant == variant,
                                Vehicle.generation == generation
                            ).first()

                            if not existing_vehicle:
                                # Create new vehicle
                                vehicle = Vehicle(
                                    model_id=model.id,
                                    year=year,
                                    generation=generation,
                                    variant=variant,
                                    engine_type=engine_types[0],
                                    engine_displacement=model_data.get("engine_displacement", 2.0),
                                    fuel_type=fuel_types[0],
                                    transmission_type=transmission_types[0],
                                    num_cylinders=model_data.get("num_cylinders", 4),
                                    body_style=body_styles[0] if body_styles else None,
                                    drive_type=drive_types[0] if drive_types else None,
                                    obd_protocol=obd_protocols[0],
                                    manufacturer_protocol=manufacturer_protocols[0] if manufacturer_protocols else None,
                                    obd_port_location=obd_port_location,
                                    ecu_location=model_data.get("ecu_location", ""),
                                    security_level=model_data.get("security_level", 0),
                                    special_notes=model_data.get("special_notes", ""),
                                    vin_pattern=model_data.get("vin_pattern", ""),
                                    engine_code=model_data.get("engine_code", "")
                                )
                                session.add(vehicle)
                                session.flush()
                                vehicles_imported += 1

                                # Add ECUs if available
                                ecus = model_data.get("ecus", [])
                                for ecu_data in ecus:
                                    ecu = ECU(
                                        vehicle_id=vehicle.id,
                                        name=ecu_data.get("name", "Engine Control Module"),
                                        code=ecu_data.get("code", ""),
                                        location=ecu_data.get("location", ""),
                                        protocol=ecu_data.get("protocol", obd_protocols[0]),
                                        address=ecu_data.get("address", ""),
                                        security_type=ecu_data.get("security_type", ""),
                                        flash_method=ecu_data.get("flash_method", ""),
                                        special_notes=ecu_data.get("special_notes", "")
                                    )
                                    session.add(ecu)
                                    ecus_imported += 1

        session.commit()
        session.close()

        logger.info(f"Import complete: {makes_imported} makes, {models_imported} models, {vehicles_imported} vehicles, {ecus_imported} ECUs")
        return (makes_imported, models_imported, vehicles_imported, ecus_imported)

    except Exception as e:
        logger.error(f"Error importing vehicle data from {file_path}: {e}")
        if 'session' in locals():
            session.rollback()
            session.close()
        return (0, 0, 0, 0)

def import_directory(db, directory):
    """
    Import all JSON files in a directory

    Args:
        db (VehicleDatabase): The vehicle database
        directory (str): Path to the directory

    Returns:
        tuple: (makes_imported, models_imported, vehicles_imported, ecus_imported)
    """
    total_makes = 0
    total_models = 0
    total_vehicles = 0
    total_ecus = 0

    if not os.path.exists(directory):
        logger.error(f"Directory not found: {directory}")
        return (0, 0, 0, 0)

    # Get all JSON files in the directory
    json_files = [f for f in os.listdir(directory) if f.endswith('.json')]

    if not json_files:
        logger.warning(f"No JSON files found in {directory}")
        return (0, 0, 0, 0)

    # Import each file
    for file_name in json_files:
        file_path = os.path.join(directory, file_name)
        logger.info(f"Importing {file_path}")

        # Determine region from directory name
        region = os.path.basename(directory)

        # Import the file
        makes, models, vehicles, ecus = import_vehicle_data(db, file_path, region)

        total_makes += makes
        total_models += models
        total_vehicles += vehicles
        total_ecus += ecus

    return (total_makes, total_models, total_vehicles, total_ecus)

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Import vehicle data from JSON files')
    parser.add_argument('--all', action='store_true', help='Import all vehicle data')
    parser.add_argument('--asian', action='store_true', help='Import Asian vehicle data')
    parser.add_argument('--european', action='store_true', help='Import European vehicle data')
    parser.add_argument('--american', action='store_true', help='Import American vehicle data')
    parser.add_argument('--file', type=str, help='Import a specific JSON file')

    args = parser.parse_args()

    # Initialize database
    db = VehicleDatabase()

    # Get the base directory
    base_dir = os.path.dirname(os.path.abspath(__file__))

    total_makes = 0
    total_models = 0
    total_vehicles = 0
    total_ecus = 0

    # Import specific file if requested
    if args.file:
        file_path = args.file if os.path.isabs(args.file) else os.path.join(base_dir, args.file)
        if os.path.exists(file_path):
            makes, models, vehicles, ecus = import_vehicle_data(db, file_path)
            total_makes += makes
            total_models += models
            total_vehicles += vehicles
            total_ecus += ecus
        else:
            logger.error(f"File not found: {file_path}")

    # Import Asian vehicle data
    if args.all or args.asian:
        asian_dir = os.path.join(base_dir, 'asian')
        makes, models, vehicles, ecus = import_directory(db, asian_dir)
        total_makes += makes
        total_models += models
        total_vehicles += vehicles
        total_ecus += ecus

    # Import European vehicle data
    if args.all or args.european:
        european_dir = os.path.join(base_dir, 'european')
        makes, models, vehicles, ecus = import_directory(db, european_dir)
        total_makes += makes
        total_models += models
        total_vehicles += vehicles
        total_ecus += ecus

    # Import American vehicle data
    if args.all or args.american:
        american_dir = os.path.join(base_dir, 'american')
        makes, models, vehicles, ecus = import_directory(db, american_dir)
        total_makes += makes
        total_models += models
        total_vehicles += vehicles
        total_ecus += ecus

    # Print summary
    if total_makes > 0 or total_models > 0 or total_vehicles > 0:
        logger.info(f"Total: {total_makes} makes, {total_models} models, {total_vehicles} vehicles, {total_ecus} ECUs")
    else:
        if not any([args.all, args.asian, args.european, args.american, args.file]):
            parser.print_help()
        else:
            logger.warning("No vehicles imported. Check if the JSON files exist.")

if __name__ == "__main__":
    main()
