#!/usr/bin/env python3
"""
Query Vehicle Database
This script queries the vehicle database to verify the imported data.
"""

import os
import sys
from database.vehicle_db import VehicleDatabase

def main():
    """Main function"""
    print("Querying vehicle database...")
    
    # Initialize database
    db = VehicleDatabase()
    
    # Get all makes
    makes = db.get_makes()
    print(f"\nAvailable Makes ({len(makes)}):")
    for make in makes:
        print(f"  {make}")
    
    # Get models for each make
    for make in makes:
        models = db.get_models(make)
        print(f"\nModels for {make} ({len(models)}):")
        for model in models:
            print(f"  {model}")
            
            # Get years for each model
            years = db.get_years(make, model)
            print(f"    Years: {min(years) if years else 'N/A'} - {max(years) if years else 'N/A'} ({len(years)} variants)")
            
            # Get variants for a specific year (using the latest year)
            if years:
                latest_year = max(years)
                variants = db.get_variants(make, model, latest_year)
                print(f"    Variants for {latest_year}: {', '.join(variants) if variants else 'N/A'}")
                
                # Get details for a specific variant
                if variants:
                    variant = variants[0]
                    vehicle = db.get_vehicle(make, model, latest_year, variant)
                    
                    if vehicle:
                        print(f"\n    Details for {latest_year} {make} {model} {variant}:")
                        print(f"      Generation: {vehicle['generation']}")
                        print(f"      Engine: {vehicle['engine_type']} {vehicle['engine_displacement']}L {vehicle['num_cylinders']} cylinder")
                        print(f"      Fuel Type: {vehicle['fuel_type']}")
                        print(f"      Transmission: {vehicle['transmission_type']}")
                        print(f"      Body Style: {vehicle['body_style']}")
                        print(f"      Drive Type: {vehicle['drive_type']}")
                        print(f"      OBD Protocol: {vehicle['obd_protocol']}")
                        print(f"      Manufacturer Protocol: {vehicle['manufacturer_protocol']}")
                        
                        print(f"      ECUs ({len(vehicle['ecus'])}):")
                        for ecu in vehicle['ecus'][:3]:  # Show only first 3 ECUs
                            print(f"        {ecu['name']} ({ecu['code']})")
                        
                        if len(vehicle['ecus']) > 3:
                            print(f"        ... and {len(vehicle['ecus']) - 3} more")
    
    print("\nQuery complete!")

if __name__ == "__main__":
    main()
