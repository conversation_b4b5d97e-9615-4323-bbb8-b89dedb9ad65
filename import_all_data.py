#!/usr/bin/env python3
"""
Import All Data Script
This script imports all vehicle and DTC data into the database.
"""

import os
import sys
import subprocess

def main():
    """Main function"""
    print("Importing all vehicle and DTC data...")
    
    # Import vehicle data
    print("\nImporting vehicle data...")
    vehicle_data_script = os.path.join("database", "vehicle_data", "import_vehicle_data.py")
    if os.path.exists(vehicle_data_script):
        subprocess.run([sys.executable, vehicle_data_script, "--all"])
    else:
        print(f"Vehicle data import script not found: {vehicle_data_script}")
    
    # Import DTC codes
    print("\nImporting DTC codes...")
    dtc_codes_script = os.path.join("database", "dtc_codes", "import_dtc_codes.py")
    if os.path.exists(dtc_codes_script):
        subprocess.run([sys.executable, dtc_codes_script, "--all"])
    else:
        print(f"DTC codes import script not found: {dtc_codes_script}")
    
    print("\nImport complete!")

if __name__ == "__main__":
    main()
