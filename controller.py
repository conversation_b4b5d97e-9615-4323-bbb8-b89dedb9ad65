#!/usr/bin/env python3
"""
Application Controller
This module provides the controller for the vehicle diagnostic application.
"""

import sys
import os
import logging
import time
import serial
import serial.tools.list_ports

from hardware.elm327 import ELM327Interface
from hardware.interface_manager import InterfaceManager, InterfaceInfo, InterfaceType
from protocols.protocol_manager import ProtocolManager, ConnectionMethod
from protocols.protocol_handler import SecurityLevel
from diagnostic.session_manager import Diagnostic<PERSON>ession, SessionState
from diagnostic.multi_ecu_manager import MultiECUManager, ECUInfo, ECUType
from diagnostic.ecu_programmer import EC<PERSON>rogrammer, ProgrammingStatus
from diagnostic.ecu_coding import ECUCoding, AdaptationChannel, CodingData, CodingType
from diagnostic.automated_procedures import (
    AutomatedDiagnostics, DiagnosticProcedure, DiagnosticStep,
    ProcedureStatus, StepStatus, StepType
)
from database.dtc_db import DTCDatabase

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("app.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("controller")

class Controller:
    """Application controller"""

    def __init__(self):
        """Initialize the controller"""
        self.dtc_db = DTCDatabase()
        self.protocol_handler = None
        self.diagnostic_session = None
        self.interface = None
        self.connected = False
        self.current_vehicle = None

        # Initialize protocol handler
        self.protocol_handler = self.create_protocol_handler()

        # Initialize interface manager
        self.interface_manager = InterfaceManager()

        # Initialize multi-ECU manager
        self.ecu_manager = MultiECUManager()

        # Initialize ECU programmer
        self.ecu_programmer = ECUProgrammer()

        # Initialize ECU coding
        self.ecu_coding = ECUCoding()

        # Initialize automated diagnostics
        self.automated_diagnostics = AutomatedDiagnostics(self)

        # Detect available interfaces
        self.interface_manager.detect_interfaces()

        logger.info("Controller initialized")

    def create_protocol_handler(self):
        """
        Create a protocol handler

        Returns:
            ProtocolHandler: The protocol handler
        """
        try:
            from protocols.protocol_handler import ProtocolHandler
            return ProtocolHandler()
        except Exception as e:
            logger.error(f"Error creating protocol handler: {e}")
            return None

    def get_available_ports(self):
        """
        Get a list of available serial ports

        Returns:
            list: List of available ports
        """
        try:
            # Detect interfaces
            self.interface_manager.detect_interfaces()

            # Get all interfaces
            interfaces = self.interface_manager.get_all_interfaces()

            # Convert to list of dictionaries
            ports = []
            for interface in interfaces:
                ports.append({
                    'port': interface.interface_id,
                    'description': interface.description,
                    'type': interface.interface_type.name,
                    'name': interface.name
                })

            return ports
        except Exception as e:
            logger.error(f"Error getting available ports: {e}")
            return []

    def connect_to_vehicle(self, port, protocol=None):
        """
        Connect to the vehicle

        Args:
            port (str): The interface port/ID
            protocol (str): The protocol name (optional)

        Returns:
            bool: True if connected, False otherwise
        """
        try:
            # Connect to the interface
            if not self.interface_manager.connect_interface(port):
                logger.error(f"Failed to connect to interface: {port}")
                return False

            # Get the active interface
            self.interface = self.interface_manager.get_active_interface()
            if not self.interface:
                logger.error("No active interface")
                return False

            logger.info(f"Connected to interface: {self.interface_manager.active_interface}")

            # Create diagnostic session for main communication
            self.diagnostic_session = DiagnosticSession(self.interface)

            # Connect to the vehicle
            if protocol:
                # Manual connection with protocol
                logger.info(f"Connecting to vehicle using protocol: {protocol}")
                if not self.diagnostic_session.connect(method=ConnectionMethod.MANUAL, protocol_name=protocol):
                    logger.error("Failed to connect to vehicle")
                    self.interface_manager.disconnect_interface(port)
                    return False
            else:
                # Automatic connection
                logger.info("Connecting to vehicle (automatic detection)")
                if not self.diagnostic_session.connect(method=ConnectionMethod.AUTO):
                    logger.error("Failed to connect to vehicle")
                    self.interface_manager.disconnect_interface(port)
                    return False

            # Set interface for ECU manager
            self.ecu_manager.set_interface(self.interface)

            # Scan for ECUs if auto-scan is enabled
            if self.ecu_manager.auto_scan:
                found_ecus = self.ecu_manager.scan_ecus()
                logger.info(f"Found {len(found_ecus)} ECUs during auto-scan")

                # Connect to engine ECU if found
                for ecu in found_ecus:
                    if ecu.ecu_type == ECUType.ENGINE:
                        self.ecu_manager.connect_ecu(ecu.ecu_id)
                        break

            self.connected = True
            return True
        except Exception as e:
            logger.error(f"Error connecting to vehicle: {e}")
            return False

    def disconnect(self):
        """
        Disconnect from the vehicle

        Returns:
            bool: True if disconnected, False otherwise
        """
        try:
            # Disconnect from all ECUs
            self.ecu_manager.disconnect_all()

            # Disconnect main diagnostic session
            if self.diagnostic_session:
                self.diagnostic_session.disconnect()

            # Disconnect all interfaces
            self.interface_manager.disconnect_all()

            self.interface = None
            self.connected = False
            logger.info("Disconnected from vehicle")
            return True
        except Exception as e:
            logger.error(f"Error disconnecting from vehicle: {e}")
            return False

    def read_dtc_codes(self, ecu_id=None):
        """
        Read DTC codes from the vehicle

        Args:
            ecu_id (int): The ECU ID (optional, uses active ECU if None)

        Returns:
            list: List of DTC codes
        """
        try:
            if not self.connected:
                logger.error("Not connected to vehicle")
                return []

            # Read DTCs from the specified ECU or active ECU
            if ecu_id is not None:
                dtcs = self.ecu_manager.read_dtc(ecu_id)
            elif self.ecu_manager.active_ecu:
                dtcs = self.ecu_manager.read_dtc()
            else:
                # Fall back to main diagnostic session if no ECU is active
                if not self.diagnostic_session:
                    logger.error("No active diagnostic session")
                    return []
                dtcs = self.diagnostic_session.read_dtc()

            # Add detailed information from the database
            detailed_dtcs = []
            for dtc in dtcs:
                code = dtc.get('code')
                if code:
                    # Get DTC information from the database
                    dtc_info = self.dtc_db.get_dtc_info(code)

                    if dtc_info:
                        # Combine vehicle DTC with database information
                        detailed_dtc = {**dtc, **dtc_info}
                        detailed_dtcs.append(detailed_dtc)
                    else:
                        # No detailed information available
                        detailed_dtcs.append(dtc)

            return detailed_dtcs
        except Exception as e:
            logger.error(f"Error reading DTC codes: {e}")
            return []

    def clear_dtc_codes(self, ecu_id=None):
        """
        Clear DTC codes from the vehicle

        Args:
            ecu_id (int): The ECU ID (optional, uses active ECU if None)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.connected:
                logger.error("Not connected to vehicle")
                return False

            # Clear DTCs from the specified ECU or active ECU
            if ecu_id is not None:
                return self.ecu_manager.clear_dtc(ecu_id)
            elif self.ecu_manager.active_ecu:
                return self.ecu_manager.clear_dtc()
            else:
                # Fall back to main diagnostic session if no ECU is active
                if not self.diagnostic_session:
                    logger.error("No active diagnostic session")
                    return False
                return self.diagnostic_session.clear_dtc()
        except Exception as e:
            logger.error(f"Error clearing DTC codes: {e}")
            return False

    def get_live_data(self, pid, ecu_id=None):
        """
        Get live data from the vehicle

        Args:
            pid (str): The parameter ID
            ecu_id (int): The ECU ID (optional, uses active ECU if None)

        Returns:
            dict: The live data
        """
        try:
            if not self.connected:
                logger.error("Not connected to vehicle")
                return None

            # Read data from the specified ECU or active ECU
            if ecu_id is not None:
                data = self.ecu_manager.read_data(pid, ecu_id)
            elif self.ecu_manager.active_ecu:
                data = self.ecu_manager.read_data(pid)
            else:
                # Fall back to main diagnostic session if no ECU is active
                if not self.diagnostic_session:
                    logger.error("No active diagnostic session")
                    return None
                data = self.diagnostic_session.read_data(pid)

            if data is not None:
                # Format the data
                return {
                    'pid': pid,
                    'value': data,
                    'unit': self.get_unit_for_pid(pid),
                    'time': time.time()
                }

            return None
        except Exception as e:
            logger.error(f"Error getting live data: {e}")
            return None

    def get_unit_for_pid(self, pid):
        """
        Get the unit for a PID

        Args:
            pid (str): The parameter ID

        Returns:
            str: The unit
        """
        # This would normally come from a database or configuration
        # For now, we'll just use some common units
        pid_units = {
            'ENGINE_LOAD': '%',
            'COOLANT_TEMP': '°C',
            'SHORT_FUEL_TRIM_1': '%',
            'LONG_FUEL_TRIM_1': '%',
            'INTAKE_PRESSURE': 'kPa',
            'RPM': 'rpm',
            'SPEED': 'km/h',
            'TIMING_ADVANCE': '°',
            'INTAKE_TEMP': '°C',
            'MAF': 'g/s',
            'THROTTLE_POS': '%',
            'O2_SENSORS': '',
            'FUEL_LEVEL': '%',
            'DISTANCE_W_MIL': 'km',
            'CONTROL_MODULE_VOLTAGE': 'V',
            'AMBIANT_AIR_TEMP': '°C',
            'FUEL_TYPE': '',
            'FUEL_RATE': 'L/h'
        }

        return pid_units.get(pid, '')

    def get_vehicle_makes(self):
        """
        Get a list of vehicle makes

        Returns:
            list: List of vehicle makes
        """
        try:
            # Get makes from the vehicle database
            from database.vehicle_db import VehicleDatabase
            vehicle_db = VehicleDatabase()
            return vehicle_db.get_makes()
        except Exception as e:
            logger.error(f"Error getting vehicle makes: {e}")
            # Fallback to hardcoded list
            return [
                "Toyota",
                "Honda",
                "Ford",
                "Chevrolet",
                "BMW",
                "Mercedes-Benz",
                "Volkswagen",
                "Audi",
                "Nissan",
                "Hyundai",
                "Kia",
                "Mazda",
                "Subaru",
                "Lexus",
                "Acura",
                "Infiniti",
                "Volvo",
                "Jeep",
                "Dodge",
                "Chrysler"
            ]

    def get_vehicle_models(self, make):
        """
        Get a list of vehicle models for a make

        Args:
            make (str): The vehicle make

        Returns:
            list: List of vehicle models
        """
        try:
            # Get models from the vehicle database
            from database.vehicle_db import VehicleDatabase
            vehicle_db = VehicleDatabase()
            return vehicle_db.get_models(make)
        except Exception as e:
            logger.error(f"Error getting vehicle models: {e}")
            # Fallback to hardcoded list
            models = {
                "Toyota": ["Camry", "Corolla", "RAV4", "Highlander", "Tacoma", "Tundra", "Prius", "4Runner"],
                "Honda": ["Accord", "Civic", "CR-V", "Pilot", "Odyssey", "Fit", "HR-V", "Ridgeline"],
                "Ford": ["F-150", "Escape", "Explorer", "Mustang", "Focus", "Edge", "Ranger", "Expedition"],
                "Chevrolet": ["Silverado", "Equinox", "Malibu", "Tahoe", "Suburban", "Traverse", "Camaro", "Colorado"],
                "BMW": ["3 Series", "5 Series", "7 Series", "X3", "X5", "X7", "M3", "M5"],
                "Mercedes-Benz": ["C-Class", "E-Class", "S-Class", "GLC", "GLE", "GLS", "A-Class", "CLA"],
                "Volkswagen": ["Golf", "Jetta", "Passat", "Tiguan", "Atlas", "ID.4", "Arteon", "Taos"],
                "Audi": ["A3", "A4", "A6", "Q3", "Q5", "Q7", "e-tron", "TT"],
                "Nissan": ["Altima", "Sentra", "Rogue", "Pathfinder", "Murano", "Frontier", "Titan", "Maxima"],
                "Hyundai": ["Elantra", "Sonata", "Tucson", "Santa Fe", "Kona", "Palisade", "Venue", "Ioniq"],
                "Kia": ["Forte", "Optima", "Sportage", "Sorento", "Telluride", "Soul", "Seltos", "Niro"],
                "Mazda": ["Mazda3", "Mazda6", "CX-5", "CX-9", "MX-5 Miata", "CX-30", "CX-3", "CX-50"],
                "Subaru": ["Outback", "Forester", "Impreza", "Legacy", "Crosstrek", "Ascent", "WRX", "BRZ"],
                "Lexus": ["ES", "RX", "NX", "IS", "GX", "LX", "UX", "LS"],
                "Acura": ["MDX", "RDX", "TLX", "ILX", "NSX", "RLX", "TL", "TSX"],
                "Infiniti": ["Q50", "Q60", "QX50", "QX60", "QX80", "G37", "FX35", "JX35"],
                "Volvo": ["XC90", "XC60", "XC40", "S60", "S90", "V60", "V90", "C40"],
                "Jeep": ["Wrangler", "Grand Cherokee", "Cherokee", "Compass", "Renegade", "Gladiator", "Wagoneer", "Grand Wagoneer"],
                "Dodge": ["Charger", "Challenger", "Durango", "Journey", "Grand Caravan", "Ram 1500", "Ram 2500", "Ram 3500"],
                "Chrysler": ["300", "Pacifica", "Voyager", "Town & Country", "200", "Sebring", "PT Cruiser", "Crossfire"]
            }
            return models.get(make, [])

    def get_vehicle_years(self, make, model):
        """
        Get a list of years for a vehicle make and model

        Args:
            make (str): The vehicle make
            model (str): The vehicle model

        Returns:
            list: List of years
        """
        try:
            # Get years from the vehicle database
            from database.vehicle_db import VehicleDatabase
            vehicle_db = VehicleDatabase()
            return vehicle_db.get_years(make, model)
        except Exception as e:
            logger.error(f"Error getting vehicle years: {e}")
            # Fallback to hardcoded range
            return list(range(1996, 2024))

    def set_current_vehicle(self, make, model, year, variant=None):
        """
        Set the current vehicle

        Args:
            make (str): The vehicle make
            model (str): The vehicle model
            year (int): The vehicle year
            variant (str, optional): The vehicle variant

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get vehicle information from the database
            from database.vehicle_db import VehicleDatabase
            vehicle_db = VehicleDatabase()
            vehicle_info = vehicle_db.get_vehicle(make, model, year, variant)

            if vehicle_info:
                self.current_vehicle = vehicle_info
                logger.info(f"Set current vehicle to {make} {model} {year} {variant if variant else ''}")
                return True
            else:
                # Fallback to simple vehicle object
                self.current_vehicle = {
                    'make': make,
                    'model': model,
                    'year': year,
                    'variant': variant,
                    'engine': 'Unknown',
                    'transmission': 'Unknown',
                    'vin': 'Unknown'
                }
                logger.info(f"Set current vehicle to {make} {model} {year} {variant if variant else ''} (basic info)")
                return True
        except Exception as e:
            logger.error(f"Error setting current vehicle: {e}")
            return False

    def get_vehicle_variants(self, make, model, year):
        """
        Get a list of variants for a specific make, model, and year

        Args:
            make (str): The vehicle make
            model (str): The vehicle model
            year (int): The vehicle year

        Returns:
            list: List of variant names
        """
        try:
            # Get variants from the vehicle database
            from database.vehicle_db import VehicleDatabase
            vehicle_db = VehicleDatabase()
            return vehicle_db.get_variants(make, model, year)
        except Exception as e:
            logger.error(f"Error getting vehicle variants: {e}")
            return []

    def get_dtc_categories(self):
        """
        Get a list of DTC categories

        Returns:
            list: List of DTC categories
        """
        try:
            return self.dtc_db.get_all_categories()
        except Exception as e:
            logger.error(f"Error getting DTC categories: {e}")
            return []

    def get_dtc_codes_by_category(self, category_prefix):
        """
        Get a list of DTC codes for a category

        Args:
            category_prefix (str): The category prefix

        Returns:
            list: List of DTC codes
        """
        try:
            return self.dtc_db.search_dtc_codes("", category=category_prefix)
        except Exception as e:
            logger.error(f"Error getting DTC codes by category: {e}")
            return []

    def search_dtc_codes(self, search_term):
        """
        Search for DTC codes

        Args:
            search_term (str): The search term

        Returns:
            list: List of matching DTC codes
        """
        try:
            return self.dtc_db.search_dtc_codes(search_term)
        except Exception as e:
            logger.error(f"Error searching DTC codes: {e}")
            return []

    def get_available_ecus(self):
        """
        Get a list of available ECUs

        Returns:
            list: List of available ECUs
        """
        try:
            if not self.connected:
                logger.error("Not connected to vehicle")
                return []

            # Get all ECUs
            ecus = self.ecu_manager.get_all_ecus()

            # Convert to dictionaries
            ecu_dicts = []
            for ecu in ecus:
                ecu_dicts.append(ecu.to_dict())

            return ecu_dicts
        except Exception as e:
            logger.error(f"Error getting available ECUs: {e}")
            return []

    def scan_for_ecus(self):
        """
        Scan for available ECUs

        Returns:
            list: List of found ECUs
        """
        try:
            if not self.connected:
                logger.error("Not connected to vehicle")
                return []

            # Scan for ECUs
            found_ecus = self.ecu_manager.scan_ecus()

            # Convert to dictionaries
            ecu_dicts = []
            for ecu in found_ecus:
                ecu_dicts.append(ecu.to_dict())

            return ecu_dicts
        except Exception as e:
            logger.error(f"Error scanning for ECUs: {e}")
            return []

    def set_active_ecu(self, ecu_id):
        """
        Set the active ECU

        Args:
            ecu_id (int): The ECU ID

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.connected:
                logger.error("Not connected to vehicle")
                return False

            # Set active ECU
            return self.ecu_manager.set_active_ecu(ecu_id)
        except Exception as e:
            logger.error(f"Error setting active ECU: {e}")
            return False

    def get_available_interfaces(self):
        """
        Get a list of available interfaces

        Returns:
            list: List of available interfaces
        """
        try:
            # Detect interfaces
            self.interface_manager.detect_interfaces()

            # Get all interfaces
            interfaces = self.interface_manager.get_all_interfaces()

            # Convert to list of dictionaries
            interface_dicts = []
            for interface in interfaces:
                interface_dicts.append(interface.to_dict())

            return interface_dicts
        except Exception as e:
            logger.error(f"Error getting available interfaces: {e}")
            return []

    def update_interface_firmware(self, interface_id, firmware_file):
        """
        Update interface firmware

        Args:
            interface_id (str): The interface ID
            firmware_file (str): Path to the firmware file

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Update firmware
            return self.interface_manager.update_firmware(interface_id, firmware_file)
        except Exception as e:
            logger.error(f"Error updating interface firmware: {e}")
            return False

    def program_ecu(self, ecu_id, firmware_file, memory_address, callback=None):
        """
        Program an ECU

        Args:
            ecu_id (int): The ECU ID
            firmware_file (str): Path to the firmware file
            memory_address (int): The memory address
            callback: Callback function for progress updates

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.connected:
                logger.error("Not connected to vehicle")
                return False

            # Get the ECU
            ecu = self.ecu_manager.get_ecu(ecu_id)
            if not ecu:
                logger.error(f"ECU with ID {ecu_id:02X} not found")
                return False

            # Connect to the ECU if not already connected
            if not ecu.connected:
                if not self.ecu_manager.connect_ecu(ecu_id):
                    logger.error(f"Failed to connect to ECU {ecu_id:02X}")
                    return False

            # Set protocol for ECU programmer
            self.ecu_programmer.set_protocol(ecu.protocol)

            # Set callback
            if callback:
                self.ecu_programmer.set_callback(callback)

            # Program ECU
            success = self.ecu_programmer.program_ecu(firmware_file, memory_address)

            if success:
                logger.info(f"Successfully programmed ECU {ecu_id:02X}")
            else:
                logger.error(f"Failed to program ECU {ecu_id:02X}")

            return success
        except Exception as e:
            logger.error(f"Error programming ECU: {e}")
            return False

    def read_ecu_coding(self, ecu_id, coding_id):
        """
        Read ECU coding

        Args:
            ecu_id (int): The ECU ID
            coding_id (int): The coding ID

        Returns:
            CodingData: The coding data or None if failed
        """
        try:
            if not self.connected:
                logger.error("Not connected to vehicle")
                return None

            # Get the ECU
            ecu = self.ecu_manager.get_ecu(ecu_id)
            if not ecu:
                logger.error(f"ECU with ID {ecu_id:02X} not found")
                return None

            # Connect to the ECU if not already connected
            if not ecu.connected:
                if not self.ecu_manager.connect_ecu(ecu_id):
                    logger.error(f"Failed to connect to ECU {ecu_id:02X}")
                    return None

            # Set protocol for ECU coding
            self.ecu_coding.set_protocol(ecu.protocol)

            # Read coding
            coding_data = self.ecu_coding.read_coding(ecu_id, coding_id)

            if coding_data:
                logger.info(f"Successfully read coding {coding_id:02X} from ECU {ecu_id:02X}")
            else:
                logger.error(f"Failed to read coding {coding_id:02X} from ECU {ecu_id:02X}")

            return coding_data
        except Exception as e:
            logger.error(f"Error reading ECU coding: {e}")
            return None

    def write_ecu_coding(self, ecu_id, coding_data):
        """
        Write ECU coding

        Args:
            ecu_id (int): The ECU ID
            coding_data (CodingData): The coding data

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.connected:
                logger.error("Not connected to vehicle")
                return False

            # Get the ECU
            ecu = self.ecu_manager.get_ecu(ecu_id)
            if not ecu:
                logger.error(f"ECU with ID {ecu_id:02X} not found")
                return False

            # Connect to the ECU if not already connected
            if not ecu.connected:
                if not self.ecu_manager.connect_ecu(ecu_id):
                    logger.error(f"Failed to connect to ECU {ecu_id:02X}")
                    return False

            # Set protocol for ECU coding
            self.ecu_coding.set_protocol(ecu.protocol)

            # Write coding
            success = self.ecu_coding.write_coding(coding_data)

            if success:
                logger.info(f"Successfully wrote coding {coding_data.coding_id:02X} to ECU {ecu_id:02X}")
            else:
                logger.error(f"Failed to write coding {coding_data.coding_id:02X} to ECU {ecu_id:02X}")

            return success
        except Exception as e:
            logger.error(f"Error writing ECU coding: {e}")
            return False

    def read_adaptation_channel(self, ecu_id, channel_id):
        """
        Read adaptation channel

        Args:
            ecu_id (int): The ECU ID
            channel_id (int): The channel ID

        Returns:
            AdaptationChannel: The adaptation channel or None if failed
        """
        try:
            if not self.connected:
                logger.error("Not connected to vehicle")
                return None

            # Get the ECU
            ecu = self.ecu_manager.get_ecu(ecu_id)
            if not ecu:
                logger.error(f"ECU with ID {ecu_id:02X} not found")
                return None

            # Connect to the ECU if not already connected
            if not ecu.connected:
                if not self.ecu_manager.connect_ecu(ecu_id):
                    logger.error(f"Failed to connect to ECU {ecu_id:02X}")
                    return None

            # Set protocol for ECU coding
            self.ecu_coding.set_protocol(ecu.protocol)

            # Read adaptation channel
            channel = self.ecu_coding.read_adaptation_channel(ecu_id, channel_id)

            if channel:
                logger.info(f"Successfully read adaptation channel {channel_id:02X} from ECU {ecu_id:02X}")
            else:
                logger.error(f"Failed to read adaptation channel {channel_id:02X} from ECU {ecu_id:02X}")

            return channel
        except Exception as e:
            logger.error(f"Error reading adaptation channel: {e}")
            return None

    def write_adaptation_channel(self, ecu_id, channel):
        """
        Write adaptation channel

        Args:
            ecu_id (int): The ECU ID
            channel (AdaptationChannel): The adaptation channel

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.connected:
                logger.error("Not connected to vehicle")
                return False

            # Get the ECU
            ecu = self.ecu_manager.get_ecu(ecu_id)
            if not ecu:
                logger.error(f"ECU with ID {ecu_id:02X} not found")
                return False

            # Connect to the ECU if not already connected
            if not ecu.connected:
                if not self.ecu_manager.connect_ecu(ecu_id):
                    logger.error(f"Failed to connect to ECU {ecu_id:02X}")
                    return False

            # Set protocol for ECU coding
            self.ecu_coding.set_protocol(ecu.protocol)

            # Write adaptation channel
            success = self.ecu_coding.write_adaptation_channel(ecu_id, channel)

            if success:
                logger.info(f"Successfully wrote adaptation channel {channel.channel_id:02X} to ECU {ecu_id:02X}")
            else:
                logger.error(f"Failed to write adaptation channel {channel.channel_id:02X} to ECU {ecu_id:02X}")

            return success
        except Exception as e:
            logger.error(f"Error writing adaptation channel: {e}")
            return False

    def get_diagnostic_procedures(self):
        """
        Get a list of available diagnostic procedures

        Returns:
            list: List of diagnostic procedures
        """
        try:
            # Get all procedures
            procedures = []
            for procedure_id, procedure in self.automated_diagnostics.procedures.items():
                procedures.append(procedure.to_dict())

            return procedures
        except Exception as e:
            logger.error(f"Error getting diagnostic procedures: {e}")
            return []

    def load_diagnostic_procedures(self, file_path):
        """
        Load diagnostic procedures from file

        Args:
            file_path (str): Path to the file

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            return self.automated_diagnostics.load_procedures(file_path)
        except Exception as e:
            logger.error(f"Error loading diagnostic procedures: {e}")
            return False

    def save_diagnostic_procedures(self, file_path):
        """
        Save diagnostic procedures to file

        Args:
            file_path (str): Path to the file

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            return self.automated_diagnostics.save_procedures(file_path)
        except Exception as e:
            logger.error(f"Error saving diagnostic procedures: {e}")
            return False

    def start_diagnostic_procedure(self, procedure_id, step_callback=None, procedure_callback=None, user_input_callback=None):
        """
        Start a diagnostic procedure

        Args:
            procedure_id (str): The procedure ID
            step_callback: Callback function for step updates
            procedure_callback: Callback function for procedure updates
            user_input_callback: Callback function for user input

        Returns:
            bool: True if started, False otherwise
        """
        try:
            # Set callbacks
            if step_callback:
                self.automated_diagnostics.set_step_callback(step_callback)

            if procedure_callback:
                self.automated_diagnostics.set_procedure_callback(procedure_callback)

            if user_input_callback:
                self.automated_diagnostics.set_user_input_callback(user_input_callback)

            # Start procedure
            return self.automated_diagnostics.start_procedure(procedure_id)
        except Exception as e:
            logger.error(f"Error starting diagnostic procedure: {e}")
            return False

    def stop_diagnostic_procedure(self):
        """
        Stop the active diagnostic procedure

        Returns:
            bool: True if stopped, False otherwise
        """
        try:
            return self.automated_diagnostics.stop_procedure()
        except Exception as e:
            logger.error(f"Error stopping diagnostic procedure: {e}")
            return False

    def pause_diagnostic_procedure(self):
        """
        Pause the active diagnostic procedure

        Returns:
            bool: True if paused, False otherwise
        """
        try:
            return self.automated_diagnostics.pause_procedure()
        except Exception as e:
            logger.error(f"Error pausing diagnostic procedure: {e}")
            return False

    def resume_diagnostic_procedure(self):
        """
        Resume the active diagnostic procedure

        Returns:
            bool: True if resumed, False otherwise
        """
        try:
            return self.automated_diagnostics.resume_procedure()
        except Exception as e:
            logger.error(f"Error resuming diagnostic procedure: {e}")
            return False

    def get_active_procedure_status(self):
        """
        Get the status of the active diagnostic procedure

        Returns:
            dict: The procedure status or None if no active procedure
        """
        try:
            if not self.automated_diagnostics.active_procedure:
                return None

            return self.automated_diagnostics.active_procedure.to_dict()
        except Exception as e:
            logger.error(f"Error getting active procedure status: {e}")
            return None

    def get_freeze_frame_data(self, dtc_code=None):
        """
        Get freeze frame data

        Args:
            dtc_code (str): The DTC code (optional)

        Returns:
            dict: The freeze frame data
        """
        try:
            if not self.connected:
                logger.error("Not connected to vehicle")
                return {}

            # This would normally read freeze frame data from the vehicle
            # For now, we'll just return dummy data
            freeze_frame_data = {
                "ENGINE_LOAD": {
                    "value": 45.0,
                    "unit": "%",
                    "description": "Calculated engine load",
                    "min": 0,
                    "max": 100
                },
                "COOLANT_TEMP": {
                    "value": 90.0,
                    "unit": "°C",
                    "description": "Engine coolant temperature",
                    "min": -40,
                    "max": 215
                },
                "SHORT_FUEL_TRIM_1": {
                    "value": 2.5,
                    "unit": "%",
                    "description": "Short term fuel trim - Bank 1",
                    "min": -100,
                    "max": 100
                },
                "LONG_FUEL_TRIM_1": {
                    "value": 1.2,
                    "unit": "%",
                    "description": "Long term fuel trim - Bank 1",
                    "min": -100,
                    "max": 100
                },
                "INTAKE_PRESSURE": {
                    "value": 95.0,
                    "unit": "kPa",
                    "description": "Intake manifold pressure",
                    "min": 0,
                    "max": 255
                },
                "RPM": {
                    "value": 1250.0,
                    "unit": "rpm",
                    "description": "Engine RPM",
                    "min": 0,
                    "max": 16383.75
                },
                "SPEED": {
                    "value": 0.0,
                    "unit": "km/h",
                    "description": "Vehicle speed",
                    "min": 0,
                    "max": 255
                },
                "TIMING_ADVANCE": {
                    "value": 12.5,
                    "unit": "°",
                    "description": "Timing advance",
                    "min": -64,
                    "max": 63.5
                },
                "INTAKE_TEMP": {
                    "value": 25.0,
                    "unit": "°C",
                    "description": "Intake air temperature",
                    "min": -40,
                    "max": 215
                },
                "MAF": {
                    "value": 15.2,
                    "unit": "g/s",
                    "description": "MAF air flow rate",
                    "min": 0,
                    "max": 655.35
                },
                "THROTTLE_POS": {
                    "value": 20.0,
                    "unit": "%",
                    "description": "Throttle position",
                    "min": 0,
                    "max": 100
                }
            }

            return freeze_frame_data
        except Exception as e:
            logger.error(f"Error getting freeze frame data: {e}")
            return {}

    def get_dtc_info(self, code):
        """
        Get information about a DTC code

        Args:
            code (str): The DTC code

        Returns:
            dict: Information about the DTC code
        """
        try:
            # Get DTC information from database
            dtc_info = self.dtc_db.get_dtc_info(code)

            if not dtc_info:
                logger.warning(f"DTC code {code} not found in database")
                return None

            return dtc_info
        except Exception as e:
            logger.error(f"Error getting DTC information: {e}")
            return None

    def update_dtc_database(self, scrape_all=False):
        """
        Update the DTC database with data from web sources

        Args:
            scrape_all (bool): Whether to scrape all sources (default: False)

        Returns:
            int: Number of codes added/updated
        """
        try:
            # Update DTC database
            count = self.dtc_db.update_dtc_database_from_web(scrape_all)

            logger.info(f"Added/updated {count} DTC codes")
            return count
        except Exception as e:
            logger.error(f"Error updating DTC database: {e}")
            return 0

    def import_dtc_codes(self, file_path):
        """
        Import DTC codes from a file

        Args:
            file_path (str): Path to the file

        Returns:
            int: Number of codes added/updated
        """
        try:
            # Import DTC codes
            count = self.dtc_db.import_dtc_codes_from_file(file_path)

            logger.info(f"Imported {count} DTC codes from {file_path}")
            return count
        except Exception as e:
            logger.error(f"Error importing DTC codes: {e}")
            return 0

    def export_dtc_codes(self, file_path):
        """
        Export DTC codes to a file

        Args:
            file_path (str): Path to the file

        Returns:
            int: Number of codes exported
        """
        try:
            # Export DTC codes
            count = self.dtc_db.export_dtc_codes_to_file(file_path)

            logger.info(f"Exported {count} DTC codes to {file_path}")
            return count
        except Exception as e:
            logger.error(f"Error exporting DTC codes: {e}")
            return 0

    def get_dtc_statistics(self):
        """
        Get statistics about the DTC database

        Returns:
            dict: Statistics about the DTC database
        """
        try:
            # Get DTC statistics
            stats = self.dtc_db.get_dtc_statistics()

            return stats
        except Exception as e:
            logger.error(f"Error getting DTC statistics: {e}")
            return {}

    def import_vehicle_data(self, file_path=None):
        """
        Import vehicle data from JSON files

        Args:
            file_path (str, optional): Path to a specific JSON file

        Returns:
            int: Number of vehicles imported
        """
        try:
            # Import vehicle data
            from database.import_vehicle_data import import_vehicle_data, import_vehicle_data_from_file

            if file_path:
                count = import_vehicle_data_from_file("database/vehicles.db", file_path)
            else:
                count = import_vehicle_data()

            logger.info(f"Imported {count} vehicles")
            return count
        except Exception as e:
            logger.error(f"Error importing vehicle data: {e}")
            return 0

    def scrape_vehicle_data(self, make, model=None, start_year=None, end_year=None):
        """
        Scrape vehicle data from online sources

        Args:
            make (str): The vehicle make
            model (str, optional): The vehicle model
            start_year (int, optional): The start year
            end_year (int, optional): The end year

        Returns:
            int: Number of vehicles imported
        """
        try:
            # Scrape vehicle data
            from database.import_vehicle_data import scrape_vehicle_data

            year_range = None
            if start_year and end_year:
                year_range = (start_year, end_year)

            count = scrape_vehicle_data("database/vehicles.db", make, model, year_range)

            logger.info(f"Scraped and imported {count} vehicles")
            return count
        except Exception as e:
            logger.error(f"Error scraping vehicle data: {e}")
            return 0
