#!/usr/bin/env python3
"""
Enhanced DTC Code Import Script
This script imports comprehensive DTC code data from JSON files and adds it to the database.
"""

import os
import json
import sys
import argparse
import logging
import re

# Add parent directory to path to import database modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from dtc_db import DTCDatabase, DTCCategory, DTCCode, DTCManufacturer, DTCSystem

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("dtc_import.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("dtc_import")

def import_dtc_data(db, file_path, manufacturer=None):
    """
    Import DTC data from a JSON file
    
    Args:
        db (DTCDatabase): The DTC database
        file_path (str): Path to the JSON file
        manufacturer (str, optional): The manufacturer name
        
    Returns:
        int: Number of codes imported
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        session = db.Session()
        codes_imported = 0
        
        # Process each code in the file
        for code_data in data:
            print(f"Processing code: {code_data.get('code', 'Unknown')}")
            logger.info(f"Processing code: {code_data.get('code', 'Unknown')}")
            
            # Find the category
            category_prefix = code_data.get('category_prefix')
            if not category_prefix:
                # Try to determine category prefix from code
                code = code_data.get('code', '')
                if code.startswith('P'):
                    category_prefix = 'P0' if re.match(r'P0[0-9A-F]{3}', code) else 'P1'
                elif code.startswith('B'):
                    category_prefix = 'B0' if re.match(r'B0[0-9A-F]{3}', code) else 'B1'
                elif code.startswith('C'):
                    category_prefix = 'C0' if re.match(r'C0[0-9A-F]{3}', code) else 'C1'
                elif code.startswith('U'):
                    category_prefix = 'U0' if re.match(r'U0[0-9A-F]{3}', code) else 'U1'
            
            # If manufacturer is provided, try to find manufacturer-specific category
            if manufacturer and category_prefix:
                if category_prefix == 'P1':
                    # Check for manufacturer-specific powertrain category
                    man_prefix = None
                    if manufacturer.lower() == 'toyota' or manufacturer.lower() == 'lexus':
                        man_prefix = 'P1T'
                    elif manufacturer.lower() == 'honda' or manufacturer.lower() == 'acura':
                        man_prefix = 'P1H'
                    elif manufacturer.lower() == 'ford' or manufacturer.lower() == 'lincoln':
                        man_prefix = 'P1F'
                    elif manufacturer.lower() == 'bmw' or manufacturer.lower() == 'mini':
                        man_prefix = 'P1B'
                    elif manufacturer.lower() == 'mercedes-benz':
                        man_prefix = 'P1M'
                    
                    if man_prefix:
                        category_prefix = man_prefix
            
            category = session.query(DTCCategory).filter(
                DTCCategory.code_prefix == category_prefix
            ).first()
            
            if not category:
                logger.warning(f"Category not found for prefix: {category_prefix}")
                continue
            
            # Check if code already exists
            existing_code = session.query(DTCCode).filter(
                DTCCode.code == code_data.get('code')
            ).first()
            
            if existing_code:
                # Update existing code with new information
                existing_code.description = code_data.get('description', existing_code.description)
                existing_code.possible_causes = code_data.get('possible_causes', existing_code.possible_causes)
                existing_code.solutions = code_data.get('solutions', existing_code.solutions)
                existing_code.severity = code_data.get('severity', existing_code.severity)
                
                # Enhanced information
                existing_code.symptoms = code_data.get('symptoms', existing_code.symptoms)
                existing_code.testing_procedures = code_data.get('testing_procedures', existing_code.testing_procedures)
                existing_code.component_location = code_data.get('component_location', existing_code.component_location)
                existing_code.wiring_diagram = code_data.get('wiring_diagram', existing_code.wiring_diagram)
                existing_code.repair_difficulty = code_data.get('repair_difficulty', existing_code.repair_difficulty)
                existing_code.repair_cost = code_data.get('repair_cost', existing_code.repair_cost)
                existing_code.repair_time = code_data.get('repair_time', existing_code.repair_time)
                existing_code.tools_required = code_data.get('tools_required', existing_code.tools_required)
                existing_code.related_codes = code_data.get('related_codes', existing_code.related_codes)
                existing_code.applicable_models = code_data.get('applicable_models', existing_code.applicable_models)
                existing_code.tsb_references = code_data.get('tsb_references', existing_code.tsb_references)
                
                # Manufacturer-specific information
                existing_code.manufacturer = manufacturer or code_data.get('manufacturer', existing_code.manufacturer)
                existing_code.model_years = code_data.get('model_years', existing_code.model_years)
                existing_code.system = code_data.get('system', existing_code.system)
                existing_code.subsystem = code_data.get('subsystem', existing_code.subsystem)
                
                # Additional resources
                existing_code.video_url = code_data.get('video_url', existing_code.video_url)
                existing_code.image_url = code_data.get('image_url', existing_code.image_url)
                
                logger.info(f"Updated existing code: {existing_code.code}")
            else:
                # Create new code
                code = DTCCode(
                    code=code_data.get('code'),
                    description=code_data.get('description', ''),
                    possible_causes=code_data.get('possible_causes', ''),
                    solutions=code_data.get('solutions', ''),
                    severity=code_data.get('severity', 3),
                    category_id=category.id,
                    
                    # Enhanced information
                    symptoms=code_data.get('symptoms', ''),
                    testing_procedures=code_data.get('testing_procedures', ''),
                    component_location=code_data.get('component_location', ''),
                    wiring_diagram=code_data.get('wiring_diagram', ''),
                    repair_difficulty=code_data.get('repair_difficulty', 3),
                    repair_cost=code_data.get('repair_cost', ''),
                    repair_time=code_data.get('repair_time', ''),
                    tools_required=code_data.get('tools_required', ''),
                    related_codes=code_data.get('related_codes', ''),
                    applicable_models=code_data.get('applicable_models', ''),
                    tsb_references=code_data.get('tsb_references', ''),
                    
                    # Manufacturer-specific information
                    manufacturer=manufacturer or code_data.get('manufacturer', ''),
                    model_years=code_data.get('model_years', ''),
                    system=code_data.get('system', ''),
                    subsystem=code_data.get('subsystem', ''),
                    
                    # Additional resources
                    video_url=code_data.get('video_url', ''),
                    image_url=code_data.get('image_url', '')
                )
                session.add(code)
                codes_imported += 1
                logger.info(f"Added new code: {code.code}")
        
        session.commit()
        session.close()
        
        logger.info(f"Import complete: {codes_imported} codes imported")
        return codes_imported
    
    except Exception as e:
        logger.error(f"Error importing DTC data from {file_path}: {e}")
        if 'session' in locals():
            session.rollback()
            session.close()
        return 0

def import_directory(db, directory, manufacturer=None):
    """
    Import all JSON files in a directory
    
    Args:
        db (DTCDatabase): The DTC database
        directory (str): Path to the directory
        manufacturer (str, optional): The manufacturer name
        
    Returns:
        int: Number of codes imported
    """
    total_codes = 0
    
    if not os.path.exists(directory):
        logger.error(f"Directory not found: {directory}")
        return 0
    
    # Get all JSON files in the directory
    json_files = [f for f in os.listdir(directory) if f.endswith('.json')]
    
    if not json_files:
        logger.warning(f"No JSON files found in {directory}")
        return 0
    
    # Import each file
    for file_name in json_files:
        file_path = os.path.join(directory, file_name)
        logger.info(f"Importing {file_path}")
        
        # Determine manufacturer from file name if not provided
        file_manufacturer = manufacturer
        if not file_manufacturer:
            file_base = os.path.splitext(file_name)[0].lower()
            if 'toyota' in file_base:
                file_manufacturer = 'Toyota'
            elif 'lexus' in file_base:
                file_manufacturer = 'Lexus'
            elif 'honda' in file_base:
                file_manufacturer = 'Honda'
            elif 'acura' in file_base:
                file_manufacturer = 'Acura'
            elif 'ford' in file_base:
                file_manufacturer = 'Ford'
            elif 'lincoln' in file_base:
                file_manufacturer = 'Lincoln'
            elif 'bmw' in file_base:
                file_manufacturer = 'BMW'
            elif 'mini' in file_base:
                file_manufacturer = 'Mini'
            elif 'mercedes' in file_base:
                file_manufacturer = 'Mercedes-Benz'
            elif 'vw' in file_base or 'volkswagen' in file_base:
                file_manufacturer = 'Volkswagen'
            elif 'audi' in file_base:
                file_manufacturer = 'Audi'
        
        # Import the file
        codes = import_dtc_data(db, file_path, file_manufacturer)
        total_codes += codes
    
    return total_codes

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Import DTC data from JSON files')
    parser.add_argument('--all', action='store_true', help='Import all DTC data')
    parser.add_argument('--generic', action='store_true', help='Import generic DTC data')
    parser.add_argument('--toyota', action='store_true', help='Import Toyota DTC data')
    parser.add_argument('--honda', action='store_true', help='Import Honda DTC data')
    parser.add_argument('--ford', action='store_true', help='Import Ford DTC data')
    parser.add_argument('--bmw', action='store_true', help='Import BMW DTC data')
    parser.add_argument('--mercedes', action='store_true', help='Import Mercedes DTC data')
    parser.add_argument('--vw', action='store_true', help='Import Volkswagen DTC data')
    parser.add_argument('--file', type=str, help='Import a specific JSON file')
    parser.add_argument('--manufacturer', type=str, help='Specify manufacturer for the file')
    
    args = parser.parse_args()
    
    # Initialize database
    db = DTCDatabase()
    
    # Get the base directory
    base_dir = os.path.dirname(os.path.abspath(__file__))
    
    total_codes = 0
    
    # Import specific file if requested
    if args.file:
        file_path = args.file if os.path.isabs(args.file) else os.path.join(base_dir, args.file)
        if os.path.exists(file_path):
            codes = import_dtc_data(db, file_path, args.manufacturer)
            total_codes += codes
        else:
            logger.error(f"File not found: {file_path}")
    
    # Import generic DTC data
    if args.all or args.generic:
        generic_dir = os.path.join(base_dir, 'generic')
        if os.path.exists(generic_dir):
            codes = import_directory(db, generic_dir)
            total_codes += codes
        else:
            logger.warning(f"Generic directory not found: {generic_dir}")
    
    # Import Toyota DTC data
    if args.all or args.toyota:
        toyota_dir = os.path.join(base_dir, 'toyota')
        if os.path.exists(toyota_dir):
            codes = import_directory(db, toyota_dir, 'Toyota')
            total_codes += codes
        else:
            logger.warning(f"Toyota directory not found: {toyota_dir}")
    
    # Import Honda DTC data
    if args.all or args.honda:
        honda_dir = os.path.join(base_dir, 'honda')
        if os.path.exists(honda_dir):
            codes = import_directory(db, honda_dir, 'Honda')
            total_codes += codes
        else:
            logger.warning(f"Honda directory not found: {honda_dir}")
    
    # Import Ford DTC data
    if args.all or args.ford:
        ford_dir = os.path.join(base_dir, 'ford')
        if os.path.exists(ford_dir):
            codes = import_directory(db, ford_dir, 'Ford')
            total_codes += codes
        else:
            logger.warning(f"Ford directory not found: {ford_dir}")
    
    # Import BMW DTC data
    if args.all or args.bmw:
        bmw_dir = os.path.join(base_dir, 'bmw')
        if os.path.exists(bmw_dir):
            codes = import_directory(db, bmw_dir, 'BMW')
            total_codes += codes
        else:
            logger.warning(f"BMW directory not found: {bmw_dir}")
    
    # Import Mercedes DTC data
    if args.all or args.mercedes:
        mercedes_dir = os.path.join(base_dir, 'mercedes')
        if os.path.exists(mercedes_dir):
            codes = import_directory(db, mercedes_dir, 'Mercedes-Benz')
            total_codes += codes
        else:
            logger.warning(f"Mercedes directory not found: {mercedes_dir}")
    
    # Import Volkswagen DTC data
    if args.all or args.vw:
        vw_dir = os.path.join(base_dir, 'vw')
        if os.path.exists(vw_dir):
            codes = import_directory(db, vw_dir, 'Volkswagen')
            total_codes += codes
        else:
            logger.warning(f"Volkswagen directory not found: {vw_dir}")
    
    # Print summary
    if total_codes > 0:
        logger.info(f"Total: {total_codes} codes imported")
    else:
        if not any([args.all, args.generic, args.toyota, args.honda, args.ford, args.bmw, args.mercedes, args.vw, args.file]):
            parser.print_help()
        else:
            logger.warning("No codes imported. Check if the JSON files exist.")

if __name__ == "__main__":
    main()
