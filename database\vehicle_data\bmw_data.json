{"BMW": {"3 Series": [{"year": 2020, "engine_type": "Inline-4", "engine_displacement": 2.0, "fuel_type": "Gasoline", "transmission_type": "Automatic", "num_cylinders": 4, "obd_protocol": "ISO15765-4 (CAN 11/500)", "manufacturer_protocol": "BMW UDS", "generation": "G20", "variant": "330i", "body_style": "Sedan", "drive_type": "RWD", "obd_port_location": "Under dashboard, driver's side", "ecu_location": "Engine bay, passenger side", "security_level": 3, "special_notes": "Requires BMW-specific security access for programming", "vin_pattern": "WBA", "engine_code": "B48", "ecus": [{"name": "Digital Motor Electronics (DME)", "code": "MSD85", "location": "Engine bay, passenger side", "protocol": "UDS over CAN", "address": "0x760", "security_type": "Seed-Key", "flash_method": "CAN", "special_notes": "Requires BMW-specific security access"}, {"name": "Transmission Control Module (TCM)", "code": "GS8P45HL", "location": "Transmission housing", "protocol": "UDS over CAN", "address": "0x761", "security_type": "Seed-Key", "flash_method": "CAN", "special_notes": "Requires BMW-specific security access"}, {"name": "Body Domain Controller (BDC)", "code": "BDC_01", "location": "Under dashboard, driver's side", "protocol": "UDS over CAN", "address": "0x762", "security_type": "Seed-Key", "flash_method": "CAN", "special_notes": "Requires BMW-specific security access"}]}, {"year": 2020, "engine_type": "Inline-6", "engine_displacement": 3.0, "fuel_type": "Gasoline", "transmission_type": "Automatic", "num_cylinders": 6, "obd_protocol": "ISO15765-4 (CAN 11/500)", "manufacturer_protocol": "BMW UDS", "generation": "G20", "variant": "M340i", "body_style": "Sedan", "drive_type": "AWD", "obd_port_location": "Under dashboard, driver's side", "ecu_location": "Engine bay, passenger side", "security_level": 3, "special_notes": "Requires BMW-specific security access for programming", "vin_pattern": "WBA", "engine_code": "B58", "ecus": [{"name": "Digital Motor Electronics (DME)", "code": "MSD87", "location": "Engine bay, passenger side", "protocol": "UDS over CAN", "address": "0x760", "security_type": "Seed-Key", "flash_method": "CAN", "special_notes": "Requires BMW-specific security access"}, {"name": "Transmission Control Module (TCM)", "code": "GS8P45HL", "location": "Transmission housing", "protocol": "UDS over CAN", "address": "0x761", "security_type": "Seed-Key", "flash_method": "CAN", "special_notes": "Requires BMW-specific security access"}, {"name": "Body Domain Controller (BDC)", "code": "BDC_01", "location": "Under dashboard, driver's side", "protocol": "UDS over CAN", "address": "0x762", "security_type": "Seed-Key", "flash_method": "CAN", "special_notes": "Requires BMW-specific security access"}, {"name": "Digital Chassis Controller (DCC)", "code": "DCC_01", "location": "Under trunk floor", "protocol": "UDS over CAN", "address": "0x763", "security_type": "Seed-Key", "flash_method": "CAN", "special_notes": "Requires BMW-specific security access"}]}], "5 Series": [{"year": 2020, "engine_type": "Inline-4", "engine_displacement": 2.0, "fuel_type": "Gasoline", "transmission_type": "Automatic", "num_cylinders": 4, "obd_protocol": "ISO15765-4 (CAN 11/500)", "manufacturer_protocol": "BMW UDS", "generation": "G30", "variant": "530i", "body_style": "Sedan", "drive_type": "RWD", "obd_port_location": "Under dashboard, driver's side", "ecu_location": "Engine bay, passenger side", "security_level": 3, "special_notes": "Requires BMW-specific security access for programming", "vin_pattern": "WBA", "engine_code": "B48", "ecus": [{"name": "Digital Motor Electronics (DME)", "code": "MSD85", "location": "Engine bay, passenger side", "protocol": "UDS over CAN", "address": "0x760", "security_type": "Seed-Key", "flash_method": "CAN", "special_notes": "Requires BMW-specific security access"}, {"name": "Transmission Control Module (TCM)", "code": "GS8P45HL", "location": "Transmission housing", "protocol": "UDS over CAN", "address": "0x761", "security_type": "Seed-Key", "flash_method": "CAN", "special_notes": "Requires BMW-specific security access"}, {"name": "Body Domain Controller (BDC)", "code": "BDC_01", "location": "Under dashboard, driver's side", "protocol": "UDS over CAN", "address": "0x762", "security_type": "Seed-Key", "flash_method": "CAN", "special_notes": "Requires BMW-specific security access"}]}, {"year": 2020, "engine_type": "Inline-6", "engine_displacement": 3.0, "fuel_type": "Gasoline", "transmission_type": "Automatic", "num_cylinders": 6, "obd_protocol": "ISO15765-4 (CAN 11/500)", "manufacturer_protocol": "BMW UDS", "generation": "G30", "variant": "540i", "body_style": "Sedan", "drive_type": "AWD", "obd_port_location": "Under dashboard, driver's side", "ecu_location": "Engine bay, passenger side", "security_level": 3, "special_notes": "Requires BMW-specific security access for programming", "vin_pattern": "WBA", "engine_code": "B58", "ecus": [{"name": "Digital Motor Electronics (DME)", "code": "MSD87", "location": "Engine bay, passenger side", "protocol": "UDS over CAN", "address": "0x760", "security_type": "Seed-Key", "flash_method": "CAN", "special_notes": "Requires BMW-specific security access"}, {"name": "Transmission Control Module (TCM)", "code": "GS8P45HL", "location": "Transmission housing", "protocol": "UDS over CAN", "address": "0x761", "security_type": "Seed-Key", "flash_method": "CAN", "special_notes": "Requires BMW-specific security access"}, {"name": "Body Domain Controller (BDC)", "code": "BDC_01", "location": "Under dashboard, driver's side", "protocol": "UDS over CAN", "address": "0x762", "security_type": "Seed-Key", "flash_method": "CAN", "special_notes": "Requires BMW-specific security access"}, {"name": "Digital Chassis Controller (DCC)", "code": "DCC_01", "location": "Under trunk floor", "protocol": "UDS over CAN", "address": "0x763", "security_type": "Seed-Key", "flash_method": "CAN", "special_notes": "Requires BMW-specific security access"}]}], "X5": [{"year": 2020, "engine_type": "Inline-6", "engine_displacement": 3.0, "fuel_type": "Gasoline", "transmission_type": "Automatic", "num_cylinders": 6, "obd_protocol": "ISO15765-4 (CAN 11/500)", "manufacturer_protocol": "BMW UDS", "generation": "G05", "variant": "xDrive40i", "body_style": "SUV", "drive_type": "AWD", "obd_port_location": "Under dashboard, driver's side", "ecu_location": "Engine bay, passenger side", "security_level": 3, "special_notes": "Requires BMW-specific security access for programming", "vin_pattern": "5UX", "engine_code": "B58", "ecus": [{"name": "Digital Motor Electronics (DME)", "code": "MSD87", "location": "Engine bay, passenger side", "protocol": "UDS over CAN", "address": "0x760", "security_type": "Seed-Key", "flash_method": "CAN", "special_notes": "Requires BMW-specific security access"}, {"name": "Transmission Control Module (TCM)", "code": "GS8P45HL", "location": "Transmission housing", "protocol": "UDS over CAN", "address": "0x761", "security_type": "Seed-Key", "flash_method": "CAN", "special_notes": "Requires BMW-specific security access"}, {"name": "Body Domain Controller (BDC)", "code": "BDC_01", "location": "Under dashboard, driver's side", "protocol": "UDS over CAN", "address": "0x762", "security_type": "Seed-Key", "flash_method": "CAN", "special_notes": "Requires BMW-specific security access"}, {"name": "Digital Chassis Controller (DCC)", "code": "DCC_01", "location": "Under trunk floor", "protocol": "UDS over CAN", "address": "0x763", "security_type": "Seed-Key", "flash_method": "CAN", "special_notes": "Requires BMW-specific security access"}]}, {"year": 2020, "engine_type": "V8", "engine_displacement": 4.4, "fuel_type": "Gasoline", "transmission_type": "Automatic", "num_cylinders": 8, "obd_protocol": "ISO15765-4 (CAN 11/500)", "manufacturer_protocol": "BMW UDS", "generation": "G05", "variant": "M50i", "body_style": "SUV", "drive_type": "AWD", "obd_port_location": "Under dashboard, driver's side", "ecu_location": "Engine bay, passenger side", "security_level": 3, "special_notes": "Requires BMW-specific security access for programming", "vin_pattern": "5UX", "engine_code": "N63", "ecus": [{"name": "Digital Motor Electronics (DME)", "code": "MSD87", "location": "Engine bay, passenger side", "protocol": "UDS over CAN", "address": "0x760", "security_type": "Seed-Key", "flash_method": "CAN", "special_notes": "Requires BMW-specific security access"}, {"name": "Transmission Control Module (TCM)", "code": "GS8P45HL", "location": "Transmission housing", "protocol": "UDS over CAN", "address": "0x761", "security_type": "Seed-Key", "flash_method": "CAN", "special_notes": "Requires BMW-specific security access"}, {"name": "Body Domain Controller (BDC)", "code": "BDC_01", "location": "Under dashboard, driver's side", "protocol": "UDS over CAN", "address": "0x762", "security_type": "Seed-Key", "flash_method": "CAN", "special_notes": "Requires BMW-specific security access"}, {"name": "Digital Chassis Controller (DCC)", "code": "DCC_01", "location": "Under trunk floor", "protocol": "UDS over CAN", "address": "0x763", "security_type": "Seed-Key", "flash_method": "CAN", "special_notes": "Requires BMW-specific security access"}]}]}}